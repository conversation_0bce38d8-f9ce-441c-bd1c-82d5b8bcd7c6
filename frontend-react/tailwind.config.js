/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      screens: {
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },
      colors: {
        primary: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
        coral: {
          50: '#fff5f5',
          100: '#ffe9e9',
          200: '#ffd8d8',
          300: '#ffb8b8',
          400: '#ff8e8e',
          500: '#ff3b3b',
          600: '#ed1515',
          700: '#c80d0d',
          800: '#a50f0f',
          900: '#881414',
          950: '#500a0a',
        },
      },
      container: {
        center: true,
        padding: {
          DEFAULT: '1rem',
          sm: '2rem',
          lg: '3rem',
          xl: '4rem',
          '2xl': '5rem',
        },
      },
      keyframes: {
        borderFlow: {
          '0%': { 
            backgroundPosition: '0% 0%'
          },
          '100%': { 
            backgroundPosition: '200% 0%'
          }
        },
        shimmer: {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' }
        },
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' }
        },
        pulse: {
          '0%, 100%': { opacity: 1 },
          '50%': { opacity: 0.5 }
        },
        bounce: {
          '0%, 100%': { 
            transform: 'translateY(0)',
            animationTimingFunction: 'cubic-bezier(0.8, 0, 1, 1)'
          },
          '50%': { 
            transform: 'translateY(-25%)',
            animationTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)'
          }
        },
        spin: {
          'from': { transform: 'rotate(0deg)' },
          'to': { transform: 'rotate(360deg)' }
        },
        ping: {
          '75%, 100%': {
            transform: 'scale(2)',
            opacity: '0'
          }
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        slideInFromRight: {
          '0%': { transform: 'translateX(100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' }
        },
        slideInFromLeft: {
          '0%': { transform: 'translateX(-100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' }
        },
        wave: {
          '0%': { transform: 'rotate(0.0deg)' },
          '10%': { transform: 'rotate(14.0deg)' },
          '20%': { transform: 'rotate(-8.0deg)' },
          '30%': { transform: 'rotate(14.0deg)' },
          '40%': { transform: 'rotate(-4.0deg)' },
          '50%': { transform: 'rotate(10.0deg)' },
          '60%': { transform: 'rotate(0.0deg)' },
          '100%': { transform: 'rotate(0.0deg)' }
        },
        blink: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.2' }
        }
      },
      animation: {
        'border-flow': 'borderFlow 2s linear infinite',
        'shimmer': 'shimmer 2.5s linear infinite',
        'float': 'float 3s ease-in-out infinite',
        'pulse': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce': 'bounce 1s infinite',
        'spin': 'spin 1s linear infinite',
        'ping': 'ping 1s cubic-bezier(0, 0, 0.2, 1) infinite',
        'fade-in': 'fadeIn 0.5s ease-out',
        'slide-in-right': 'slideInFromRight 0.5s ease-out',
        'slide-in-left': 'slideInFromLeft 0.5s ease-out',
        'wave': 'wave 1.5s infinite',
        'blink': 'blink 1.5s infinite'
      },
      backgroundImage: {
        'border-gradient': 'linear-gradient(90deg, transparent, #ff3b3b, transparent, transparent)',
        'flowing-gradient': 'linear-gradient(90deg, transparent 0%, rgba(255,59,59,0.4) 50%, transparent 100%)',
        'radial-gradient': 'radial-gradient(circle, rgba(255,59,59,0.2) 0%, rgba(255,255,255,0) 70%)',
        'hero-pattern': 'url("/img/hero-pattern.svg")',
        'footer-texture': 'url("/img/footer-texture.png")',
        'coral-gradient': 'linear-gradient(to right, #ff3b3b, #ff8e8e)',
        'coral-gradient-vertical': 'linear-gradient(to bottom, #ff3b3b, #ff8e8e)',
      },
      boxShadow: {
        'coral-sm': '0 1px 2px 0 rgba(255, 59, 59, 0.05)',
        'coral': '0 4px 6px -1px rgba(255, 59, 59, 0.1), 0 2px 4px -1px rgba(255, 59, 59, 0.06)',
        'coral-md': '0 6px 16px -1px rgba(255, 59, 59, 0.1), 0 2px 4px -1px rgba(255, 59, 59, 0.06)',
        'coral-lg': '0 20px 25px -5px rgba(255, 59, 59, 0.1), 0 10px 10px -5px rgba(255, 59, 59, 0.04)',
        'coral-xl': '0 25px 50px -12px rgba(255, 59, 59, 0.25)',
        'coral-inner': 'inset 0 2px 4px 0 rgba(255, 59, 59, 0.06)'
      },
      blur: {
        'xs': '2px',
      },
      borderRadius: {
        '4xl': '2rem',
        '5xl': '2.5rem',
      },
      transitionDuration: {
        '400': '400ms',
        '2000': '2000ms',
      },
      transitionTimingFunction: {
        'bounce-start': 'cubic-bezier(0.8, 0, 1, 1)',
        'bounce-end': 'cubic-bezier(0, 0, 0.2, 1)',
      },
      typography: {
        DEFAULT: {
          css: {
            a: {
              color: '#ff3b3b',
              '&:hover': {
                color: '#ed1515',
              },
            },
          },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ],
}
