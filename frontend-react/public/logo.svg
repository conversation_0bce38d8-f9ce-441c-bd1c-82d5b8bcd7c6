<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="128" height="128" viewBox="0 0 128 128">
  <!-- 定义渐变 -->
  <defs>
    <linearGradient id="purplePinkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#8B5CF6" />
      <stop offset="100%" stop-color="#EC4899" />
    </linearGradient>
    
    <!-- 字母B的光晕效果 -->
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="4" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>
  
  <!-- 紫色/粉色渐变背景 -->
  <circle cx="64" cy="64" r="60" fill="url(#purplePinkGradient)" />
  
  <!-- 装饰元素 - 小圆点 -->
  <circle cx="34" cy="44" r="3" fill="#FFFFFF" opacity="0.7" />
  <circle cx="94" cy="84" r="2" fill="#FFFFFF" opacity="0.5" />
  
  <!-- 完全居中且圆润的字母B -->
  <path d="M64,34
           C76,34 84,42 84,52
           C84,59 80,64 75,66
           C82,68 86,74 86,82
           C86,94 76,100 64,100
           L47,100
           C45.5,100 44,98.5 44,97
           L44,37
           C44,35.5 45.5,34 47,34
           L64,34 Z
           
           M62,62
           C67,62 70,59 70,54
           C70,49 67,46 62,46
           L58,46
           L58,62
           L62,62 Z
           
           M64,88
           C69,88 72,85 72,80
           C72,75 69,71 64,71
           L58,71
           L58,88
           L64,88 Z"
        fill="#FFFFFF" 
        filter="url(#glow)" />
  
  <!-- 光泽效果 -->
  <circle cx="64" cy="64" r="24" fill="white" opacity="0.1" />
</svg>