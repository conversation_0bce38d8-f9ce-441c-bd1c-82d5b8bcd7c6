<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600" fill="none">
  <!-- Modern dark purple background -->
  <rect width="1200" height="600" fill="#1E1033"/>
  
  <!-- Abstract shapes with purple gradient -->
  <path d="M0 0L1200 0L900 600L0 600L0 0Z" fill="url(#shape_gradient_1)" opacity="0.5"/>
  <path d="M800 0L1200 0L1000 600L600 600L800 0Z" fill="url(#shape_gradient_2)" opacity="0.4"/>
  
  <!-- Subtle grid pattern -->
  <path d="M0 50H1200M0 150H1200M0 250H1200M0 350H1200M0 450H1200M0 550H1200" stroke="#FFFFFF" stroke-opacity="0.03" stroke-width="1"/>
  <path d="M50 0V600M150 0V600M250 0V600M350 0V600M450 0V600M550 0V600M650 0V600M750 0V600M850 0V600M950 0V600M1050 0V600M1150 0V600" stroke="#FFFFFF" stroke-opacity="0.03" stroke-width="1"/>
  
  <!-- Cashback Illustration - Shopping Bag with Money -->
  <g transform="translate(800, 300)">
    <!-- Shopping Bag -->
    <rect x="-100" y="-150" width="200" height="220" rx="20" fill="#7E22CE" filter="drop-shadow(0px 10px 30px rgba(0, 0, 0, 0.25))"/>
    <rect x="-100" y="-150" width="200" height="220" rx="20" fill="url(#bag_gradient)" opacity="0.5"/>
    
    <!-- Bag Handles -->
    <path d="M-60 -150C-60 -180 -30 -190 0 -190C30 -190 60 -180 60 -150" stroke="#A855F7" stroke-width="10" stroke-linecap="round"/>
    
    <!-- Dollar Sign -->
    <circle cx="0" cy="-50" r="40" fill="#FFFFFF"/>
    <path d="M0 -70V-30M-15 -60H15M-15 -40H15" stroke="#7E22CE" stroke-width="6" stroke-linecap="round"/>
    
    <!-- Coins coming out of bag -->
    <circle cx="-50" cy="20" r="25" fill="url(#coin_gradient_1)" filter="drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.2))"/>
    <path d="M-50 10V30M-60 20H-40" stroke="#FFFFFF" stroke-width="3" stroke-linecap="round"/>
    
    <circle cx="50" cy="30" r="20" fill="url(#coin_gradient_2)" filter="drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.2))"/>
    <path d="M50 20V40M40 30H60" stroke="#FFFFFF" stroke-width="3" stroke-linecap="round"/>
    
    <circle cx="0" cy="60" r="30" fill="url(#coin_gradient_3)" filter="drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.2))"/>
    <path d="M0 45V75M-15 60H15" stroke="#FFFFFF" stroke-width="3" stroke-linecap="round"/>
  </g>
  
  <!-- Percentage Symbol - Representing Cashback -->
  <g transform="translate(600, 200)">
    <circle cx="0" cy="0" r="40" fill="url(#percent_gradient)" filter="drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.2))"/>
    <path d="M-15 -15L15 15M-15 15L15 -15" stroke="#FFFFFF" stroke-width="6" stroke-linecap="round"/>
  </g>
  
  <!-- Store Icon - Representing Shopping -->
  <g transform="translate(950, 150)">
    <rect x="-40" y="-40" width="80" height="80" rx="10" fill="#A855F7" opacity="0.8" filter="drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.2))"/>
    <path d="M-25 -10H25M-25 0H25M-25 10H25" stroke="#FFFFFF" stroke-width="4" stroke-linecap="round"/>
    <path d="M-15 -25V25M15 -25V25" stroke="#FFFFFF" stroke-width="4" stroke-linecap="round"/>
  </g>
  
  <!-- Credit Card - Representing Payments -->
  <g transform="translate(700, 400)">
    <rect x="-50" y="-30" width="100" height="60" rx="8" fill="#9333EA" filter="drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.2))"/>
    <rect x="-40" y="-5" width="80" height="10" rx="2" fill="#FFFFFF" opacity="0.3"/>
    <rect x="-40" y="15" width="30" height="5" rx="2" fill="#FFFFFF" opacity="0.3"/>
    <rect x="-40" y="-20" width="20" height="10" rx="2" fill="#FFD700" opacity="0.8"/>
  </g>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="shape_gradient_1" x1="0" y1="0" x2="1200" y2="600" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#7E22CE" stop-opacity="0.3"/>
      <stop offset="1" stop-color="#7E22CE" stop-opacity="0.1"/>
    </linearGradient>
    
    <linearGradient id="shape_gradient_2" x1="800" y1="0" x2="1000" y2="600" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#A855F7" stop-opacity="0.3"/>
      <stop offset="1" stop-color="#A855F7" stop-opacity="0.1"/>
    </linearGradient>
    
    <linearGradient id="bag_gradient" x1="-100" y1="-150" x2="100" y2="70" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#A855F7"/>
      <stop offset="1" stop-color="#7E22CE"/>
    </linearGradient>
    
    <linearGradient id="coin_gradient_1" x1="-75" y1="-5" x2="-25" y2="45" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F59E0B"/>
      <stop offset="1" stop-color="#D97706"/>
    </linearGradient>
    
    <linearGradient id="coin_gradient_2" x1="30" y1="10" x2="70" y2="50" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F59E0B"/>
      <stop offset="1" stop-color="#D97706"/>
    </linearGradient>
    
    <linearGradient id="coin_gradient_3" x1="-30" y1="30" x2="30" y2="90" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F59E0B"/>
      <stop offset="1" stop-color="#D97706"/>
    </linearGradient>
    
    <linearGradient id="percent_gradient" x1="-40" y1="-40" x2="40" y2="40" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#EC4899"/>
      <stop offset="1" stop-color="#BE185D"/>
    </linearGradient>
  </defs>
</svg>
