<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600" fill="none">
  <!-- Modern dark purple background -->
  <rect width="1200" height="600" fill="#1E1033"/>
  
  <!-- Abstract shapes with purple gradient -->
  <path d="M0 0L1200 0L900 600L0 600L0 0Z" fill="url(#shape_gradient_1)" opacity="0.5"/>
  <path d="M800 0L1200 0L1000 600L600 600L800 0Z" fill="url(#shape_gradient_2)" opacity="0.4"/>
  
  <!-- Subtle grid pattern -->
  <path d="M0 50H1200M0 150H1200M0 250H1200M0 350H1200M0 450H1200M0 550H1200" stroke="#FFFFFF" stroke-opacity="0.03" stroke-width="1"/>
  <path d="M50 0V600M150 0V600M250 0V600M350 0V600M450 0V600M550 0V600M650 0V600M750 0V600M850 0V600M950 0V600M1050 0V600M1150 0V600" stroke="#FFFFFF" stroke-opacity="0.03" stroke-width="1"/>
  
  <!-- Professional credit card illustration -->
  <g transform="translate(750, 200)">
    <!-- Card -->
    <rect x="0" y="0" width="300" height="180" rx="12" fill="url(#card_gradient)" filter="drop-shadow(0px 10px 30px rgba(0, 0, 0, 0.25))"/>
    
    <!-- Card details -->
    <rect x="30" y="100" width="240" height="20" rx="2" fill="#FFFFFF" opacity="0.15"/>
    <rect x="30" y="130" width="60" height="20" rx="2" fill="#FFFFFF" opacity="0.15"/>
    <rect x="100" y="130" width="60" height="20" rx="2" fill="#FFFFFF" opacity="0.15"/>
    
    <!-- Card chip -->
    <rect x="30" y="40" width="40" height="30" rx="4" fill="#FFD700" opacity="0.8"/>
    
    <!-- Cashback symbol -->
    <circle cx="250" cy="40" r="25" fill="#FFFFFF" opacity="0.9"/>
    <path d="M250 25V55M240 35H260" stroke="#7E22CE" stroke-width="4" stroke-linecap="round"/>
    <path d="M240 45H260" stroke="#7E22CE" stroke-width="4" stroke-linecap="round"/>
  </g>
  
  <!-- Floating coins -->
  <circle cx="650" cy="150" r="20" fill="url(#coin_gradient_1)" filter="drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.2))"/>
  <circle cx="700" cy="250" r="15" fill="url(#coin_gradient_2)" filter="drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.2))"/>
  <circle cx="600" cy="300" r="18" fill="url(#coin_gradient_3)" filter="drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.2))"/>
  
  <!-- Dollar signs on coins -->
  <path d="M650 145V155M645 150H655" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round"/>
  <path d="M700 245V255M695 250H705" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round"/>
  <path d="M600 295V305M595 300H605" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round"/>
  
  <!-- Percentage symbol for cashback -->
  <g transform="translate(550, 200)">
    <circle cx="0" cy="0" r="30" fill="url(#percent_gradient)" filter="drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.2))"/>
    <path d="M-10 -10L10 10M-10 10L10 -10" stroke="#FFFFFF" stroke-width="4" stroke-linecap="round"/>
  </g>
  
  <!-- Abstract decorative elements -->
  <circle cx="900" cy="100" r="50" fill="url(#circle_gradient)" opacity="0.1"/>
  <circle cx="200" cy="400" r="80" fill="url(#circle_gradient)" opacity="0.05"/>
  <circle cx="1100" cy="500" r="100" fill="url(#circle_gradient)" opacity="0.05"/>
  
  <!-- Subtle wave pattern -->
  <path d="M0,400 C200,450 400,350 600,400 C800,450 1000,350 1200,400 L1200,600 L0,600 Z" fill="url(#wave_gradient)" opacity="0.1"/>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="shape_gradient_1" x1="0" y1="0" x2="1200" y2="600" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#7E22CE" stop-opacity="0.3"/>
      <stop offset="1" stop-color="#7E22CE" stop-opacity="0.1"/>
    </linearGradient>
    
    <linearGradient id="shape_gradient_2" x1="800" y1="0" x2="1000" y2="600" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#A855F7" stop-opacity="0.3"/>
      <stop offset="1" stop-color="#A855F7" stop-opacity="0.1"/>
    </linearGradient>
    
    <linearGradient id="card_gradient" x1="0" y1="0" x2="300" y2="180" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#7E22CE"/>
      <stop offset="1" stop-color="#A855F7"/>
    </linearGradient>
    
    <linearGradient id="coin_gradient_1" x1="630" y1="130" x2="670" y2="170" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F59E0B"/>
      <stop offset="1" stop-color="#D97706"/>
    </linearGradient>
    
    <linearGradient id="coin_gradient_2" x1="685" y1="235" x2="715" y2="265" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F59E0B"/>
      <stop offset="1" stop-color="#D97706"/>
    </linearGradient>
    
    <linearGradient id="coin_gradient_3" x1="582" y1="282" x2="618" y2="318" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F59E0B"/>
      <stop offset="1" stop-color="#D97706"/>
    </linearGradient>
    
    <linearGradient id="percent_gradient" x1="-30" y1="-30" x2="30" y2="30" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#EC4899"/>
      <stop offset="1" stop-color="#BE185D"/>
    </linearGradient>
    
    <linearGradient id="circle_gradient" x1="0%" y1="0%" x2="100%" y2="100%" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#FFFFFF" stop-opacity="0.2"/>
      <stop offset="100%" stop-color="#FFFFFF" stop-opacity="0"/>
    </linearGradient>
    
    <linearGradient id="wave_gradient" x1="0" y1="400" x2="0" y2="600" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#7E22CE"/>
      <stop offset="1" stop-color="#7E22CE" stop-opacity="0"/>
    </linearGradient>
  </defs>
</svg>
