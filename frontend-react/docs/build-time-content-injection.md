# Build-Time Content Injection

## Overview

This project now supports **build-time injection** of `headContent` and `bodyContentList` directly into the static `index.html` file during the production build process. This ensures that external platform validation tools can see the required content without needing to execute JavaScript.

## How It Works

### Development Mode
- Content is injected dynamically via React components (`HeadInjector` and `BodyInjector`)
- Allows for hot reloading and development flexibility

### Production Mode
- Content is injected directly into `index.html` during the build process
- React components skip injection to avoid duplication
- Results in a static HTML file with all required content

## Implementation Details

### Custom Vite Plugin
A custom Vite plugin (`htmlContentInjectionPlugin`) handles the build-time injection:

1. **Reads Configuration**: Parses `src/config/index.ts` to extract `headContent` and `bodyContentList`
2. **Extracts String Literals**: Uses a sophisticated parser to handle various quote types and escape sequences
3. **Injects Content**: Adds content to the appropriate sections of `index.html`
4. **Preserves Formatting**: Maintains exact content as specified in config (no modifications)

### Content Extraction
The plugin handles:
- Single quotes (`'...'`)
- Double quotes (`"..."`)
- Template literals (`` `...` ``)
- Escaped characters (`\n`, `\t`, `\"`, etc.)
- Multi-line strings
- Comments in the config file

### React Component Behavior
- **Development**: Components inject content dynamically
- **Production**: Components detect production mode and skip injection

## Configuration

Content is configured in `src/config/index.ts`:

```typescript
export const config = {
  headContent: [
    '<link rel="preconnect" href="https://fonts.googleapis.com">',
    '<meta name="apple-mobile-web-app-capable" content="yes">',
    // ... more head content
  ],
  
  bodyContentList: [
    `<script type='text/javascript'>
    // Your tracking code here
    </script>`,
    // ... more body content
  ],
}
```

## Build Process

### Production Build
```bash
npm run build:live
```

This will:
1. Compile TypeScript
2. Run Vite build with the custom plugin
3. Inject content into `dist/index.html`
4. Generate optimized assets

### Result
The final `dist/index.html` will contain:
- All `headContent` items injected before `</head>`
- All `bodyContentList` items injected before `</body>`
- Content exactly as specified (no data attributes or modifications)

## Validation Benefits

- **External Platform Compatibility**: Content is present in static HTML for validation
- **SEO Friendly**: Search engines can see all content immediately
- **Performance**: No JavaScript execution required for content visibility
- **Reliability**: Content is guaranteed to be present regardless of JavaScript execution

## Troubleshooting

### Content Not Appearing
1. Check that content is properly quoted in `src/config/index.ts`
2. Verify the build is using production mode (`build:live`)
3. Check browser console for any plugin warnings

### Malformed Content
1. Ensure proper escaping of quotes within strings
2. Verify template literals are properly closed
3. Check for syntax errors in the config file

## Technical Notes

- Plugin only runs during production builds (not dev server)
- React components automatically detect production mode
- Content is injected exactly as specified (preserving external platform validation requirements)
- Plugin handles complex string parsing including nested quotes and escape sequences
