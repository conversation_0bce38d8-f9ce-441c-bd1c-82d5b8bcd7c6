# Body Content Injection 功能说明

## 概述

Body Content Injection 功能允许您在前端应用的每个页面的 `</body>` 标签前动态注入自定义 HTML 内容。这个功能类似于现有的 `headContent` 功能，但专门用于在页面底部添加内容。

## 配置方式

在 `frontend-react/src/config/index.ts` 文件中，您可以通过 `bodyContentList` 数组来配置要注入的内容：

```typescript
export const config = {
  // ... 其他配置

  // Body tag custom content array
  // Each item in this array will be injected before the closing </body> tag of every page
  // This allows for adding custom scripts, tracking codes, widgets, etc.
  bodyContentList: [
    // 示例：第三方跟踪脚本
    '<script>console.log("Custom body script loaded");</script>',

    // 示例：聊天小部件或客户支持
    '<script src="https://example.com/chat-widget.js"></script>',

    // 示例：分析或转换跟踪
    '<script>/* Your tracking code here */</script>',

    // 示例：自定义 HTML 元素
    '<div id="custom-footer-widget" style="display:none;">Custom content</div>',
  ],
}
```

## 使用场景

1. **第三方跟踪代码**：Google Analytics、Facebook Pixel、其他分析工具
2. **聊天小部件**：客户支持聊天工具
3. **转换跟踪**：广告平台的转换跟踪代码
4. **自定义脚本**：任何需要在页面底部执行的 JavaScript
5. **隐藏元素**：需要在页面底部添加的隐藏 HTML 元素

## 技术实现

- **组件**：`BodyInjector.tsx` 负责将配置的内容注入到 DOM 中
- **位置**：内容会被添加到 `document.body` 的末尾，在 `</body>` 标签前
- **原样注入**：内容完全按照配置中的原样注入，不添加任何额外属性，确保与外部平台验证兼容
- **错误处理**：包含错误处理机制，确保单个项目的错误不会影响其他项目
- **清理**：组件卸载时会自动清理注入的内容

## 注意事项

1. **安全性**：确保注入的内容来自可信源，避免 XSS 攻击
2. **性能**：避免注入过多或过大的脚本，可能影响页面性能
3. **格式**：每个数组项应该是有效的 HTML 字符串
4. **顺序**：数组中的项目会按顺序注入到页面中
5. **外部平台验证**：内容会完全按照配置原样注入，不会添加任何额外属性，确保通过第三方平台的验证检查

## 测试

当前配置中包含一个测试脚本，您可以在浏览器控制台中看到：
```
✅ BodyInjector: Custom body script loaded successfully!
```

这表明功能正常工作。

## 示例用法

### 添加 Google Analytics
```typescript
bodyContentList: [
  '<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>',
  '<script>window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}gtag("js", new Date());gtag("config", "GA_MEASUREMENT_ID");</script>',
]
```

### 添加聊天小部件
```typescript
bodyContentList: [
  '<script>window.chatSettings = {apiKey: "your-api-key"};</script>',
  '<script src="https://widget.chat-service.com/widget.js"></script>',
]
```

### 添加自定义跟踪
```typescript
bodyContentList: [
  '<script>console.log("Page loaded:", window.location.href);</script>',
  '<div id="tracking-pixel"><img src="https://analytics.example.com/pixel.gif" style="display:none;"></div>',
]
```
