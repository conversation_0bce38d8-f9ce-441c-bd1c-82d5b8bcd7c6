{"name": "frontend-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:live": "vite --mode live", "build": "tsc && vite build", "build:live": "tsc && vite build --mode live", "build:development": "tsc && vite build --mode development", "build:production": "tsc && vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.0.0", "@heroicons/react": "^2.2.0", "@react-three/drei": "^10.0.6", "@react-three/fiber": "^9.1.2", "@react-three/postprocessing": "^3.0.4", "axios": "^1.7.9", "dayjs": "^1.11.13", "framer-motion": "^11.15.0", "gsap": "^3.12.7", "prismjs": "^1.30.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^9.1.0", "react-router-dom": "^6.21.1", "react-simple-captcha": "^9.3.1", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^11.0.2", "rehype-autolink-headings": "^7.1.0", "rehype-code-titles": "^1.2.0", "rehype-highlight": "^7.0.2", "rehype-prism-plus": "^2.0.0", "rehype-raw": "^7.0.0", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.1", "swiper": "^11.0.5", "three": "^0.175.0", "web-vitals": "^4.2.4", "yup": "^1.6.1"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.25.9", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/node": "^20.10.5", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "cssnano": "^7.0.6", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.4.17", "terser": "^5.27.0", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-pwa": "^0.17.5"}}