<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Bonus Earned - Your Ultimate Cashback and Rewards Destination. Shop smarter and earn more with exclusive cashback offers from your favorite stores." />
    <meta name="theme-color" content="#8B5CF6" />
    <meta property="og:title" content="Bonus Earned - Shop Smart, Earn More" />
    <meta property="og:description" content="Get rewarded for your online shopping with the best cashback offers from your favorite stores." />
    <meta property="og:type" content="website" />
    <title>Bonus Earned - Shop Smart, Earn More | Best Cashback and Rewards Site</title>
    
    <!-- Resource hints -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
    <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
    
    <!-- Critical CSS -->
    <style>
      :root {
        --coral-500: #8B5CF6;
        --coral-600: #7C3AED;
        --coral-700: #6D28D9;
      }
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f9fafb;
      }
      #root {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
      }
      /* Page load animation */
      .page-load-fade-in {
        animation: fadeIn 0.5s ease-in;
      }
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
    </style>
  </head>
  <body>
    <div id="root" class="page-load-fade-in"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
