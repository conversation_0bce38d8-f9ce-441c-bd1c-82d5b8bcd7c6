import api from '@/lib/api';

export interface Coupon {
  id: number;
  merchant_id: number;
  code: string;
  commission_rate: string;
  title: string;
  description: string;
  type: string;
  started_at: string;
  ended_at?: string;
  created_at: string;
  merchant_name: string;
  merchant_logo: string;
  merchant_unique_name: string;
}

export interface CouponResponse {
  total: number;
  page: number;
  page_size: number;
  coupon_list: Coupon[];
}

export const couponApi = {
  getMerchantCoupons: async (merchantId: number) => {
    return api.get<Coupon[]>(`/coupons/merchant/${merchantId}`);
  },

  searchCoupons: async (query: string = '', params: { page?: number; page_size?: number; featured?: boolean } = {}) => {
    const usercode = localStorage.getItem('usercode')
    const headers: Record<string, string> = {}

    if (usercode) {
      headers['X-User-Code'] = usercode
    }

    const queryParams = new URLSearchParams({
      search: query,
      page: String(params.page || 1),
      page_size: String(params.page_size || 30)
    });

    // Add featured parameter if it exists
    if (params.featured) {
      queryParams.append('featured', 'true');
    }

    const response = await api.get<CouponResponse>(`/coupons?${queryParams.toString()}`, { headers });
    return response.coupon_list;
  }
};
