:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

@tailwind base;
@tailwind components;
@tailwind utilities;

.logo-error::before {
  content: "Logo";
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f9f9f9;
}

/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 5px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #f97316;
  border-radius: 10px;
  transition: background 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #e86304;
}

/* Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #f97316 #f1f5f9;
}

/* 添加鼠标滑动动画效果 */
.category-item-enter {
  opacity: 0;
  transform: translateX(-20px);
}

.category-item-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 300ms, transform 300ms;
}

.category-item-exit {
  opacity: 1;
  transform: translateX(0);
}

.category-item-exit-active {
  opacity: 0;
  transform: translateX(20px);
  transition: opacity 300ms, transform 300ms;
}

/* 改进鼠标悬停效果 */
.category-hover {
  position: relative;
  overflow: hidden;
}

.category-hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(249, 115, 22, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.category-hover:hover::after {
  transform: translateX(0);
}

/* 动画效果 */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* 边框动画 */
@keyframes border-flow {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-border-flow {
  background-size: 200% 100%;
  animation: border-flow 2s linear infinite;
}

/* 缓慢弹跳动画 */
@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(-10%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-bounce-slow {
  animation: bounce-slow 3s infinite;
}

/* 3D旋转动画 */
.perspective-1000 {
  perspective: 1000px;
}

/* Markdown 样式增强 */

/* 自定义代码高亮样式 */
.rehype-code-title {
  background-color: #f8f8f8;
  color: #ff7f50;
  font-family: monospace;
  font-weight: bold;
  padding: 0.75rem 1.25rem;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  border-bottom: none;
  font-size: 0.875rem;
}

/* 代码块容器 */
pre {
  margin-top: 0 !important;
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
  font-size: 0.875rem !important;
  padding: 1.25rem !important;
  background-color: #f8f8f8 !important;
  overflow-x: auto !important;
  border: 1px solid #e5e7eb !important;
}

/* 没有标题的代码块 */
pre:not(.rehype-code-title + pre) {
  border-radius: 0.5rem !important;
}

.rehype-code-title + pre {
  margin-top: 0 !important;
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

/* 行号样式 */
.line-number {
  display: inline-block;
  width: 2em;
  color: #a0aec0;
  text-align: right;
  padding-right: 1em;
  user-select: none;
  opacity: 0.5;
}

/* 代码高亮颜色 */
code[class*="language-"],
pre[class*="language-"] {
  color: #333;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  line-height: 1.5;
  tab-size: 4;
  hyphens: none;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #8292a2;
}

.token.punctuation {
  color: #444;
}

.token.property,
.token.tag,
.token.constant,
.token.symbol,
.token.deleted {
  color: #f92672;
}

.token.boolean,
.token.number {
  color: #ae81ff;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #a6e22e;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
  color: #ff7f50;
}

.token.atrule,
.token.attr-value,
.token.function,
.token.class-name {
  color: #e6db74;
}

.token.keyword {
  color: #66d9ef;
}

.token.regex,
.token.important {
  color: #fd971f;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

/* 代码块内部滚动条样式 */
pre::-webkit-scrollbar {
  height: 6px;
}

pre::-webkit-scrollbar-track {
  background: #f1f1f1;
}

pre::-webkit-scrollbar-thumb {
  background: #ff7f50;
  border-radius: 3px;
}

pre::-webkit-scrollbar-thumb:hover {
  background: #ff6347;
}

/* 文章内容增强样式 */
.prose {
  scroll-margin-top: 64px;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  scroll-margin-top: 64px;
  position: relative;
}

/* 头部链接标记样式 */
.anchor-link {
  position: relative;
  text-decoration: none !important;
  color: inherit !important;
}

.anchor-link::before {
  content: '#';
  position: absolute;
  left: -1em;
  color: #FF7F50;
  opacity: 0;
  transition: opacity 0.2s ease;
  font-weight: normal;
}

.anchor-link:hover::before {
  opacity: 1;
}

/* 目录导航增强 */
.toc {
  position: relative;
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 0.5rem;
  scrollbar-width: thin;
  scrollbar-color: #FF7F50 #f1f1f1;
}

.toc::-webkit-scrollbar {
  width: 4px;
}

.toc::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.toc::-webkit-scrollbar-thumb {
  background: #FF7F50;
  border-radius: 10px;
}

.toc a {
  display: block;
  position: relative;
  padding-left: 0.5rem;
  border-left: 2px solid transparent;
  transition: all 0.2s ease;
  line-height: 1.4;
  color: #6b7280;
}

.toc a[href^="#h2-"] {
  padding-left: 1rem;
}

.toc a[href^="#h3-"] {
  padding-left: 1.5rem;
}

.toc a[href^="#h4-"] {
  padding-left: 2rem;
}

.toc a:hover {
  color: #FF7F50;
  border-left-color: #FFB8A5;
  background-color: rgba(255, 127, 80, 0.05);
}

.toc a.active {
  color: #FF7F50;
  font-weight: 500;
  border-left-color: #FF7F50;
  background-color: rgba(255, 127, 80, 0.1);
}

/* 代码块包装器样式 */
.code-block-wrapper {
  position: relative;
}

.code-block-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 2px;
  background: linear-gradient(to bottom, #FF7F50, transparent);
}

/* Custom animations */
@keyframes border-flow {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-border-flow {
  background-size: 200% 100%;
  animation: border-flow 2s linear infinite;
}

/* Add will-change for better performance on animations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Force hardware acceleration for critical animations */
.hardware-accelerate {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transform: translateZ(0);
  perspective: 1000px;
  will-change: transform;
}

/* Optimize transitions */
.transition-gpu {
  transition-property: transform, opacity;
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* Animation utilities */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.5);
  }
}

.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 着陆页专用样式 */
.bg-pattern {
  background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 增强的按钮悬停效果 */
@keyframes soft-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.btn-pulse:hover {
  animation: soft-pulse 2s ease-in-out infinite;
}

/* 进一步优化的视差效果类 */
.parallax-slow {
  transition: transform 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.parallax-medium {
  transition: transform 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.parallax-fast {
  transition: transform 0.2s cubic-bezier(0.2, 0.8, 0.2, 1);
}

/* 增强着陆页样式 */
.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* 高级背景效果 */
.bg-mesh {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 3D卡片悬停效果 */
.card-3d {
  transition: transform 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.preserve-3d {
  transform-style: preserve-3d;
}

.perspective-1200 {
  perspective: 1200px;
}

/* 流动边框动画 */
.flowing-border {
  position: relative;
  overflow: hidden;
}

.flowing-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  width: calc(100% + 4px);
  height: calc(100% + 4px);
  background: linear-gradient(45deg,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.8) 50%,
    rgba(255,255,255,0) 100%);
  z-index: -1;
  animation: flow 3s linear infinite;
}

@keyframes flow {
  0% {
    transform: translateX(-100%) translateY(-100%);
  }
  100% {
    transform: translateX(100%) translateY(100%);
  }
}

/* 按钮脉冲效果 */
.btn-pulse {
  position: relative;
}

.btn-pulse::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 9999px;
  box-shadow: 0 0 0 0 rgba(249, 115, 22, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(249, 115, 22, 0.4);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 15px rgba(249, 115, 22, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(249, 115, 22, 0);
  }
}

/* 按钮光晕效果 */
.btn-glow {
  box-shadow: 0 0 10px rgba(249, 115, 22, 0.5);
  transition: box-shadow 0.3s ease;
}

.btn-glow:hover {
  box-shadow: 0 0 20px rgba(249, 115, 22, 0.8);
}

/* 修复Hero部分的背景滚动问题 */
.bg-gradient-to-br.from-coral-500.via-coral-600.to-coral-700 {
  background-attachment: fixed;
}

/* 防止在滚动时变灰色 */
.fixed-coral-bg {
  position: relative;
  z-index: 1;
}

.fixed-coral-bg::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f97316, #ea580c, #c2410c);
  z-index: -1;
}

/* 新增固定背景类 */
.background-fixed {
  background-attachment: fixed !important;
  will-change: transform;
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* 添加视差背景效果 */
@keyframes slow-drift {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

.parallax-bg {
  animation: slow-drift 30s ease infinite alternate;
  background-size: 120% 120%;
}

/* 动画光束效果 */
@keyframes light-sweep {
  0% {
    transform: translateY(100%) rotate(45deg);
  }
  100% {
    transform: translateY(-100%) rotate(45deg);
  }
}

.animate-light-sweep {
  animation: light-sweep 3s ease-in-out infinite;
}

/* 波浪底部效果 */
.wave-bottom {
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
}

.wave-bottom svg {
  position: relative;
  display: block;
  width: 100%;
  height: 50px;
}

/* 商店卡片样式 */
.store-card {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
}

.store-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}

.store-card::before {
  content: '';
  position: absolute;
  top: -5px;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, #FF7F50, #FFB347);
  transition: transform 0.3s ease;
  transform: translateY(-100%);
}

.store-card:hover::before {
  transform: translateY(0);
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Orbit animation for Travel section */
@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes spin-slow-reverse {
  from { transform: rotate(0deg); }
  to { transform: rotate(-360deg); }
}

.animate-spin-slow {
  animation: spin-slow 30s linear infinite;
}

.animate-spin-slow-reverse {
  animation: spin-slow-reverse 20s linear infinite;
}
