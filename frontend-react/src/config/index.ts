export const config = {
  apiUrl: import.meta.env.VITE_API_URL,
  env: import.meta.env.VITE_ENV,
  isLocal: import.meta.env.VITE_ENV === 'local',
  isLive: import.meta.env.VITE_ENV === 'live',

  // Head tag custom content array
  // Each item in this array will be injected into the <head> of every page
  // This allows for adding custom meta tags, scripts, stylesheets, etc.
  headContent: [
    // Google Fonts example
    '<link rel="preconnect" href="https://fonts.googleapis.com">',
    '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>',
    '<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">',

    // Meta tags example
    '<meta name="apple-mobile-web-app-capable" content="yes">',
    '<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">',

    // Canonical URL example (base URL - should be updated per page ideally)
    '<link rel="canonical" href="https://www.bonusearned.com/">',

    // Favicon example
    '<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">',

    // Custom CSS example
    '<style>.rewards-highlight{color:#8B5CF6;font-weight:bold;}</style>',

    // Uncomment the following for Google Analytics or other tracking scripts
    '<script async src="https://www.googletagmanager.com/gtag/js?id=G-V3MFWSYVZY"></script>',
    '<script>window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}gtag("js", new Date());gtag("config", "G-V3MFWSYVZY");</script>',
    '<script type="text/javascript" async src="https://js.trckprf.com/v1/js?api_key=41df4b33f24bcdc84c1b320893cb4ae8&site_id=b528d10c745f4d90a9a76b5d2ea6522d"></script>',
  ],

  // Body tag custom content array
  // Each item in this array will be injected before the closing </body> tag of every page
  // This allows for adding custom scripts, tracking codes, widgets, etc.
  bodyContentList: [
    // yieldkit script
    `<script type='text/javascript'>
(function () { 
var scriptProto = 'https:' == document.location.protocol ? 'https://' : 'http://'; 
var script = document.createElement('script');
script.type = 'text/javascript';
script.async = true;
script.src = scriptProto+'js.trckprf.com/v1/js?api_key=41df4b33f24bcdc84c1b320893cb4ae8&site_id=b528d10c745f4d90a9a76b5d2ea6522d';
(document.getElementsByTagName('head')[0] || document.body).appendChild(script); 
})();
</script>`,
  ],
} as const
