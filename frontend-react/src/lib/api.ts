// Ensure environment variables are correctly used even after refresh
const API_URL = import.meta.env.VITE_ENV === 'live'
  ? 'https://api.bonusearned.com'
  : import.meta.env.VITE_API_URL || 'http://localhost:8080'

interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

async function fetchApi<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const token = localStorage.getItem('token')
  const usercode = localStorage.getItem('usercode')
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...options.headers,
  }

  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }

  if (usercode) {
    headers['X-User-Code'] = usercode
  }

  const response = await fetch(`${API_URL}${endpoint}`, {
    ...options,
    headers,
  })

  let data: ApiResponse<T>;
  try {
    // First check response status
    if (!response.ok) {
      if (response.status === 0) {
        throw new Error('Network connection failed. Please check your internet connection');
      }
      const errorText = await response.text();
      throw new Error(
        `Request failed (${response.status}): ${errorText.substring(0, 100)}`
      );
    }

    // Try to parse JSON response
    data = await response.json();
  } catch (error) {
    if (error instanceof Error) {
      // If it's an already handled error, throw it directly
      if (error.message.includes('Network connection failed') || error.message.includes('Request failed')) {
        throw error;
      }
      // Handle JSON parsing error
      throw new Error(`Response format error: ${error.message}`);
    }
    // Handle other unknown errors
    throw new Error('An unknown error occurred during the request');
  }

  if (!response.ok) {
    // Only clear authentication info when confirmed as auth failure
    if (response.status === 401 && data.code === 401) {
      // Avoid clearing auth info in auth-related endpoints
      if (!endpoint.startsWith('/auth/')) {
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        localStorage.removeItem('usercode')
        // Only redirect if not on login page
        if (!window.location.pathname.includes('/login')) {
          window.location.href = '/login'
        }
      }
    }
    const error = new Error(data.message || `API Error: ${response.statusText}`)
    ;(error as any).status = response.status
    throw error
  }

  // Handle the standard response structure from backend
  if (data.code !== 200) {
    const error = new Error(data.message || 'Unknown error occurred')
    ;(error as any).status = data.code
    throw error
  }

  return data.data
}

const api = {
  setToken: (token: string) => {
    localStorage.setItem('token', token)
  },
  removeToken: () => {
    localStorage.removeItem('token')
  },
  get: <T>(endpoint: string, options: { params?: Record<string, any> } = {}) => {
    const url = new URL(`${API_URL}/api/v1${endpoint}`)
    if (options.params) {
      Object.entries(options.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value))
        }
      })
    }
    return fetchApi<T>(url.pathname + url.search, {
      method: 'GET',
      ...options,
    })
  },
  post: <T>(endpoint: string, data: any) => {
    const url = new URL(`${API_URL}/api/v1${endpoint}`)
    return fetchApi<T>(url.pathname + url.search, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },
  put: <T>(endpoint: string, data: any) => {
    const url = new URL(`${API_URL}/api/v1${endpoint}`)
    return fetchApi<T>(url.pathname + url.search, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  },
  delete: <T>(endpoint: string) => {
    const url = new URL(`${API_URL}/api/v1${endpoint}`)
    return fetchApi<T>(url.pathname + url.search, {
      method: 'DELETE',
    })
  },
}

export default api
