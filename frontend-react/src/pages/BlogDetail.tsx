import React, { useState, useEffect, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useLocation } from 'react-router-dom';
import { ArrowLeftIcon, ArrowUpIcon } from '@heroicons/react/20/solid';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import rehypeHighlight from 'rehype-highlight';
import rehypeSlug from 'rehype-slug';
import rehypeAutolinkHeadings from 'rehype-autolink-headings';
import remarkGfm from 'remark-gfm';
import rehypeCodeTitles from 'rehype-code-titles';
import rehypePrism from 'rehype-prism-plus';
import dayjs from 'dayjs';
import api from '../lib/api';

// 导入Prism依赖和样式
import Prism from 'prismjs';
// 确保在组件挂载后加载Prism
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-css';
import 'prismjs/components/prism-jsx';
import 'prismjs/components/prism-tsx';
import 'prismjs/components/prism-json';
import 'prismjs/components/prism-bash';
import 'prismjs/components/prism-markdown';
import 'prismjs/components/prism-yaml';
import 'prismjs/components/prism-python';
import 'prismjs/components/prism-go';
import 'prismjs/components/prism-sql';
import 'prismjs/components/prism-java';

interface Blog {
  id: number;
  title: string;
  content: string;
  description: string;
  featured_image: string;
  published_at: string;
  category: string;
  tags: string[];
  merchant_info?: {
    id: number;
    name: string;
    unique_name: string;
    merchant_code: string;
    logo: string;
    website: string;
    track_url: string;
    cashback_type: number;
    cashback_value: string;
    description: string;
    category: {
      id: number;
      name: string;
      icon: string;
    };
    featured: boolean;
    country: string;
    supported_countries: string[] | null;
    created_at: string;
    updated_at: string;
  };
}

interface BlogResponse {
  code: number;
  message: string;
  data: Blog;
}

interface TocItem {
  id: string;
  text: string;
  level: number;
}

const parseHeadings = (content: string): TocItem[] => {
  const headings = content.match(/^#{1,6}.+$/gm) || [];
  return headings.map(heading => {
    const level = (heading.match(/^#+/) || [''])[0].length;
    const text = heading.replace(/^#+\s*/, '');
    const id = text.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
    return { id, text, level };
  });
};

const BlogDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const [blog, setBlog] = useState<Blog | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [toc, setToc] = useState<TocItem[]>([]);
  const [activeHeading, setActiveHeading] = useState<string>('');
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [showMobileToc, setShowMobileToc] = useState(false);

  useEffect(() => {
    const fetchBlog = async () => {
      try {
        setLoading(true);
        const data = await api.get<BlogResponse['data']>(`/blogs/${id}`);
        setBlog(data);
        // 解析内容中的标题生成目录
        const headings = parseHeadings(data.content);
        setToc(headings);
      } catch (error) {
        console.error('Error fetching blog:', error);
        setError(error instanceof Error ? error.message : 'Failed to load blog');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchBlog();
    }
  }, [id]);

  useEffect(() => {
    if (blog?.content) {
      // Parse content to get TOC
      setToc(parseHeadings(blog.content));
    }
  }, [blog?.content]);

  useEffect(() => {
    const handleScroll = () => {
      const headings = document.querySelectorAll('h1[id], h2[id], h3[id], h4[id], h5[id], h6[id]');
      let activeId = '';
      
      // 找到当前视口中最靠近顶部的标题
      headings.forEach(heading => {
        const rect = heading.getBoundingClientRect();
        if (rect.top <= 100) {
          activeId = heading.id;
        }
      });
      
      setActiveHeading(activeId);
      
      // Show back to top button when scrolled down
      setShowBackToTop(window.scrollY > 500);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      const offset = 80; // 添加一些偏移，避免标题被固定导航栏遮挡
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - offset;
      
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // 在组件挂载后高亮所有代码块
  useEffect(() => {
    if (typeof window !== 'undefined') {
      Prism.highlightAll();
    }
  }, [blog]);

  // 自定义Markdown组件
  const MarkdownComponents = {
    // 图片组件增强，添加懒加载和全宽显示
    img: ({ node, alt, src, ...props }: any) => {
      const safeProps = Object.fromEntries(
        Object.entries(props).filter(([key]) => !key.startsWith('on'))
      );
      return (
        <div className="my-8 overflow-hidden rounded-lg shadow-md">
          <img
            src={src}
            alt={alt || ''}
            loading="lazy"
            className="w-full h-auto object-cover transition-transform duration-300 hover:scale-105"
            {...safeProps}
          />
          {alt && <p className="text-sm text-gray-500 text-center mt-2">{alt}</p>}
        </div>
      );
    },
    
    // 增强链接
    a: ({ node, children, href, ...props }: any) => {
      const safeProps = Object.fromEntries(
        Object.entries(props).filter(([key]) => !key.startsWith('on'))
      );
      
      const isExternal = href?.startsWith('http');
      
      return (
        <a 
          href={href} 
          {...safeProps} 
          className="text-primary hover:text-primary-dark underline decoration-primary/30 hover:decoration-primary/100 transition-all"
          {...(isExternal ? { target: '_blank', rel: 'noopener noreferrer' } : {})}
        >
          {children}
          {isExternal && (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4 inline-block ml-1">
              <path fillRule="evenodd" d="M4.25 5.5a.75.75 0 00-.75.75v8.5c0 .414.336.75.75.75h8.5a.75.75 0 00.75-.75v-4a.75.75 0 011.5 0v4A2.25 2.25 0 0112.75 17h-8.5A2.25 2.25 0 012 14.75v-8.5A2.25 2.25 0 014.25 4h5a.75.75 0 010 1.5h-5z" clipRule="evenodd" />
              <path fillRule="evenodd" d="M6.194 12.753a.75.75 0 001.06.053L16.5 4.44v2.81a.75.75 0 001.5 0v-4.5a.75.75 0 00-.75-.75h-4.5a.75.75 0 000 1.5h2.553l-9.056 8.194a.75.75 0 00-.053 1.06z" clipRule="evenodd" />
            </svg>
          )}
        </a>
      );
    },
    
    // 表格增强
    table: ({ node, children, ...props }: any) => {
      const safeProps = Object.fromEntries(
        Object.entries(props).filter(([key]) => !key.startsWith('on'))
      );
      return (
        <div className="my-8 overflow-x-auto rounded-lg border border-gray-200 shadow-md">
          <table className="w-full border-collapse" {...safeProps}>
            {children}
          </table>
        </div>
      );
    },
    
    // 表头增强
    thead: ({ node, children, ...props }: any) => {
      const safeProps = Object.fromEntries(
        Object.entries(props).filter(([key]) => !key.startsWith('on'))
      );
      return (
        <thead className="bg-gray-50 text-left" {...safeProps}>
          {children}
        </thead>
      );
    },
    
    // 表格行增强
    tr: ({ node, children, ...props }: any) => {
      const safeProps = Object.fromEntries(
        Object.entries(props).filter(([key]) => !key.startsWith('on'))
      );
      return (
        <tr 
          className="hover:bg-gray-50 border-b border-gray-200 transition-colors"
          {...safeProps}
        >
          {children}
        </tr>
      );
    },
    
    // 表头单元格增强
    th: ({ node, children, ...props }: any) => {
      const safeProps = Object.fromEntries(
        Object.entries(props).filter(([key]) => !key.startsWith('on'))
      );
      return (
        <th className="px-6 py-3 text-sm font-medium text-gray-700" {...safeProps}>
          {children}
        </th>
      );
    },
    
    // 表格单元格增强
    td: ({ node, children, ...props }: any) => {
      const safeProps = Object.fromEntries(
        Object.entries(props).filter(([key]) => !key.startsWith('on'))
      );
      return (
        <td className="px-6 py-4 text-sm text-gray-700" {...safeProps}>
          {children}
        </td>
      );
    },
    
    // 引用块增强
    blockquote: ({ node, children, ...props }: any) => {
      const safeProps = Object.fromEntries(
        Object.entries(props).filter(([key]) => !key.startsWith('on'))
      );
      return (
        <blockquote 
          className="pl-4 border-l-4 border-purple-500 bg-purple-50/50 py-2 px-4 my-6 rounded-r-lg"
          {...safeProps}
        >
          {children}
        </blockquote>
      );
    },
    
    // 代码块会由rehype-prism-plus处理
    code: ({ node, inline, className, children, ...props }: any) => {
      const match = /language-(\w+)/.exec(className || '');
      const safeProps = Object.fromEntries(
        Object.entries(props).filter(([key]) => !key.startsWith('on'))
      );
      
      if (inline) {
        return (
          <code className="font-mono bg-gray-100 px-1 py-0.5 rounded text-purple-700" {...safeProps}>
            {children}
          </code>
        );
      }
      
      // 确保代码块有适当的样式
      return (
        <code className={className} {...safeProps}>
          {children}
        </code>
      );
    },
    
    // 增强代码块容器
    pre: ({ node, children, ...props }: any) => {
      const safeProps = Object.fromEntries(
        Object.entries(props).filter(([key]) => !key.startsWith('on'))
      );
      
      // 添加阴影和圆角
      return (
        <div className="code-block-wrapper my-6 rounded-lg shadow-md overflow-hidden">
          <pre {...safeProps} className={`${props.className || ''}`}>
            {children}
          </pre>
        </div>
      );
    },
    
    // 分割线增强
    hr: ({ node, ...props }: any) => {
      const safeProps = Object.fromEntries(
        Object.entries(props).filter(([key]) => !key.startsWith('on'))
      );
      return (
        <hr className="my-10 border-t-2 border-gray-200 w-1/2 mx-auto" {...safeProps} />
      );
    },
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {loading ? (
        <div className="container mx-auto px-4 py-16">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-12"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded w-full"></div>
              <div className="h-4 bg-gray-200 rounded w-full"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      ) : error ? (
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-2xl mx-auto text-center">
            <div className="mb-8">
              <svg
                className="mx-auto h-16 w-16 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Blog Not Found</h2>
            <p className="text-gray-600 mb-8">
              Sorry, we couldn't find the blog post you're looking for. It might have been removed or doesn't exist.
            </p>
            <div className="flex justify-center space-x-4">
              <Link
                to="/blog"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 transition-colors"
              >
                <ArrowLeftIcon className="h-5 w-5 mr-2" />
                Back to Blog List
              </Link>
              <Link
                to="/stores"
                className="inline-flex items-center px-6 py-3 border-2 border-purple-500 text-purple-600 text-base font-medium rounded-lg hover:bg-purple-50 transition-colors"
              >
                Browse All Stores
              </Link>
            </div>
          </div>
        </div>
      ) : blog ? (
        <>
          {/* Hero Section with Featured Image */}
          <div className="relative w-full h-[40vh] min-h-[300px]">
            <div className="absolute inset-0">
              <img
                src={blog.featured_image}
                alt={blog.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-50" />
            </div>
            <div className="absolute inset-0 flex flex-col justify-center items-center text-center text-white p-4">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">{blog.title}</h1>
              <p className="text-lg mb-2">{dayjs(blog.published_at).format('MMMM D, YYYY')}</p>
              {blog.tags && blog.tags.length > 0 && (
                <div className="flex flex-wrap justify-center gap-2">
                  {blog.tags.map((tag) => (
                    <span
                      key={tag}
                      className="px-3 py-1 bg-white/10 rounded-full text-sm"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Three Column Layout */}
          <div className="max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12">
            <div className="grid grid-cols-1 md:grid-cols-8 lg:grid-cols-12 gap-6 lg:gap-10">
              {/* Left Sidebar - Table of Contents */}
              <div className="hidden md:block md:col-span-2 lg:col-span-3 order-1">
                <div className="sticky top-24">
                  <h2 className="text-lg font-semibold mb-4">Table of Contents</h2>
                  <nav className="toc">
                    {toc.map((item) => (
                      <a
                        key={item.id}
                        href={`#${item.id}`}
                        onClick={(e) => {
                          e.preventDefault();
                          const element = document.getElementById(item.id);
                          if (element) {
                            element.scrollIntoView({ behavior: 'smooth' });
                          }
                        }}
                        className={`block py-2 text-sm hover:text-primary cursor-pointer ${
                          activeHeading === item.id
                            ? 'text-primary font-medium'
                            : 'text-gray-600'
                        }`}
                      >
                        {item.text}
                      </a>
                    ))}
                  </nav>
                </div>
              </div>

              {/* Right Sidebar - Merchant Info - Shown first on mobile */}
              <div className="col-span-1 md:col-span-8 lg:col-span-3 order-first lg:order-3">
                {blog.merchant_info && blog.merchant_info.id > 0 && (
                  <div className="bg-white rounded-lg shadow-lg p-6 lg:sticky lg:top-24">
                    <div className="flex flex-col items-center text-center">
                      {blog.merchant_info.logo && (
                        <img
                          src={blog.merchant_info.logo}
                          alt={blog.merchant_info.name}
                          className="w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 object-contain mb-4"
                        />
                      )}
                      <h3 className="text-xl font-semibold mb-2">
                        {blog.merchant_info.name}
                      </h3>
                      {blog.merchant_info.cashback_value && (
                        <div className="mb-4">
                          <span className="text-2xl font-bold text-primary">
                            {blog.merchant_info.cashback_value}
                          </span>
                          <span className="text-gray-600 ml-2">Cashback</span>
                        </div>
                      )}
                      <a
                        href={blog.merchant_info.track_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200"
                      >
                        Shop Now
                      </a>
                    </div>
                  </div>
                )}
              </div>

              {/* Main Content */}
              <div className="col-span-1 md:col-span-6 lg:col-span-6 order-2">
                <article className="prose prose-lg max-w-none prose-headings:scroll-mt-24 prose-headings:font-semibold prose-a:text-purple-600 prose-a:decoration-purple-200 hover:prose-a:decoration-purple-500 prose-img:rounded-xl prose-img:shadow-md">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[
                      rehypeRaw,
                      rehypeSlug,
                      rehypeCodeTitles,
                      [rehypePrism, { showLineNumbers: true }],
                      [rehypeAutolinkHeadings, { 
                        behavior: 'wrap',
                        properties: {
                          className: ['anchor-link'],
                          title: '直接链接到这个标题'
                        }
                      }],
                    ]}
                    components={MarkdownComponents}
                  >
                    {blog.content}
                  </ReactMarkdown>
                </article>
              </div>
            </div>
          </div>

          {/* Mobile Table of Contents Button */}
          <div className="fixed bottom-20 right-8 md:hidden">
            <button
              onClick={() => setShowMobileToc(!showMobileToc)}
              className="bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary-dark transition-colors duration-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>

          {/* Mobile Table of Contents Modal */}
          {showMobileToc && (
            <div className="fixed inset-0 z-50 md:hidden">
              <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setShowMobileToc(false)} />
              <div className="absolute right-0 top-0 bottom-0 w-80 bg-white p-6 overflow-y-auto">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-lg font-semibold">Table of Contents</h2>
                  <button onClick={() => setShowMobileToc(false)} className="text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <nav className="toc">
                  {toc.map((item) => (
                    <a
                      key={item.id}
                      href={`#${item.id}`}
                      onClick={(e) => {
                        e.preventDefault();
                        const element = document.getElementById(item.id);
                        if (element) {
                          element.scrollIntoView({ behavior: 'smooth' });
                          setShowMobileToc(false);
                        }
                      }}
                      className={`block py-2 text-sm hover:text-primary cursor-pointer ${
                        activeHeading === item.id
                          ? 'text-primary font-medium'
                          : 'text-gray-600'
                      }`}
                    >
                      {item.text}
                    </a>
                  ))}
                </nav>
              </div>
            </div>
          )}

          {/* Back to Top Button */}
          <button
            onClick={scrollToTop}
            className={`fixed bottom-8 right-8 p-3 rounded-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white shadow-lg transition-all duration-300 ${
              showBackToTop ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            } hover:scale-110`}
          >
            <ArrowUpIcon className="w-6 h-6" />
          </button>
        </>
      ) : (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Error</h2>
            <p className="text-gray-600 mb-8">{error || 'Blog not found'}</p>
            <Link
              to="/blog"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Blog
            </Link>
            <Link
              to="/stores"
              className="inline-flex items-center px-6 py-3 border-2 border-purple-500 text-purple-600 text-base font-medium rounded-lg hover:bg-purple-50 transition-colors"
            >
              Browse All Stores
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default BlogDetail;
