import React from 'react'
import { motion } from 'framer-motion'

const AboutUs = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto"
      >
        <h1 className="text-4xl font-bold text-gray-900 mb-8">About Us</h1>
        <div className="bg-white rounded-xl shadow-sm p-8">
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Our Vision</h2>
            <p className="text-gray-600 leading-relaxed">
              At BonusEarned, we envision a future where smart shopping becomes effortless and rewarding. 
              As an innovative cashback platform, we're dedicated to transforming the online shopping experience 
              by connecting shoppers with their favorite brands while providing valuable rewards.
            </p>
          </section>

          <section className="mt-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Our Mission</h2>
            <p className="text-gray-600 leading-relaxed">
              We strive to create a seamless and transparent shopping experience that truly rewards our users. 
              Through cutting-edge technology and carefully curated partnerships, we're building a platform 
              that makes earning cashback simple, reliable, and enjoyable for everyone.
            </p>
          </section>

          <section className="mt-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">What Sets Us Apart</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {[
                {
                  title: 'User-Centric Approach',
                  description: 'Every feature and service we develop is designed with our users in mind, ensuring a smooth and intuitive experience.'
                },
                {
                  title: 'Secure & Reliable',
                  description: 'We prioritize the security of your transactions and personal information, implementing robust protection measures.'
                },
                {
                  title: 'Transparent Rewards',
                  description: 'Clear and straightforward cashback terms with no hidden conditions. What you see is what you get.'
                },
                {
                  title: 'Quality Partnerships',
                  description: 'We collaborate with trusted brands and retailers to bring you the best cashback opportunities.'
                }
              ].map((value, index) => (
                <motion.div
                  key={value.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-gray-50 p-6 rounded-lg"
                >
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{value.title}</h3>
                  <p className="text-gray-600">{value.description}</p>
                </motion.div>
              ))}
            </div>
          </section>

          <section className="mt-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Our Commitment</h2>
            <p className="text-gray-600 leading-relaxed">
              As a new player in the cashback industry, we're committed to continuous improvement and innovation. 
              We value your feedback and are constantly working to enhance our platform, expand our partner network, 
              and deliver the best possible cashback experience. Our dedicated support team is here to assist you 
              every step of the way.
            </p>
          </section>

        </div>
      </motion.div>
    </div>
  )
}

export default AboutUs
