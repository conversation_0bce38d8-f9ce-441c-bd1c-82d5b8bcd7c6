import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { couponApi } from '../../api/coupon';
import CouponButton from '../../components/common/CouponButton';

const ITEMS_PER_PAGE = 30;

const Coupons: React.FC = () => {
  const [coupons, setCoupons] = useState<any[]>([]);
  const [featuredCoupons, setFeaturedCoupons] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [featuredLoading, setFeaturedLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [selectedCoupon, setSelectedCoupon] = useState<any>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [countdown, setCountdown] = useState<number | null>(0);
  const [countdownTimer, setCountdownTimer] = useState<NodeJS.Timeout | null>(null);

  // Load featured coupons with featured=true parameter
  const loadFeaturedCoupons = async () => {
    try {
      setFeaturedLoading(true);
      // Use featured=true parameter to get real featured coupons
      const params: any = {
        page: 1,
        page_size: 6,
        featured: true
      };

      const data = await couponApi.searchCoupons('', params);
      // Only set featured coupons if we have data
      if (data && data.length > 0) {
        setFeaturedCoupons(data);
      } else {
        // Set empty array if no featured coupons
        setFeaturedCoupons([]);
      }
    } catch (error) {
      console.error('Error loading featured coupons:', error);
      // Set empty array on error
      setFeaturedCoupons([]);
    } finally {
      setFeaturedLoading(false);
    }
  };

  // Load regular coupons
  const loadCoupons = async (query?: string, pageNum: number = 1, append: boolean = false) => {
    try {
      setLoading(true);
      const params: any = {
        page: pageNum,
        page_size: ITEMS_PER_PAGE
      };

      const data = await couponApi.searchCoupons(query || '', params);

      if (append) {
        setCoupons(prev => [...prev, ...data]);
      } else {
        setCoupons(data);
      }

      setHasMore(data.length === ITEMS_PER_PAGE);
    } catch (error) {
      console.error('Error loading coupons:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };



  // Load initial data
  useEffect(() => {
    loadCoupons();
    loadFeaturedCoupons();
  }, []);

  const handleSearch = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setPage(1);
      loadCoupons(searchValue);
    }
  };

  const handleLoadMore = async () => {
    if (loadingMore || !hasMore) return;

    setLoadingMore(true);
    const nextPage = page + 1;
    await loadCoupons(searchValue, nextPage, true);
    setPage(nextPage);
    setLoadingMore(false);
  };

  const handleCouponClick = (coupon: any) => {
    // 检查是否是通过URL参数访问
    const urlParams = new URLSearchParams(window.location.search);
    const urlCouponCode = urlParams.get('code');
    const isUrlAccess = urlCouponCode === coupon.code;

    if (!isUrlAccess) {
      // 在新标签页打开当前页面并带上优惠券参数
      const newTabUrl = new URL(window.location.href);
      newTabUrl.searchParams.set('code', coupon.code);
      window.open(newTabUrl.toString(), '_blank');

      // 当前页面跳转到商家跟踪链接
      if (coupon.merchant_info?.track_url) {
        window.location.href = coupon.merchant_info.track_url;
      }
      return;
    }

    setSelectedCoupon(coupon);
    setIsCopied(false);
    setCountdown(0);
  };

  const handleStoreClick = (uniqueName: string) => {
    window.location.href = `/store/${uniqueName}`;
  };

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setIsCopied(true);

      // 显示通知
      const notification = document.createElement('div');
      notification.className = 'notification';
      notification.textContent = 'Code copied! Will open store in 3 seconds';
      document.body.appendChild(notification);

      setTimeout(() => {
        notification.remove();
      }, 2000);

      // 清除现有定时器
      if (countdownTimer) {
        clearInterval(countdownTimer);
      }

      // 开始倒计时
      setCountdown(3);
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev === null || prev <= 1) {
            clearInterval(timer);
            // 在新标签页打开商家跟踪链接
            if (selectedCoupon && selectedCoupon.merchant_info?.track_url) {
              window.open(selectedCoupon.merchant_info.track_url, '_blank');
            }
            setCountdown(null);
            setIsCopied(false);
            setCountdownTimer(null);
            return null;
          }
          return prev - 1;
        });
      }, 1000);

      // 保存定时器引用
      setCountdownTimer(timer);
    } catch (error) {
      console.error('Failed to copy:', error);
      const notification = document.createElement('div');
      notification.className = 'notification';
      notification.textContent = 'Failed to copy code';
      document.body.appendChild(notification);
      setTimeout(() => notification.remove(), 2000);
    }
  };

  // 在弹窗关闭时清除倒计时
  const handleCloseModal = () => {
    if (countdownTimer) {
      clearInterval(countdownTimer);
      setCountdownTimer(null);
    }
    setSelectedCoupon(null);
    setCountdown(null);
    setIsCopied(false);
  };

  // 检查URL中是否有优惠券代码 - 同时检查普通优惠券和特色优惠券
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');

    if (code) {
      // First check in regular coupons
      if (coupons.length > 0) {
        const coupon = coupons.find(c => c.code === code);
        if (coupon) {
          setSelectedCoupon(coupon);
          return;
        }
      }

      // Then check in featured coupons
      if (featuredCoupons.length > 0) {
        const featuredCoupon = featuredCoupons.find(c => c.code === code);
        if (featuredCoupon) {
          setSelectedCoupon(featuredCoupon);
        }
      }
    }
  }, [coupons, featuredCoupons]);

  // 将优惠券代码显示为部分隐藏的形式
  const maskCouponCode = (code: string) => {
    if (!code) return '';
    const length = code.length;
    const visibleLength = Math.floor(length / 3);
    return code.substring(0, visibleLength) + '•••';
  };

  // 计算优惠券过期状态
  const isExpired = (endedAt: string) => {
    if (!endedAt) return false;
    return new Date(endedAt) < new Date();
  };

  // 格式化日期
  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Enhanced Header Section with Rich Background */}
      <div className="relative bg-gradient-to-br from-purple-800 via-purple-700 to-purple-900 py-16 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Decorative Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Animated Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-600/30 via-purple-800/20 to-pink-600/30"></div>

          {/* Decorative Circles */}
          <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-pink-500/20 to-purple-500/20 rounded-full blur-3xl transform translate-x-1/3 -translate-y-1/3"></div>
          <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-br from-purple-500/20 to-indigo-500/20 rounded-full blur-3xl transform -translate-x-1/3 translate-y-1/3"></div>

          {/* Dot Pattern */}
          <div className="absolute inset-0 opacity-10">
            <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
              <defs>
                <pattern id="coupon-dots" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse" patternContentUnits="userSpaceOnUse">
                  <circle id="pattern-circle" cx="10" cy="10" r="1.6" fill="#ffffff" />
                </pattern>
              </defs>
              <rect x="0" y="0" width="100%" height="100%" fill="url(#coupon-dots)" />
            </svg>
          </div>

          {/* Coupon Ticket Shapes */}
          <div className="absolute -right-24 top-1/4 w-64 h-32 border-4 border-dashed border-purple-300/20 rounded-xl transform rotate-12"></div>
          <div className="absolute -left-24 bottom-1/4 w-64 h-32 border-4 border-dashed border-pink-300/20 rounded-xl transform -rotate-12"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <motion.div
            className="text-center relative"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div
              className="inline-block px-4 py-1 rounded-full bg-white/20 backdrop-blur-sm mb-6"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <span className="text-white/90 text-sm font-medium">Save More With Exclusive Deals</span>
            </motion.div>

            <motion.h1
              className="text-4xl md:text-6xl font-extrabold tracking-tight text-white mb-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <span className="inline-block relative">
                Exclusive Coupons
                <motion.svg
                  className="absolute -right-10 -top-4 w-8 h-8 text-yellow-300"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  animate={{
                    y: [0, -5, 0],
                    rotate: [0, 5, 0, -5, 0]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <path d="M20 7L12 3L4 7V17L12 21L20 17V7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="rgba(255,255,255,0.1)"/>
                  <path d="M12 12L12 21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 12L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 12L4 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </motion.svg>
              </span>
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-pink-300">& Promo Codes</span>
            </motion.h1>

            <motion.p
              className="max-w-2xl mx-auto text-xl text-white/80 mb-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              Find the latest deals and save on your favorite brands
            </motion.p>

            {/* Enhanced Search Box */}
            <motion.div
              className="max-w-xl mx-auto relative"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
            >
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  className="w-full pl-12 pr-4 py-4 rounded-xl border-2 border-white/20 shadow-lg focus:ring-2 focus:ring-purple-300 focus:border-transparent transition-all duration-200 bg-white/10 backdrop-blur-md text-white placeholder-white/70"
                  placeholder="Search for coupons or stores..."
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  onKeyDown={handleSearch}
                />
                {loading && (
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  </div>
                )}
                {!loading && searchValue && (
                  <button
                    onClick={() => {
                      setSearchValue('');
                      loadCoupons('');
                    }}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-white"
                  >
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>

              {/* Search Features */}
              <div className="flex flex-wrap justify-center gap-4 mt-6">
                <div className="flex items-center text-white/70 text-sm">
                  <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Verified Coupons</span>
                </div>
                <div className="flex items-center text-white/70 text-sm">
                  <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Updated Daily</span>
                </div>
                <div className="flex items-center text-white/70 text-sm">
                  <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Exclusive Cashback</span>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Featured Coupons Section - Only shown when there are featured coupons */}
        {!featuredLoading && featuredCoupons.length > 0 && (
          <motion.div
            className="mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex items-center mb-6">
              <div className="w-1 h-6 bg-gradient-to-b from-purple-600 to-pink-600 rounded-full mr-3"></div>
              <h2 className="text-2xl font-bold text-gray-800">Featured Coupons</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredCoupons.map((coupon, index) => (
                <motion.div
                  key={`featured-${coupon.id}-${index}`}
                  className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-purple-100 overflow-hidden group"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  {/* Featured Tag */}
                  <div className="absolute top-0 right-0">
                    <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs font-bold px-3 py-1 rounded-bl-lg shadow-md">
                      Featured
                    </div>
                  </div>

                  <div className="flex flex-col h-full">
                    {/* Merchant Header */}
                    <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 flex items-center">
                      <div
                        className="w-16 h-16 bg-white rounded-lg shadow-sm flex items-center justify-center mr-4 overflow-hidden cursor-pointer"
                        onClick={() => handleStoreClick(coupon.merchant_info?.unique_name || coupon.merchant_unique_name)}
                      >
                        <img
                          alt={coupon.merchant_name}
                          src={coupon.merchant_info?.logo || coupon.merchant_logo}
                          className="max-w-full max-h-full object-contain p-2"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(coupon.merchant_name)}&background=f0f0f0&color=9F7AEA&size=60`;
                          }}
                        />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-800">{coupon.merchant_name}</h3>
                        {coupon.merchant_info?.cashback_value && (
                          <div className="text-sm text-purple-700 font-medium">
                            {coupon.merchant_info.cashback_value} Cashback
                          </div>
                        )}
                      </div>

                      {/* Discount Rate */}
                      {coupon.discount_rate && (
                        <div className="ml-auto inline-flex items-center px-3 py-1.5 rounded-md text-base font-bold bg-blue-100 text-blue-800 shadow-sm">
                          <span className="mr-1">🏷️</span>
                          {coupon.discount_rate}
                        </div>
                      )}
                    </div>

                    {/* Coupon Content */}
                    <div className="p-5 flex-1 flex flex-col">
                      <h2 className="text-lg font-bold text-gray-900 mb-2 line-clamp-1">{coupon.title}</h2>
                      <p className="text-gray-600 text-sm mb-4 line-clamp-2">{coupon.description}</p>

                      <div className="mt-auto flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div className="flex items-center">
                          <div className="bg-gradient-to-r from-purple-50 to-purple-100 text-purple-700 flex items-center px-3 py-1.5 rounded-lg text-sm font-medium border border-purple-100 shadow-sm">
                            <svg className="w-4 h-4 mr-1.5 text-purple-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M20.59 13.41L13.42 20.58C13.2343 20.766 13.0137 20.9135 12.7709 21.0141C12.5281 21.1148 12.2678 21.1666 12.005 21.1666C11.7422 21.1666 11.4819 21.1148 11.2391 21.0141C10.9963 20.9135 10.7757 20.766 10.59 20.58L2 12V3H11L19.59 11.59C19.9625 11.9647 20.1716 12.4716 20.1716 13C20.1716 13.5284 19.9625 14.0353 19.59 14.41V14.41Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M7 9H7.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                            Code: <span className="font-mono ml-1 font-bold">{maskCouponCode(coupon.code)}</span>
                          </div>
                        </div>

                        <div className="flex-none">
                          <CouponButton
                            code={coupon.code}
                            onClick={() => handleCouponClick(coupon)}
                            className="w-full sm:w-auto transform group-hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* All Coupons (without title) - Adjust spacing based on whether featured coupons are shown */}
        <div className={`max-w-6xl mx-auto ${featuredCoupons.length > 0 ? 'mt-8' : 'mt-0'}`}>

          {loading && !coupons.length ? (
            <div className="flex flex-col justify-center items-center py-20">
              <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mb-4" />
              <p className="text-gray-500 animate-pulse">Loading coupons...</p>
            </div>
          ) : coupons.length > 0 ? (
            <div className="space-y-8">
              <div className="grid gap-6">
                {coupons.map((coupon, index) => (
                  <motion.div
                    key={`${coupon.id}-${index}`}
                    className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border border-purple-50 overflow-hidden group"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: Math.min(index * 0.05, 1.5) }}
                    whileHover={{ y: -5 }}
                  >
                    <div className="flex flex-col md:flex-row">
                      {/* Enhanced Merchant Area */}
                      <div
                        className="bg-gradient-to-br from-purple-50 to-purple-100 py-6 px-4 md:w-[220px] md:min-w-[220px] flex flex-col items-center justify-center cursor-pointer hover:from-purple-100 hover:to-pink-100 transition-colors duration-300 relative overflow-hidden"
                        onClick={() => handleStoreClick(coupon.merchant_info?.unique_name || coupon.merchant_unique_name)}
                        role="button"
                        tabIndex={0}
                      >
                        {/* Decorative Background Elements */}
                        <div className="absolute inset-0 opacity-10">
                          <div className="absolute top-0 right-0 w-32 h-32 bg-purple-300 rounded-full transform translate-x-1/2 -translate-y-1/2"></div>
                          <div className="absolute bottom-0 left-0 w-32 h-32 bg-pink-300 rounded-full transform -translate-x-1/2 translate-y-1/2"></div>
                        </div>

                        <div className="text-center relative z-10">
                          <div className="w-24 h-24 mx-auto flex items-center justify-center bg-white rounded-lg shadow-md mb-3 overflow-hidden group-hover:shadow-lg transition-all duration-300">
                            <img
                              alt={coupon.merchant_name}
                              src={coupon.merchant_info?.logo || coupon.merchant_logo}
                              className="max-w-full max-h-full object-contain p-2"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(coupon.merchant_name)}&background=f0f0f0&color=9F7AEA&size=60`;
                              }}
                            />
                          </div>
                          <h3 className="text-sm font-medium text-gray-800 line-clamp-1">{coupon.merchant_name}</h3>

                          {/* Enhanced Discount Rate Display */}
                          {coupon.discount_rate && (
                            <div className="mt-3 inline-flex items-center px-3 py-1.5 rounded-md text-base font-bold bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-sm">
                              <span className="mr-1">🏷️</span>
                              {coupon.discount_rate}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Enhanced Coupon Content Area */}
                      <div className="flex-1 p-6">
                        <div className="flex flex-col h-full">
                          <div className="mb-3 flex justify-between items-start">
                            <h2 className="text-lg font-bold text-gray-900 mb-1 group-hover:text-purple-700 transition-colors duration-300">{coupon.title}</h2>

                            {/* Expiration Badge */}
                            {isExpired(coupon.ended_at) ? (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 whitespace-nowrap">
                                <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Expired
                              </span>
                            ) : coupon.ended_at && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 whitespace-nowrap">
                                <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Expires {formatDate(coupon.ended_at)}
                              </span>
                            )}
                          </div>

                          {/* Enhanced Cashback Information */}
                          <div className="flex flex-wrap gap-2 mb-3">
                            {coupon.merchant_info?.cashback_value && (
                              <span className="inline-flex items-center px-3 py-1.5 rounded-md text-xs font-medium bg-gradient-to-r from-purple-50 to-purple-100 text-purple-700 border border-purple-200">
                                <svg className="w-4 h-4 mr-1.5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                {coupon.merchant_info.cashback_value} Cashback
                              </span>
                            )}
                          </div>

                          <p className="text-gray-600 text-sm mb-5 line-clamp-2 group-hover:text-gray-700 transition-colors duration-300">{coupon.description}</p>

                          <div className="mt-auto flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                            <div className="flex items-center">
                              <div className="bg-gradient-to-r from-purple-50 to-purple-100 text-purple-700 flex items-center px-3 py-1.5 rounded-lg text-sm font-medium border border-purple-100 shadow-sm">
                                <svg className="w-4 h-4 mr-1.5 text-purple-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M20.59 13.41L13.42 20.58C13.2343 20.766 13.0137 20.9135 12.7709 21.0141C12.5281 21.1148 12.2678 21.1666 12.005 21.1666C11.7422 21.1666 11.4819 21.1148 11.2391 21.0141C10.9963 20.9135 10.7757 20.766 10.59 20.58L2 12V3H11L19.59 11.59C19.9625 11.9647 20.1716 12.4716 20.1716 13C20.1716 13.5284 19.9625 14.0353 19.59 14.41V14.41Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M7 9H7.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                                Code: <span className="font-mono ml-1 font-bold">{maskCouponCode(coupon.code)}</span>
                              </div>
                            </div>

                            <div className="flex-none">
                              <CouponButton
                                code={coupon.code}
                                onClick={() => handleCouponClick(coupon)}
                                className="w-full sm:w-auto transform group-hover:scale-105 transition-transform duration-300"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Enhanced Load More Button */}
              {hasMore && (
                <motion.div
                  className="flex justify-center pt-6 pb-12"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.2 }}
                >
                  <button
                    onClick={handleLoadMore}
                    disabled={loadingMore}
                    className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 shadow-md hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loadingMore ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Loading More Coupons...
                      </>
                    ) : (
                      <>
                        <span>Load More Coupons</span>
                        <svg className="ml-2 -mr-1 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                      </>
                    )}
                  </button>
                </motion.div>
              )}
            </div>
          ) : (
            <div className="text-center py-20 bg-purple-50 rounded-xl border border-purple-100">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-white rounded-full mb-6 shadow-md">
                <svg className="w-10 h-10 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">No Coupons Found</h3>
              <p className="text-base text-gray-600 mb-6 max-w-md mx-auto">
                We couldn't find any coupons matching your search criteria. Try different search terms or browse all available offers.
              </p>
              <button
                onClick={() => {
                  setSearchValue('');
                  loadCoupons('');
                }}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-xl shadow-md text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 transition-all duration-300 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                <span>View All Coupons</span>
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Enhanced Coupon Detail Modal */}
      <AnimatePresence>
        {selectedCoupon && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleCloseModal}
          >
            <motion.div
              className="bg-white rounded-2xl shadow-2xl max-w-lg w-full overflow-hidden"
              initial={{ scale: 0.9, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.9, opacity: 0, y: 20 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Enhanced Modal Header - Merchant Info */}
              <div className="bg-gradient-to-r from-purple-700 to-pink-600 p-6 flex items-center relative overflow-hidden">
                {/* Decorative Elements */}
                <div className="absolute inset-0 overflow-hidden">
                  <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full transform translate-x-1/2 -translate-y-1/2"></div>
                  <div className="absolute bottom-0 left-0 w-32 h-32 bg-white/5 rounded-full transform -translate-x-1/2 translate-y-1/2"></div>
                </div>

                <div
                  className="w-20 h-20 bg-white rounded-lg shadow-lg flex items-center justify-center mr-5 overflow-hidden cursor-pointer relative z-10 border-2 border-white/30"
                  onClick={() => {
                    handleCloseModal();
                    handleStoreClick(selectedCoupon.merchant_info?.unique_name || selectedCoupon.merchant_unique_name);
                  }}
                >
                  <img
                    src={selectedCoupon.merchant_info?.logo || selectedCoupon.merchant_logo}
                    alt={selectedCoupon.merchant_name}
                    className="max-h-14 max-w-14 object-contain"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(selectedCoupon.merchant_name)}&background=ffffff&color=9F7AEA&size=60`;
                    }}
                  />
                </div>
                <div className="flex-1 relative z-10">
                  <div className="flex flex-wrap items-center gap-3 mb-1">
                    <h3 className="text-white text-xl font-bold">{selectedCoupon.merchant_name}</h3>
                    {selectedCoupon.discount_rate && (
                      <div className="inline-flex items-center px-3 py-1 rounded-md text-base font-bold bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-sm">
                        <span className="mr-1">🏷️</span>
                        {selectedCoupon.discount_rate}
                      </div>
                    )}
                  </div>
                  <p className="text-white/90 text-sm">{selectedCoupon.title}</p>

                  {/* Cashback Badge in Header */}
                  {selectedCoupon.merchant_info?.cashback_value && (
                    <div className="mt-2 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/20 text-white backdrop-blur-sm">
                      <svg className="w-3.5 h-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {selectedCoupon.merchant_info.cashback_value} Cashback
                    </div>
                  )}
                </div>
                <button
                  className="text-white/80 hover:text-white bg-white/10 hover:bg-white/20 rounded-full p-2 transition-colors duration-200 relative z-10"
                  onClick={handleCloseModal}
                >
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Enhanced Modal Content */}
              <div className="p-6">
                {/* Coupon Code Section */}
                <div className="mb-8">
                  <div className="flex items-center mb-3">
                    <div className="w-1 h-5 bg-gradient-to-b from-purple-600 to-pink-600 rounded-full mr-2.5"></div>
                    <label className="text-base font-bold text-gray-800">Coupon Code</label>
                  </div>

                  <div className="relative">
                    <div className="p-4 w-full border-2 border-dashed border-purple-300 rounded-lg bg-purple-50 flex items-center justify-between">
                      <div className="flex-1 font-mono text-lg font-bold text-purple-800 tracking-wide">
                        {selectedCoupon.code}
                      </div>
                      <button
                        onClick={() => handleCopy(selectedCoupon.code)}
                        className={`flex items-center px-4 py-2 rounded-lg shadow-sm transition-all duration-300 ${
                          isCopied
                            ? 'bg-green-500 text-white'
                            : 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700'
                        }`}
                      >
                        {isCopied ? (
                          <>
                            <svg className="w-5 h-5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                            Copied!
                          </>
                        ) : (
                          <>
                            <svg className="w-5 h-5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                            Copy Code
                          </>
                        )}
                      </button>
                    </div>

                    {/* Countdown Notification */}
                    {countdown !== null && countdown > 0 && (
                      <div className="mt-3 text-center p-2 bg-blue-50 text-blue-700 rounded-lg border border-blue-100">
                        <div className="flex items-center justify-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span>Opening store in <strong>{countdown}</strong> seconds...</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Coupon Details Section */}
                <div className="mb-6">
                  <div className="flex items-center mb-3">
                    <div className="w-1 h-5 bg-gradient-to-b from-purple-600 to-pink-600 rounded-full mr-2.5"></div>
                    <h4 className="text-base font-bold text-gray-800">Coupon Details</h4>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <p className="text-gray-700">{selectedCoupon.description}</p>

                    {/* Expiration Date */}
                    {selectedCoupon.ended_at && (
                      <div className="mt-4 flex items-center text-sm text-gray-600">
                        <svg className="w-4 h-4 mr-1.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <strong>Expires:</strong> <span className="ml-1">{formatDate(selectedCoupon.ended_at)}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="mt-8 flex flex-col sm:flex-row gap-3">
                  <button
                    onClick={() => window.open(selectedCoupon.merchant_info?.track_url, '_blank')}
                    className="flex-1 inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-xl shadow-md text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 transition-all duration-300 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  >
                    <span>Continue to Store</span>
                    <svg className="ml-2 -mr-1 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                  </button>
                  <button
                    onClick={handleCloseModal}
                    className="flex-1 sm:flex-none inline-flex justify-center items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  >
                    Close
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Coupons;
