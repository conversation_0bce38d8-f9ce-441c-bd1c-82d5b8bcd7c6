import React, { useState, useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import api from '../../lib/api'

interface Transaction {
  id: number
  amount: number
  type: string
  status: string
  created_at: string
  store?: {
    name: string
    logo?: string
  }
}

interface Balance {
  approved_amount: number
  paid_amount: number
  pending_amount: number
  total_amount: number
}

interface Profile {
  user: {
    id: number
    email: string
    nickname: string
    phone: string
    payment_info?: PaymentInfo
  }
  balance: Balance
  transactions: Transaction[]
}

interface PaypalInfo {
  default_method: boolean
  paypal_email: string
  paypal_name: string
}

interface BankInfo {
  bank_account_name: string
  bank_account_number: number
  bank_address: string
  bank_country: string
  bank_currency: string
  bank_iban: string
  bank_name: string
  bank_routing_number: number
  bank_sort_code: string
  bank_swift_code: string
  bank_taxpayer_id: string
  default_method: boolean
}

interface PaymentInfo {
  paypal_info?: PaypalInfo
  bank_info?: BankInfo
}

interface UpdateProfileRequest {
  password?: string;
  payment_info?: PaymentInfo;
}

interface UpdateProfileFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
  paypal_email: string;
  paypal_name: string;
  paypal_default: boolean;
  bank_account: string;
  bank_account_name: string;
  bank_name: string;
  bank_branch: string;
  bank_country: string;
  bank_currency: string;
  bank_iban: string;
  bank_routing_number: string;
  bank_sort_code: string;
  bank_swift_code: string;
  bank_taxpayer_id: string;
  bank_default: boolean;
}

interface UpdatePaymentInfoRequest {
  payment_info: {
    paypal_info?: {
      default_method: boolean
      paypal_email: string
      paypal_name: string
    }
    bank_info?: {
      bank_account_name: string
      bank_account_number: number
      bank_address: string
      bank_country: string
      bank_currency: string
      bank_iban: string
      bank_name: string
      bank_routing_number: number
      bank_sort_code: string
      bank_swift_code: string
      bank_taxpayer_id: string
      default_method: boolean
    }
  }
}

interface ProfileResponse {
  email: string
  user_code: string
  nickname: string
  payment_info?: PaymentInfo
  user_balance?: Balance
}

interface Withdrawal {
  id: number;
  amount: number;
  status: number;
  created_at: string;
  payment_info: {
    method: string;
    paypal_email?: string;
    bank_account?: string;
    bank_name?: string;
    bank_branch?: string;
  };
}

interface WithdrawalResponse {
  withdrawals: Withdrawal[];
  pagination: {
    current_page: number;
    page_size: number;
    total: number;
  };
}

interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
}

const Profile = () => {
  const { user, loading: authLoading } = useAuth()
  const navigate = useNavigate()
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [updateError, setUpdateError] = useState<string | null>(null)
  const [formData, setFormData] = useState<UpdateProfileFormData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    paypal_email: '',
    paypal_name: '',
    paypal_default: false,
    bank_account: '',
    bank_account_name: '',
    bank_name: '',
    bank_branch: '',
    bank_country: '',
    bank_currency: '',
    bank_iban: '',
    bank_routing_number: '',
    bank_sort_code: '',
    bank_swift_code: '',
    bank_taxpayer_id: '',
    bank_default: false
  })

  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [profileFormData, setProfileFormData] = useState({
    nickname: '',
    phone: ''
  });
  const [profileUpdateError, setProfileUpdateError] = useState<string | null>(null);

  const getProfile = async () => {
    if (!user) return
    
    setLoading(true)
    setError(null)
    try {
      const response = await api.get<ProfileResponse>('/users/me/profile')
      setProfile({
        user: {
          id: 0, // 后端没有返回 id，暂时使用 0
          email: response.email,
          nickname: response.nickname,
          phone: response.phone || '', // 后端没有返回 phone，暂时使用空字符串
          payment_info: response.payment_info
        },
        balance: response.user_balance || {
          approved_amount: 0,
          paid_amount: 0,
          pending_amount: 0,
          total_amount: 0
        },
        transactions: [] // 暂时使用空数组，如果需要交易记录需要单独获取
      })

      // 更新表单数据
      setFormData(prev => ({
        ...prev,
        paypal_email: response.payment_info?.paypal_info?.paypal_email || '',
        paypal_name: response.payment_info?.paypal_info?.paypal_name || '',
        paypal_default: response.payment_info?.paypal_info?.default_method || false,
        bank_account: response.payment_info?.bank_info?.bank_account_number?.toString() || '',
        bank_account_name: response.payment_info?.bank_info?.bank_account_name || '',
        bank_name: response.payment_info?.bank_info?.bank_name || '',
        bank_branch: response.payment_info?.bank_info?.bank_address || '',
        bank_country: response.payment_info?.bank_info?.bank_country || '',
        bank_currency: response.payment_info?.bank_info?.bank_currency || '',
        bank_iban: response.payment_info?.bank_info?.bank_iban || '',
        bank_routing_number: response.payment_info?.bank_info?.bank_routing_number?.toString() || '',
        bank_sort_code: response.payment_info?.bank_info?.bank_sort_code || '',
        bank_swift_code: response.payment_info?.bank_info?.bank_swift_code || '',
        bank_taxpayer_id: response.payment_info?.bank_info?.bank_taxpayer_id || '',
        bank_default: response.payment_info?.bank_info?.default_method || false
      }))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch profile')
    } finally {
      setLoading(false)
    }
  }

  // 检查用户认证状态
  useEffect(() => {
    if (!authLoading && !user) {
      navigate('/auth/login')
    }
  }, [authLoading, user, navigate])

  // 获取用户资料
  useEffect(() => {
    if (!authLoading && user) {
      getProfile()
    }
  }, [authLoading, user])

  // 如果还在加载认证状态，显示加载中
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-b-coral-600"></div>
      </div>
    )
  }

  // 如果没有用户且认证加载完成，不渲染任何内容（会被上面的 useEffect 重定向）
  if (!user) {
    return null
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    try {
      const updateData: UpdatePaymentInfoRequest = {
        payment_info: {}
      }

      if (formData.paypal_email) {
        updateData.payment_info.paypal_info = {
          default_method: formData.paypal_default,
          paypal_email: formData.paypal_email,
          paypal_name: formData.paypal_name || ''
        }
      }

      if (formData.bank_account) {
        updateData.payment_info.bank_info = {
          default_method: formData.bank_default,
          bank_account_name: formData.bank_account_name || '',
          bank_account_number: parseInt(formData.bank_account),
          bank_address: formData.bank_branch || '',
          bank_country: formData.bank_country || 'USA',
          bank_currency: formData.bank_currency || 'USD',
          bank_iban: formData.bank_iban || '',
          bank_name: formData.bank_name || '',
          bank_routing_number: parseInt(formData.bank_routing_number || '0'),
          bank_sort_code: formData.bank_sort_code || '',
          bank_swift_code: formData.bank_swift_code || '',
          bank_taxpayer_id: formData.bank_taxpayer_id || ''
        }
      }

      // 确保至少有一个默认方式
      if (!formData.paypal_default && !formData.bank_default) {
        if (formData.paypal_email) {
          updateData.payment_info.paypal_info!.default_method = true
        } else if (formData.bank_account) {
          updateData.payment_info.bank_info!.default_method = true
        }
      }

      await api.post('/users/me/update-payment-info', updateData)
      
      await getProfile()
      setIsEditing(false)
      setError(null)
    } catch (error) {
      setError('Failed to update profile')
      console.error('Error updating profile:', error)
    }
  }

  const handleDefaultMethodChange = (method: 'paypal' | 'bank') => {
    setFormData(prev => ({
      ...prev,
      paypal_default: method === 'paypal',
      bank_default: method === 'bank'
    }))
  }

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setProfileUpdateError(null);

    try {
      await api.post('/users/me/update-profile', {
        nickname: profileFormData.nickname,
        phone: profileFormData.phone
      });

      // 如果没有抛出错误，就认为更新成功
      await getProfile();
      setIsEditingProfile(false);
    } catch (error: any) {
      console.error('Error updating profile:', error);
      setProfileUpdateError(error?.message || 'Failed to update profile');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-coral-600"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-3xl mx-auto px-4">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl relative">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error}</span>
          </div>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-3xl mx-auto px-4">
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-xl relative">
            <span className="block sm:inline">No profile data available.</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-8">
        {/* Profile Header */}
        <div className="max-w-full mx-auto">
          <div className="relative overflow-hidden bg-gradient-to-br from-coral-500 via-coral-600 to-coral-700 rounded-3xl shadow-2xl transform transition-all duration-300 hover:scale-[1.01] hover:shadow-coral-500/20">
            {/* 装饰性背景元素 */}
            <div className="absolute -top-24 -right-24 w-96 h-96 opacity-10 animate-spin-slow">
              <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" className="transform rotate-45">
                <path fill="white" d="M44.7,-76.4C58.8,-69.2,71.8,-59.1,79.6,-45.8C87.4,-32.5,90,-16.3,88.5,-1.5C87,13.3,81.4,26.7,73.6,38.4C65.8,50.1,55.8,60.2,43.7,67.1C31.6,74,17.8,77.7,3.3,76.6C-11.1,75.4,-22.2,69.4,-33.6,62.4C-45,55.4,-56.7,47.4,-65.4,36.4C-74.1,25.4,-79.8,11.3,-79.6,-2.8C-79.4,-16.9,-73.4,-31.8,-64.6,-39.9C-55.8,-48,-44.2,-49.3,-33.3,-57.7C-22.4,-66.1,-11.2,-81.6,2.4,-85.8C16,-90,31.9,-83,44.7,-76.4Z" />
              </svg>
            </div>
            <div className="relative px-8 py-12">
              <div className="flex flex-col md:flex-row items-start md:items-center gap-8">
                {/* 用户头像 */}
                <motion.div
                  whileHover={{ scale: 1.05, rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-32 h-32 rounded-2xl bg-white/20 backdrop-blur-md flex items-center justify-center shadow-lg ring-4 ring-white/30 transform transition-all duration-300"
                >
                  <span className="text-6xl text-white font-bold">
                    {profile.user.nickname.charAt(0).toUpperCase()}
                  </span>
                </motion.div>

                {/* 用户信息 */}
                <div className="flex-1">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <h1 className="text-4xl font-bold text-white mb-3 drop-shadow-md">
                      {profile.user.nickname}
                    </h1>
                    <div className="space-y-3">
                      <div className="flex items-center text-coral-50 gap-3">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <span className="text-xl">{profile.user.email}</span>
                      </div>
                      <div className="flex items-center text-coral-50 gap-3">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        <span className="text-xl">{profile.user.phone || 'No phone number'}</span>
                      </div>
                    </div>
                  </motion.div>
                </div>

                {/* 编辑按钮 */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="absolute top-6 right-6"
                >
                  <button
                    onClick={() => {
                      setIsEditingProfile(true);
                      setProfileFormData({
                        nickname: profile.user.nickname,
                        phone: profile.user.phone || ''
                      });
                    }}
                    className="inline-flex items-center px-5 py-2.5 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-white bg-white/20 hover:bg-white/30 backdrop-blur-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-coral-500 transition-all duration-300"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    <span style={{color: 'white'}}>Edit Profile</span>
                  </button>
                </motion.div>
              </div>
            </div>
          </div>

          {/* 编辑表单 */}
          {isEditingProfile && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 bg-white rounded-2xl shadow-lg p-8"
            >
              <form onSubmit={handleProfileUpdate} className="space-y-4">
                {profileUpdateError && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg"
                  >
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-red-700">{profileUpdateError}</p>
                      </div>
                    </div>
                  </motion.div>
                )}
                <div>
                  <label htmlFor="nickname" className="block text-sm font-medium text-gray-700">
                    Nickname
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <input
                      type="text"
                      id="nickname"
                      value={profileFormData.nickname}
                      onChange={(e) => setProfileFormData(prev => ({ ...prev, nickname: e.target.value }))}
                      className="block w-full pr-10 border border-gray-300 rounded-lg focus:ring-coral-500 focus:border-coral-500 pl-4 py-3 transition-all duration-200"
                      minLength={2}
                      maxLength={50}
                      required
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  </div>
                </div>
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                    Phone Number
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <input
                      type="tel"
                      id="phone"
                      value={profileFormData.phone}
                      onChange={(e) => setProfileFormData(prev => ({ ...prev, phone: e.target.value }))}
                      className="block w-full pr-10 border border-gray-300 rounded-lg focus:ring-coral-500 focus:border-coral-500 pl-4 py-3 transition-all duration-200"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                  </div>
                </div>
                <div className="flex justify-end space-x-3 pt-4">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="button"
                    onClick={() => setIsEditingProfile(false)}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-coral-500 transition-all duration-200"
                  >
                    Cancel
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="submit"
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-coral-600 hover:bg-coral-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-coral-500 transition-all duration-200"
                  >
                    Save Changes
                  </motion.button>
                </div>
              </form>
            </motion.div>
          )}
        </div>

        {/* Balance Information */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div 
            whileHover={{ scale: 1.03 }}
            className="bg-gradient-to-br from-gray-50 to-white p-6 rounded-2xl shadow-md border border-gray-100"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="text-sm font-medium text-gray-500">Total Amount</div>
              <div className="w-10 h-10 rounded-xl bg-gray-100 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              ${profile.balance.total_amount.toFixed(2)}
            </div>
          </motion.div>

          <motion.div 
            whileHover={{ scale: 1.03 }}
            className="bg-gradient-to-br from-yellow-50 to-white p-6 rounded-2xl shadow-md border border-yellow-100"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="text-sm font-medium text-yellow-600">Pending Amount</div>
              <div className="w-10 h-10 rounded-xl bg-yellow-100 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="text-2xl font-bold text-yellow-600">
              ${profile.balance.pending_amount.toFixed(2)}
            </div>
          </motion.div>

          <motion.div 
            whileHover={{ scale: 1.03 }}
            className="bg-gradient-to-br from-green-50 to-white p-6 rounded-2xl shadow-md border border-green-100"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="text-sm font-medium text-green-600">Approved Amount</div>
              <div className="w-10 h-10 rounded-xl bg-green-100 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="text-2xl font-bold text-green-600">
              ${profile.balance.approved_amount.toFixed(2)}
            </div>
          </motion.div>

          <motion.div 
            whileHover={{ scale: 1.03 }}
            className="bg-gradient-to-br from-blue-50 to-white p-6 rounded-2xl shadow-md border border-blue-100"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="text-sm font-medium text-blue-600">Paid Amount</div>
              <div className="w-10 h-10 rounded-xl bg-blue-100 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 8h6m-5 0a3 3 0 110 6H9l3 3m-3-6h6m6 1a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="text-2xl font-bold text-blue-600">
              ${profile.balance.paid_amount.toFixed(2)}
            </div>
          </motion.div>
        </div>

        {/* Profile Edit Form */}
        {isEditing ? (
          <form onSubmit={handleSubmit} className="space-y-6 bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-lg font-semibold text-gray-900">Edit Payment Information</h2>
            
            {/* PayPal Section */}
            <div className="space-y-4">
              <h3 className="text-md font-medium text-gray-700">PayPal Information</h3>
              <div className="space-y-4">
                <div>
                  <label htmlFor="paypal_email" className="block text-sm font-medium text-gray-700">
                    PayPal Email
                  </label>
                  <input
                    type="email"
                    name="paypal_email"
                    id="paypal_email"
                    value={formData.paypal_email}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-coral-500 focus:border-coral-500"
                  />
                </div>
                <div>
                  <label htmlFor="paypal_name" className="block text-sm font-medium text-gray-700">
                    PayPal Name
                  </label>
                  <input
                    type="text"
                    name="paypal_name"
                    id="paypal_name"
                    value={formData.paypal_name}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-coral-500 focus:border-coral-500"
                  />
                </div>
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="paypal_default"
                    name="default_method"
                    checked={formData.paypal_default}
                    onChange={() => handleDefaultMethodChange('paypal')}
                    className="h-4 w-4 text-coral-600 focus:ring-coral-500 border-gray-300"
                  />
                  <label htmlFor="paypal_default" className="ml-2 block text-sm text-gray-900">
                    Set as default payment method
                  </label>
                </div>
              </div>
            </div>

            {/* Bank Account Section */}
            <div className="space-y-4">
              <h3 className="text-md font-medium text-gray-700">Bank Account Information</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="bank_account_name" className="block text-sm font-medium text-gray-700">
                    Account Name
                  </label>
                  <input
                    type="text"
                    name="bank_account_name"
                    id="bank_account_name"
                    value={formData.bank_account_name}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-coral-500 focus:border-coral-500"
                  />
                </div>
                <div>
                  <label htmlFor="bank_account" className="block text-sm font-medium text-gray-700">
                    Account Number
                  </label>
                  <input
                    type="text"
                    name="bank_account"
                    id="bank_account"
                    value={formData.bank_account}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-coral-500 focus:border-coral-500"
                  />
                </div>
                <div>
                  <label htmlFor="bank_name" className="block text-sm font-medium text-gray-700">
                    Bank Name
                  </label>
                  <input
                    type="text"
                    name="bank_name"
                    id="bank_name"
                    value={formData.bank_name}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-coral-500 focus:border-coral-500"
                  />
                </div>
                <div>
                  <label htmlFor="bank_branch" className="block text-sm font-medium text-gray-700">
                    Bank Address
                  </label>
                  <input
                    type="text"
                    name="bank_branch"
                    id="bank_branch"
                    value={formData.bank_branch}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-coral-500 focus:border-coral-500"
                  />
                </div>
                <div>
                  <label htmlFor="bank_routing_number" className="block text-sm font-medium text-gray-700">
                    Routing Number
                  </label>
                  <input
                    type="text"
                    name="bank_routing_number"
                    id="bank_routing_number"
                    value={formData.bank_routing_number}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-coral-500 focus:border-coral-500"
                  />
                </div>
                <div>
                  <label htmlFor="bank_swift_code" className="block text-sm font-medium text-gray-700">
                    SWIFT Code
                  </label>
                  <input
                    type="text"
                    name="bank_swift_code"
                    id="bank_swift_code"
                    value={formData.bank_swift_code}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-coral-500 focus:border-coral-500"
                  />
                </div>
                <div>
                  <label htmlFor="bank_iban" className="block text-sm font-medium text-gray-700">
                    IBAN
                  </label>
                  <input
                    type="text"
                    name="bank_iban"
                    id="bank_iban"
                    value={formData.bank_iban}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-coral-500 focus:border-coral-500"
                  />
                </div>
                <div>
                  <label htmlFor="bank_sort_code" className="block text-sm font-medium text-gray-700">
                    Sort Code
                  </label>
                  <input
                    type="text"
                    name="bank_sort_code"
                    id="bank_sort_code"
                    value={formData.bank_sort_code}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-coral-500 focus:border-coral-500"
                  />
                </div>
                <div>
                  <label htmlFor="bank_taxpayer_id" className="block text-sm font-medium text-gray-700">
                    Taxpayer ID
                  </label>
                  <input
                    type="text"
                    name="bank_taxpayer_id"
                    id="bank_taxpayer_id"
                    value={formData.bank_taxpayer_id}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-coral-500 focus:border-coral-500"
                  />
                </div>
              </div>
              <div className="flex items-center mt-4">
                <input
                  type="radio"
                  id="bank_default"
                  name="default_method"
                  checked={formData.bank_default}
                  onChange={() => handleDefaultMethodChange('bank')}
                  className="h-4 w-4 text-coral-600 focus:ring-coral-500 border-gray-300"
                />
                <label htmlFor="bank_default" className="ml-2 block text-sm text-gray-900">
                  Set as default payment method
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-coral-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-coral-600 hover:bg-coral-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-coral-500"
              >
                Save Changes
              </button>
            </div>
          </form>
        ) : (
          <div className="space-y-6">
            {/* Payment Information Display */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Information</h3>
              <div className="space-y-4">
                {/* PayPal Information */}
                {profile?.user?.payment_info?.paypal_info && (
                  <div className="mb-6">
                    <div className="flex items-center mb-2">
                      <h3 className="text-md font-medium text-gray-700">PayPal</h3>
                      {profile.user.payment_info.paypal_info.default_method && (
                        <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Default Method
                        </span>
                      )}
                    </div>
                    <div className="bg-gray-50 p-4 rounded-2xl">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="text-sm text-gray-500">PayPal Email</div>
                          <div className="font-medium">{profile.user.payment_info.paypal_info.paypal_email}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">PayPal Name</div>
                          <div className="font-medium">{profile.user.payment_info.paypal_info.paypal_name}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Bank Information */}
                {profile?.user?.payment_info?.bank_info && (
                  <div>
                    <div className="flex items-center mb-2">
                      <h3 className="text-md font-medium text-gray-700">Bank Account</h3>
                      {profile.user.payment_info.bank_info.default_method && (
                        <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Default Method
                        </span>
                      )}
                    </div>
                    <div className="bg-gray-50 p-4 rounded-2xl">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="text-sm text-gray-500">Account Name</div>
                          <div className="font-medium">{profile.user.payment_info.bank_info.bank_account_name}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">Account Number</div>
                          <div className="font-medium">{profile.user.payment_info.bank_info.bank_account_number}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">Bank Name</div>
                          <div className="font-medium">{profile.user.payment_info.bank_info.bank_name}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">Bank Address</div>
                          <div className="font-medium">{profile.user.payment_info.bank_info.bank_address}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">Country</div>
                          <div className="font-medium">{profile.user.payment_info.bank_info.bank_country}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">Currency</div>
                          <div className="font-medium">{profile.user.payment_info.bank_info.bank_currency}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">IBAN</div>
                          <div className="font-medium">{profile.user.payment_info.bank_info.bank_iban}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">Routing Number</div>
                          <div className="font-medium">{profile.user.payment_info.bank_info.bank_routing_number}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">Sort Code</div>
                          <div className="font-medium">{profile.user.payment_info.bank_info.bank_sort_code}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">SWIFT Code</div>
                          <div className="font-medium">{profile.user.payment_info.bank_info.bank_swift_code}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">Taxpayer ID</div>
                          <div className="font-medium">{profile.user.payment_info.bank_info.bank_taxpayer_id}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {!profile?.user?.payment_info?.paypal_info && !profile?.user?.payment_info?.bank_info && (
                  <div className="text-center text-gray-500">
                    No payment information set
                  </div>
                )}
              </div>
            </div>
            <button
              onClick={() => setIsEditing(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-coral-600 hover:bg-coral-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-coral-500"
            >
              Edit Payment Info
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default Profile
