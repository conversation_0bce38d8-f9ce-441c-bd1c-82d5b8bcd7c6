import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion';
import dayjs from 'dayjs';
import api from '../lib/api';
import { FaSearch, FaArrowUp, FaCalendarAlt, FaTag, FaBookmark, FaChevronLeft, FaChevronRight } from 'react-icons/fa';

interface Blog {
  id: number;
  title: string;
  description: string;
  content: string;
  featured_image: string;
  published_at: string;
  category: string;
  tags: string[];
  merchant_info?: {
    id: number;
    name: string;
    unique_name: string;
    merchant_code: string;
    logo: string;
    website: string;
    track_url: string;
    cashback_type: number;
    cashback_value: string;
    description: string;
    category: {
      id: number;
      name: string;
      icon: string;
    };
    featured: boolean;
    country: string;
    supported_countries: string[] | null;
    created_at: string;
    updated_at: string;
  };
}

interface BlogResponse {
  code: number;
  message: string;
  data: {
    total: number;
    page: number;
    page_size: number;
    blog_list: Blog[];
  };
}

const BlogPage: React.FC = () => {
  // Main state
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [featuredBlogs, setFeaturedBlogs] = useState<Blog[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [searchInput, setSearchInput] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [featuredLoading, setFeaturedLoading] = useState(true);

  // Category filtering
  const [categories, setCategories] = useState<string[]>([]);
  const [activeCategory, setActiveCategory] = useState<string>('all');

  // UI state
  const [showBackToTop, setShowBackToTop] = useState(false);

  // Refs
  const blogListRef = useRef<HTMLDivElement>(null);

  // Scroll animation
  const { scrollY } = useScroll();
  const headerOpacity = useTransform(scrollY, [0, 200], [1, 0.8]);
  const headerY = useTransform(scrollY, [0, 200], [0, -20]);

  const pageSize = 12; // Reduced for better visual presentation
  const totalPages = Math.ceil(total / pageSize);

  // Fetch regular blogs with filtering options
  const fetchBlogs = async () => {
    try {
      setLoading(true);
      const params: any = {
        page,
        page_size: pageSize,
        search: searchQuery,
      };

      // Add category filter if not 'all'
      if (activeCategory !== 'all') {
        params.category = activeCategory;
      }

      const data = await api.get<BlogResponse['data']>('/blogs', { params });

      if (data && Array.isArray(data.blog_list)) {
        setBlogs(data.blog_list);
        setTotal(data.total);

        // Extract unique categories if not already loaded
        if (categories.length === 0) {
          const uniqueCategories = Array.from(
            new Set(data.blog_list.map(blog => blog.category))
          ).filter(Boolean);
          setCategories(uniqueCategories);
        }
      }
    } catch (error) {
      console.error('Error fetching blogs:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch featured blogs
  const fetchFeaturedBlogs = async () => {
    try {
      setFeaturedLoading(true);
      const data = await api.get<BlogResponse['data']>('/blogs', {
        params: {
          page: 1,
          page_size: 3, // Limit to 3 featured blogs
          featured: true, // Assuming API supports this parameter
        },
      });

      if (data && Array.isArray(data.blog_list)) {
        setFeaturedBlogs(data.blog_list);
      }
    } catch (error) {
      console.error('Error fetching featured blogs:', error);
    } finally {
      setFeaturedLoading(false);
    }
  };

  // Scroll event handler for back-to-top button
  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 500);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Fetch blogs when page, search query, or category changes
  useEffect(() => {
    fetchBlogs();
    // Scroll to top of blog list when changing page
    if (page > 1 && blogListRef.current) {
      blogListRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [page, searchQuery, activeCategory]);

  // Fetch featured blogs on initial load
  useEffect(() => {
    fetchFeaturedBlogs();
  }, []);

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearchQuery(searchInput);
    setPage(1); // Reset page when searching
    setActiveCategory('all'); // Reset category filter when searching
  };

  // Handle category filter change
  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    setPage(1); // Reset page when changing category
    setSearchQuery(''); // Clear search when changing category
    setSearchInput(''); // Clear search input when changing category
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return dayjs(dateString).format('MMM D, YYYY');
  };

  // Truncate text with ellipsis
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + '...';
  };

  // Generate pagination array
  const generatePaginationArray = (currentPage: number, totalPages: number) => {
    if (totalPages <= 1) return [];

    const delta = 2;
    const range = [];
    const rangeWithDots = [];
    let l;

    range.push(1);

    for (let i = currentPage - delta; i <= currentPage + delta; i++) {
      if (i < totalPages && i > 1) {
        range.push(i);
      }
    }

    range.push(totalPages);

    for (let i of range) {
      if (l) {
        if (i - l === 2) {
          rangeWithDots.push(l + 1);
        } else if (i - l !== 1) {
          rangeWithDots.push('...');
        }
      }
      rangeWithDots.push(i);
      l = i;
    }

    return rangeWithDots;
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-purple-50 to-white">
      {/* Enhanced Header Section with Rich Background */}
      <div className="relative bg-gradient-to-br from-purple-800 via-purple-700 to-purple-900 py-16 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Decorative Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Animated Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-600/30 via-purple-800/20 to-pink-600/30"></div>

          {/* Decorative Circles */}
          <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-pink-500/20 to-purple-500/20 rounded-full blur-3xl transform translate-x-1/3 -translate-y-1/3"></div>
          <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-br from-purple-500/20 to-indigo-500/20 rounded-full blur-3xl transform -translate-x-1/3 translate-y-1/3"></div>

          {/* Dot Pattern */}
          <div className="absolute inset-0 opacity-10">
            <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
              <defs>
                <pattern id="blog-dots" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse" patternContentUnits="userSpaceOnUse">
                  <circle id="pattern-circle" cx="10" cy="10" r="1.6" fill="#ffffff" />
                </pattern>
              </defs>
              <rect x="0" y="0" width="100%" height="100%" fill="url(#blog-dots)" />
            </svg>
          </div>

          {/* Decorative Shapes */}
          <div className="absolute -right-24 top-1/4 w-64 h-32 border-4 border-dashed border-purple-300/20 rounded-xl transform rotate-12"></div>
          <div className="absolute -left-24 bottom-1/4 w-64 h-32 border-4 border-dashed border-pink-300/20 rounded-xl transform -rotate-12"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <motion.div
            className="text-center relative"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div
              className="inline-block px-4 py-1 rounded-full bg-white/20 backdrop-blur-sm mb-6"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <span className="text-white/90 text-sm font-medium">Insights & Inspiration</span>
            </motion.div>

            <motion.h1
              className="text-4xl md:text-6xl font-extrabold tracking-tight text-white mb-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <span className="inline-block relative">
                Discover Our Blog
                <motion.svg
                  className="absolute -right-10 -top-4 w-8 h-8 text-yellow-300"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  animate={{
                    y: [0, -5, 0],
                    rotate: [0, 5, 0, -5, 0]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="rgba(255,255,255,0.1)"/>
                </motion.svg>
              </span>
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-pink-300">Expert Tips & Savings Guides</span>
            </motion.h1>

            <motion.p
              className="max-w-2xl mx-auto text-xl text-white/80 mb-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              Discover strategies to maximize your cashback and unlock exclusive savings opportunities
            </motion.p>

            {/* Enhanced Search Box */}
            <motion.form
              onSubmit={handleSearch}
              className="max-w-xl mx-auto relative"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
            >
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FaSearch className="h-5 w-5 text-purple-500" />
                </div>
                <input
                  type="text"
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  placeholder="Search for articles, tips, and guides..."
                  className="w-full pl-12 pr-4 py-4 rounded-xl border-2 border-white/20 shadow-lg focus:ring-2 focus:ring-purple-300 focus:border-transparent transition-all duration-200 bg-white/10 backdrop-blur-md text-white placeholder-white/70"
                />
                <div className="absolute inset-y-0 right-0 flex items-center">
                  <button
                    type="submit"
                    className="h-full px-6 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-r-xl hover:from-purple-700 hover:to-pink-700 transition-colors duration-200"
                  >
                    Search
                  </button>
                </div>
              </div>
            </motion.form>
          </motion.div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Enhanced Search Status */}
        {searchQuery && (
          <div className="mb-8">
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl shadow-sm p-5 border border-purple-100">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center">
                  <div className="bg-purple-100 p-2 rounded-full mr-3">
                    <FaSearch className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <div className="text-sm text-purple-700 font-medium">Search Results</div>
                    <div className="flex items-center mt-1">
                      <span className="text-gray-600 mr-2">Showing articles for:</span>
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-700 border border-purple-200">
                        "{searchQuery}"
                      </span>
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setSearchInput('');
                    setPage(1);
                  }}
                  className="inline-flex items-center px-4 py-2 rounded-lg bg-white text-purple-700 hover:bg-purple-50 border border-purple-200 shadow-sm transition-colors duration-200 font-medium text-sm"
                >
                  <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="currentColor"/>
                  </svg>
                  Clear Search
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Blog Grid */}
        <div className="mb-12">
          {loading ? (
            // Enhanced Loading Skeleton
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 animate-pulse">
              {Array.from({ length: 8 }).map((_, index) => (
                <div key={index} className="bg-white rounded-xl shadow-sm overflow-hidden border border-purple-50">
                  <div className="h-48 bg-gradient-to-r from-purple-100 to-purple-50" />
                  <div className="p-5 space-y-3">
                    <div className="h-4 bg-gradient-to-r from-purple-200 to-purple-100 rounded-full w-3/4" />
                    <div className="h-4 bg-gradient-to-r from-purple-100 to-purple-50 rounded-full w-1/2" />
                    <div className="h-4 bg-gradient-to-r from-purple-100 to-purple-50 rounded-full w-5/6" />
                    <div className="flex justify-between items-center pt-2">
                      <div className="h-3 bg-purple-100 rounded-full w-1/4" />
                      <div className="h-6 bg-purple-100 rounded-full w-1/4" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : blogs.length > 0 ? (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {blogs.map((blog, index) => (
                  <motion.div
                    key={blog.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: Math.min(index * 0.05, 1) }}
                    className="h-full"
                  >
                    <Link
                      to={`/blog/${blog.id}`}
                      className="group bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-lg transition-all duration-300 block h-full transform hover:-translate-y-1 border border-purple-50/50"
                    >
                      <div className="relative h-48 overflow-hidden">
                        {/* Purple Gradient Overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-purple-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"></div>

                        <img
                          src={blog.featured_image || 'https://via.placeholder.com/300x200?text=Blog+Image'}
                          alt={blog.title}
                          className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = 'https://via.placeholder.com/300x200?text=Blog+Image';
                          }}
                        />

                        {/* Category Tag with Enhanced Styling */}
                        {blog.category && (
                          <div className="absolute top-3 left-3 bg-white/90 backdrop-blur-sm text-xs font-medium text-purple-700 px-3 py-1 rounded-full shadow-sm border border-purple-100 z-20">
                            {blog.category}
                          </div>
                        )}

                        {/* Merchant Logo with Enhanced Styling */}
                        {blog.merchant_info && blog.merchant_info.logo && (
                          <div className="absolute top-3 right-3 bg-white rounded-full p-1.5 shadow-md border border-purple-100 z-20">
                            <img
                              src={blog.merchant_info.logo}
                              alt={blog.merchant_info.name}
                              className="w-7 h-7 rounded-full object-contain"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(blog.merchant_info?.name || 'Blog')}&background=f0f0f0&color=9F7AEA&size=40`;
                              }}
                            />
                          </div>
                        )}

                        {/* Read More Overlay - Only visible on hover */}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                          <span className="px-4 py-2 bg-white/90 text-purple-700 rounded-full text-sm font-medium shadow-lg transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
                            Read Article
                          </span>
                        </div>
                      </div>

                      <div className="p-5">
                        <h3 className="text-lg font-semibold mb-2 line-clamp-2 group-hover:text-purple-600 transition-colors duration-200">
                          {blog.title}
                        </h3>
                        <p className="text-gray-600 text-sm line-clamp-2 mb-4">
                          {blog.description}
                        </p>
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center text-gray-500">
                            <FaCalendarAlt className="w-3 h-3 mr-1.5 text-purple-400" />
                            <span>{dayjs(blog.published_at).format('MMM D, YYYY')}</span>
                          </div>
                          {blog.merchant_info && blog.merchant_info.cashback_value && (
                            <span className="inline-flex items-center px-2.5 py-1 rounded-full bg-gradient-to-r from-purple-50 to-pink-50 text-purple-700 text-xs font-medium border border-purple-100">
                              {blog.merchant_info.cashback_value} Cashback
                            </span>
                          )}
                        </div>
                      </div>
                    </Link>
                  </motion.div>
                ))}
              </div>

              {/* Enhanced Pagination Controls */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-12">
                  <div className="flex space-x-2 rounded-xl bg-white p-3 shadow-md border border-purple-100">
                    <motion.button
                      onClick={() => handlePageChange(page - 1)}
                      disabled={page <= 1}
                      className={`inline-flex h-10 items-center justify-center rounded-lg px-4 text-sm font-medium transition-all ${
                        page <= 1
                          ? 'pointer-events-none text-gray-300 bg-gray-50'
                          : 'text-purple-700 hover:bg-purple-50 hover:text-purple-800 border border-purple-100'
                      }`}
                      whileHover={page > 1 ? { scale: 1.05 } : undefined}
                      whileTap={page > 1 ? { scale: 0.95 } : undefined}
                    >
                      <FaChevronLeft className="h-3.5 w-3.5 mr-2" />
                      Previous
                    </motion.button>

                    <div className="hidden sm:flex sm:items-center sm:space-x-1.5">
                      {generatePaginationArray(page, totalPages).map((pageItem, index) => (
                        <div key={index}>
                          {pageItem === '...' ? (
                            <span className="inline-flex h-10 w-10 items-center justify-center text-sm text-purple-400">
                              ···
                            </span>
                          ) : (
                            <motion.button
                              onClick={() => handlePageChange(pageItem as number)}
                              className={`inline-flex h-10 w-10 items-center justify-center rounded-lg text-sm font-medium transition-all ${
                                pageItem === page
                                  ? 'bg-gradient-to-r from-purple-600 to-purple-800 text-white shadow-md'
                                  : 'text-purple-700 hover:bg-purple-50 border border-purple-100'
                              }`}
                              whileHover={{ scale: pageItem !== page ? 1.1 : 1 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              {pageItem}
                            </motion.button>
                          )}
                        </div>
                      ))}
                    </div>

                    <motion.button
                      onClick={() => handlePageChange(page + 1)}
                      disabled={page >= totalPages}
                      className={`inline-flex h-10 items-center justify-center rounded-lg px-4 text-sm font-medium transition-all ${
                        page >= totalPages
                          ? 'pointer-events-none text-gray-300 bg-gray-50'
                          : 'text-purple-700 hover:bg-purple-50 hover:text-purple-800 border border-purple-100'
                      }`}
                      whileHover={page < totalPages ? { scale: 1.05 } : undefined}
                      whileTap={page < totalPages ? { scale: 0.95 } : undefined}
                    >
                      Next
                      <FaChevronRight className="h-3.5 w-3.5 ml-2" />
                    </motion.button>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-20 bg-white rounded-xl shadow-md border border-purple-100">
              <div className="relative mx-auto w-24 h-24 mb-4">
                <div className="absolute inset-0 bg-purple-100 rounded-full opacity-50 animate-ping"></div>
                <div className="relative flex items-center justify-center w-full h-full bg-gradient-to-r from-purple-100 to-pink-100 rounded-full">
                  <svg className="h-12 w-12 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-xl font-bold text-purple-800 mb-2">No Articles Found</h3>
              <p className="text-gray-600 max-w-md mx-auto">
                {searchQuery
                  ? `We couldn't find any articles matching "${searchQuery}". Please try different search terms.`
                  : "We're working on adding new articles. Please check back soon for fresh content."}
              </p>
              {searchQuery && (
                <motion.button
                  onClick={() => {
                    setSearchQuery('');
                    setSearchInput('');
                    setPage(1);
                  }}
                  className="mt-6 inline-flex items-center px-5 py-2.5 rounded-lg shadow-md text-white bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-700 hover:to-purple-900 transition-all duration-200 font-medium"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="currentColor"/>
                  </svg>
                  Clear Search
                </motion.button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Enhanced Back to Top Button */}
      <AnimatePresence>
        {showBackToTop && (
          <motion.button
            onClick={scrollToTop}
            className="fixed right-8 bottom-8 z-50 p-3 rounded-full bg-gradient-to-r from-purple-600 to-purple-800 text-white shadow-lg"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.5 }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <FaArrowUp className="w-5 h-5" />
          </motion.button>
        )}
      </AnimatePresence>
    </div>
  );
};

export default BlogPage;
