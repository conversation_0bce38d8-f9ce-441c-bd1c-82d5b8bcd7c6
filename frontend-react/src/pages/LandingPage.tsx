import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import { FaChevronRight, FaShoppingBag, FaUserShield, FaGift, FaUserPlus, FaRegSmile, FaShieldAlt, FaQuestionCircle, FaShoppingCart } from 'react-icons/fa'
import { useStore } from '../contexts/StoreContext'
import FeaturedMerchants from '../components/landing/FeaturedMerchants'
import { motion } from 'framer-motion'

const LandingPage: React.FC = () => {
  const { getStores } = useStore()
  const [featuredStores, setFeaturedStores] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [storeCount] = useState(20000)
  const [userCount] = useState(60000)
  const [cashbackPaid] = useState(3600000)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const featuredResponse = await getStores({ page: 1, page_size: 8, featured: true })
        setFeaturedStores(featuredResponse?.merchant_list || [])
      } catch {
        setFeaturedStores([])
      } finally {
        setIsLoading(false)
      }
    }
    fetchData()
  }, [getStores])

  // Section styles
  const purpleBg = 'bg-gradient-to-b from-purple-900 via-purple-800 to-purple-900'
  const whiteBg = 'bg-gradient-to-b from-white to-purple-50/30'
  const sectionPad = 'py-32 md:py-44 px-4 md:px-8' // Increased vertical padding
  const sectionTitle = 'text-5xl md:text-6xl font-extrabold bg-gradient-to-r from-purple-900 to-purple-700 bg-clip-text text-transparent mb-10 text-center drop-shadow-sm'
  const purpleSectionTitle = 'text-5xl md:text-6xl font-extrabold text-white mb-10 text-center drop-shadow-xl tracking-tight'
  const sectionDesc = 'text-2xl text-gray-600 mb-14 text-center max-w-3xl mx-auto font-medium leading-relaxed'
  const purpleSectionDesc = 'text-2xl text-white/90 mb-14 text-center max-w-3xl mx-auto font-medium leading-relaxed'

  return (
    <div className="w-full min-h-screen overflow-x-hidden">
      {/* Hero Section - Purple */}
      <section className={`${purpleBg} ${sectionPad} text-white relative`}>
        <div className="max-w-6xl mx-auto flex flex-col items-center justify-center text-center min-h-[520px] md:min-h-[700px]">
          <h1 className="text-6xl md:text-8xl font-extrabold mb-10 leading-tight text-white">Earn Cashback on Every Purchase</h1>
          <p className="text-2xl md:text-3xl mb-12 max-w-4xl mx-auto text-white/95 font-medium leading-relaxed">Shop at 1,200+ stores and get <span className="font-bold text-yellow-300">50% extra cashback</span> on your first purchase!</p>
          <Link to="/auth/register" className="inline-flex items-center px-14 py-5 rounded-2xl bg-gradient-to-r from-yellow-400 to-yellow-300 hover:from-yellow-300 hover:to-yellow-200 text-purple-900 text-2xl font-extrabold shadow-2xl transition-all duration-300 border-2 border-yellow-300/70 hover:scale-105">
            Get Started & Claim Bonus <FaChevronRight className="ml-3" />
          </Link>
          <div className="flex flex-wrap justify-center gap-16 mt-20">
            <div className="flex flex-col items-center">
              <span className="text-4xl font-extrabold text-yellow-200">{userCount.toLocaleString()}+</span>
              <span className="text-white/90 text-lg mt-2 font-medium">Happy Members</span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-4xl font-extrabold text-yellow-200">${cashbackPaid.toLocaleString()}</span>
              <span className="text-white/90 text-lg mt-2 font-medium">Cashback Paid</span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-4xl font-extrabold text-yellow-200">{storeCount}+</span>
              <span className="text-white/90 text-lg mt-2 font-medium">Partner Stores</span>
            </div>
          </div>
        </div>
      </section>

      {/* Removed TOP BRANDS, MAXIMUM CASHBACK section as requested */}

      {/* New User Benefits - White */}
      <section className={`${whiteBg} ${sectionPad}`}> 
        <h2 className={sectionTitle}>New User Exclusive: First Order Bonus</h2>
        <p className={sectionDesc}>Register now and enjoy <span className="font-bold bg-gradient-to-r from-purple-900 to-purple-600 bg-clip-text text-transparent">50% more cashback</span> on your first purchase. Start your journey to smarter shopping today!</p>
        <div className="flex justify-center mt-14">
          <Link to="/auth/register" className="inline-flex items-center px-12 py-5 rounded-2xl bg-gradient-to-r from-purple-800 to-purple-600 hover:from-purple-700 hover:to-purple-500 text-white text-2xl font-bold shadow-xl transition-all duration-300 hover:scale-105">
            Register & Claim Bonus <FaGift className="ml-3" />
          </Link>
        </div>
      </section>

      {/* How Cashback Works - Purple */}
      <section className={`${purpleBg} ${sectionPad} text-white`}>
        <h2 className={purpleSectionTitle}>How Does Cashback Work?</h2>
        <div className="flex flex-col md:flex-row justify-center gap-16 mt-16 max-w-5xl mx-auto">
          <div className="flex-1 flex flex-col items-center min-h-[220px]">
            <FaUserPlus className="text-5xl mb-6 text-yellow-300 drop-shadow-lg" />
            <h4 className="font-bold text-2xl mb-3 text-white tracking-wide">1. Join Free</h4>
            <p className="text-white/90 text-lg leading-relaxed">Sign up in seconds and unlock instant access to exclusive cashback deals.</p>
          </div>
          <div className="flex-1 flex flex-col items-center min-h-[220px]">
            <FaGift className="text-5xl mb-6 text-yellow-300 drop-shadow-lg" />
            <h4 className="font-bold text-2xl mb-3 text-white tracking-wide">2. Shop & Earn</h4>
            <p className="text-white/90 text-lg leading-relaxed">Shop at your favorite stores via our platform and earn real cash rewards on every order.</p>
          </div>
          <div className="flex-1 flex flex-col items-center min-h-[220px]">
            <FaUserShield className="text-5xl mb-6 text-yellow-300 drop-shadow-lg" />
            <h4 className="font-bold text-2xl mb-3 text-white tracking-wide">3. Get Paid</h4>
            <p className="text-white/90 text-lg leading-relaxed">Withdraw your cashback easily via PayPal, bank transfer, or gift cards.</p>
          </div>
        </div>
      </section>



      {/* Popular Brands - White */}
      <section className={`${whiteBg} ${sectionPad}`}>
        <h2 className={sectionTitle}>Earn money while shopping at all your favorite stores</h2>
        <p className={sectionDesc}>Sounds too good to be true? Here's how it works: Stores pay us a commission for referring you to them, and we share that commission with you as Cashback. It's a win-win for everyone!</p>
        
        <div className="max-w-6xl mx-auto mt-16">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12">
            {/* Row 1 */}
            <motion.div 
              className="flex justify-center items-center p-6 bg-white rounded-xl shadow-md border border-purple-100"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <img src="https://logo.clearbit.com/adidas.com" alt="Adidas" className="h-12 object-contain" />
            </motion.div>
            <motion.div 
              className="flex justify-center items-center p-6 bg-white rounded-xl shadow-md border border-purple-100"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <img src="https://logo.clearbit.com/nike.com" alt="Nike" className="h-12 object-contain" />
            </motion.div>
            <motion.div 
              className="flex justify-center items-center p-6 bg-white rounded-xl shadow-md border border-purple-100"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <img src="https://logo.clearbit.com/walmart.com" alt="Walmart" className="h-12 object-contain" />
            </motion.div>
            <motion.div 
              className="flex justify-center items-center p-6 bg-white rounded-xl shadow-md border border-purple-100"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <img src="https://logo.clearbit.com/kiehls.com" alt="Kiehls" className="h-12 object-contain" />
            </motion.div>
            
            {/* Row 2 */}
            <motion.div 
              className="flex justify-center items-center p-6 bg-white rounded-xl shadow-md border border-purple-100"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <img src="https://logo.clearbit.com/macys.com" alt="Macys" className="h-12 object-contain" />
            </motion.div>
            <motion.div 
              className="flex justify-center items-center p-6 bg-white rounded-xl shadow-md border border-purple-100"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <img src="https://logo.clearbit.com/lancome-usa.com" alt="Lancome" className="h-12 object-contain" />
            </motion.div>
            <motion.div 
              className="flex justify-center items-center p-6 bg-white rounded-xl shadow-md border border-purple-100"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <img src="https://logo.clearbit.com/esteelauder.com" alt="Estee Lauder" className="h-12 object-contain" />
            </motion.div>
            <motion.div 
              className="flex justify-center items-center p-6 bg-white rounded-xl shadow-md border border-purple-100"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <img src="https://logo.clearbit.com/coach.com" alt="Coach" className="h-12 object-contain" />
            </motion.div>
            
            {/* Row 3 */}
            <motion.div 
              className="flex justify-center items-center p-6 bg-white rounded-xl shadow-md border border-purple-100"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <img src="https://logo.clearbit.com/bloomingdales.com" alt="Bloomingdales" className="h-12 object-contain" />
            </motion.div>
            <motion.div 
              className="flex justify-center items-center p-6 bg-white rounded-xl shadow-md border border-purple-100"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <img src="https://logo.clearbit.com/expedia.com" alt="Expedia" className="h-12 object-contain" />
            </motion.div>
            <motion.div 
              className="flex justify-center items-center p-6 bg-white rounded-xl shadow-md border border-purple-100"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <img src="https://logo.clearbit.com/saksfifthavenue.com" alt="Saks Fifth Avenue" className="h-12 object-contain" />
            </motion.div>
            <motion.div 
              className="flex justify-center items-center p-6 bg-white rounded-xl shadow-md border border-purple-100"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <img src="https://logo.clearbit.com/footlocker.com" alt="Foot Locker" className="h-12 object-contain" />
            </motion.div>
          </div>
        </div>
      </section>
      
      {/* Platform Data - White */}
      <section className={`${whiteBg} ${sectionPad}`}> 
        <h2 className={sectionTitle}>A Platform You Can Trust</h2>
        <div className="flex flex-wrap justify-center gap-20 mt-16">
          <div className="flex flex-col items-center">
            <span className="text-5xl font-extrabold bg-gradient-to-r from-purple-900 to-purple-600 bg-clip-text text-transparent drop-shadow-sm">{userCount.toLocaleString()}+</span>
            <span className="text-gray-600 text-lg mt-2 font-medium">Registered Users</span>
          </div>
          <div className="flex flex-col items-center">
            <span className="text-5xl font-extrabold bg-gradient-to-r from-purple-900 to-purple-600 bg-clip-text text-transparent drop-shadow-sm">{storeCount}+</span>
            <span className="text-gray-600 text-lg mt-2 font-medium">Partner Stores</span>
          </div>
          <div className="flex flex-col items-center">
            <span className="text-5xl font-extrabold bg-gradient-to-r from-purple-900 to-purple-600 bg-clip-text text-transparent drop-shadow-sm">${cashbackPaid.toLocaleString()}</span>
            <span className="text-gray-600 text-lg mt-2 font-medium">Cashback Paid</span>
          </div>
        </div>
      </section>

      {/* FAQ - Purple */}
      <section className={`${purpleBg} ${sectionPad} text-white`}>
        <h2 className={purpleSectionTitle}>Frequently Asked Questions</h2>
        <div className="max-w-3xl mx-auto mt-16 space-y-10">
          <div className="bg-gradient-to-b from-purple-800/90 to-purple-700/90 rounded-2xl p-8 flex items-start gap-6 border border-purple-600/20 hover:shadow-lg hover:shadow-purple-900/20 transition-all duration-300">
            <FaQuestionCircle className="text-3xl mt-1 text-yellow-200 drop-shadow-lg" />
            <div>
              <h4 className="font-bold mb-2 text-lg text-white tracking-wide">How long does it take for cashback to appear in my account?</h4>
              <p className="text-white/90 text-lg leading-relaxed">Cashback typically appears as pending within 48 hours after your purchase. It will become available for withdrawal after the store's return period ends, usually 30-90 days depending on the retailer.</p>
            </div>
          </div>
          <div className="bg-gradient-to-b from-purple-800/90 to-purple-700/90 rounded-2xl p-8 flex items-start gap-6 border border-purple-600/20 hover:shadow-lg hover:shadow-purple-900/20 transition-all duration-300">
            <FaQuestionCircle className="text-3xl mt-1 text-yellow-200 drop-shadow-lg" />
            <div>
              <h4 className="font-bold mb-2 text-lg text-white tracking-wide">How do I get my cashback?</h4>
              <p className="text-white/90 text-lg leading-relaxed">Simply shop via our links, and your cashback will be tracked and credited to your account. Withdraw anytime via PayPal, bank, or gift cards.</p>
            </div>
          </div>
          <div className="bg-gradient-to-b from-purple-800/90 to-purple-700/90 rounded-2xl p-8 flex items-start gap-6 border border-purple-600/20 hover:shadow-lg hover:shadow-purple-900/20 transition-all duration-300">
            <FaQuestionCircle className="text-3xl mt-1 text-yellow-200 drop-shadow-lg" />
            <div>
              <h4 className="font-bold mb-2 text-lg text-white tracking-wide">What are the new user benefits?</h4>
              <p className="text-white/90 text-lg leading-relaxed">New users receive an exclusive bonus on their first order, plus access to the highest cashback rates and special promotions.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA - White */}
      <section className={`${whiteBg} ${sectionPad} text-center`}>
        <h2 className="text-5xl md:text-6xl font-extrabold bg-gradient-to-r from-purple-900 to-purple-600 bg-clip-text text-transparent mb-10 drop-shadow-sm">Ready to Start Earning Cashback?</h2>
        <p className="text-2xl text-gray-600 mb-12 max-w-2xl mx-auto font-medium leading-relaxed">Sign up now and get instant access to exclusive cashback deals, new user bonuses, and a world of smarter shopping.</p>
        <Link to="/auth/register" className="inline-flex items-center px-16 py-5 rounded-2xl bg-gradient-to-r from-yellow-400 to-yellow-300 hover:from-yellow-300 hover:to-yellow-200 text-purple-900 text-2xl font-extrabold shadow-2xl transition-all duration-300 border-2 border-yellow-300/70 hover:scale-105">
          Join Now <FaChevronRight className="ml-3" />
        </Link>
      </section>
    </div>
  )
}

export default LandingPage
