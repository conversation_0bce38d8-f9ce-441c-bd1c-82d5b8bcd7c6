import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import MainLayout from '../components/layout/MainLayout'

const NotFound = () => {
  const location = useLocation()

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <div className="mb-8">
            <h1 className="text-8xl font-extrabold text-primary mb-4">404</h1>
            <div className="h-1 w-20 bg-primary mx-auto rounded-full mb-8"></div>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Oops! Page Not Found</h2>
          <p className="text-gray-600 mb-2">
            The page <span className="font-medium text-gray-900">{location.pathname}</span> could not be found.
          </p>
          <p className="text-gray-600 mb-8">
            The page you are looking for might have been removed, renamed, or did not exist in the first place.
          </p>
          <div className="flex justify-center space-x-4">
            <Link
              to="/"
              className="inline-block bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors font-medium"
            >
              Back to Home
            </Link>
            <Link
              to="/stores"
              className="inline-block border-2 border-primary text-primary px-6 py-3 rounded-lg hover:bg-primary/10 transition-colors font-medium"
            >
              Browse Stores
            </Link>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}

export default NotFound
