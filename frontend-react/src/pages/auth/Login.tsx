import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { motion, AnimatePresence } from 'framer-motion'
import Captcha from '../../components/Captcha'
import { FiMail, FiLock, FiShield, FiAlertCircle, FiCheck } from 'react-icons/fi'

const Login = () => {
  const navigate = useNavigate()
  const { login } = useAuth()

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    captcha: '',
  })
  const [captchaCode, setCaptchaCode] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [showForgotPassword, setShowForgotPassword] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (formData.captcha.toLowerCase() !== captchaCode.toLowerCase()) {
      setError('Invalid verification code')
      return
    }

    try {
      setLoading(true)
      await login(formData.email, formData.password)

      // Navigate directly to home page
      navigate('/home')
    } catch (err) {
      setError('Failed to sign in')
      console.error('Login error:', err)
    } finally {
      setLoading(false)
    }
  }



  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* 动态线条背景装饰 */}
      <motion.div
        className="absolute inset-0 w-full h-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1.5 }}
      >
        <svg className="w-full h-full" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="grid-pattern" width="40" height="40" patternUnits="userSpaceOnUse">
              <motion.path
                d="M 40 0 L 0 0 0 40"
                fill="none"
                stroke="rgba(229, 231, 235, 0.4)"
                strokeWidth="1"
                initial={{ pathLength: 0 }}
                animate={{
                  pathLength: 1,
                  transition: { duration: 2, ease: "easeInOut" }
                }}
              />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid-pattern)" />
        </svg>
      </motion.div>

      {/* 左上角装饰 */}
      <div className="absolute top-0 left-0 -mt-10 -ml-10">
        <motion.svg
          width="240"
          height="240"
          viewBox="0 0 240 240"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          initial={{ opacity: 0, scale: 0.8, rotate: 10 }}
          animate={{
            opacity: 0.6,
            scale: 1,
            rotate: 0
          }}
          transition={{ duration: 1, ease: "easeOut" }}
          whileHover={{
            scale: 1.05,
            rotate: -5,
            transition: { duration: 0.3 }
          }}
        >
          {/* 美元符号形装饰 */}
          <motion.path
            d="M120 40V200"
            stroke="rgba(255, 127, 80, 0.3)"
            strokeWidth="4"
            strokeLinecap="round"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{
              pathLength: 1,
              transition: {
                pathLength: { duration: 2, ease: "easeInOut" }
              }
            }}
          />
          <motion.path
            d="M70 80H170"
            stroke="rgba(255, 127, 80, 0.2)"
            strokeWidth="4"
            strokeLinecap="round"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{
              pathLength: 1,
              transition: {
                pathLength: { duration: 2, delay: 0.3, ease: "easeInOut" }
              }
            }}
          />
          <motion.path
            d="M70 160H170"
            stroke="rgba(255, 127, 80, 0.1)"
            strokeWidth="4"
            strokeLinecap="round"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{
              pathLength: 1,
              transition: {
                pathLength: { duration: 2, delay: 0.6, ease: "easeInOut" }
              }
            }}
          />
          <motion.circle
            cx="120"
            cy="120"
            r="80"
            stroke="rgba(255, 127, 80, 0.15)"
            strokeWidth="2"
            strokeDasharray="8 8"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{
              pathLength: 1,
              rotate: 360,
              transition: {
                pathLength: { duration: 2, ease: "easeInOut" },
                rotate: { duration: 40, ease: "linear", repeat: Infinity }
              }
            }}
          />
        </motion.svg>
      </div>

      {/* 右下角装饰 */}
      <div className="absolute bottom-0 right-0 -mb-16 -mr-16">
        <motion.svg
          width="200"
          height="200"
          viewBox="0 0 200 200"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          initial={{ opacity: 0, scale: 0.8, rotate: -10 }}
          animate={{
            opacity: 0.6,
            scale: 1,
            rotate: 0
          }}
          transition={{ duration: 1, ease: "easeOut" }}
          whileHover={{
            scale: 1.05,
            rotate: 5,
            transition: { duration: 0.3 }
          }}
        >
          {/* 星星装饰 */}
          <motion.path
            d="M100 40L110 70L140 75L120 95L125 125L100 110L75 125L80 95L60 75L90 70L100 40Z"
            stroke="rgba(66, 153, 225, 0.3)"
            strokeWidth="2"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{
              pathLength: 1,
              rotate: [0, 360],
              transition: {
                pathLength: { duration: 2, ease: "easeInOut" },
                rotate: { duration: 70, ease: "linear", repeat: Infinity }
              }
            }}
          />
          <motion.circle
            cx="100"
            cy="100"
            r="60"
            stroke="rgba(66, 153, 225, 0.2)"
            strokeWidth="2"
            strokeDasharray="5 5"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{
              pathLength: 1,
              rotate: [0, -360],
              transition: {
                pathLength: { duration: 2, delay: 0.3, ease: "easeInOut" },
                rotate: { duration: 60, ease: "linear", repeat: Infinity }
              }
            }}
          />
          <motion.path
            d="M60 100L140 100"
            stroke="rgba(66, 153, 225, 0.1)"
            strokeWidth="2"
            strokeLinecap="round"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{
              pathLength: 1,
              transition: {
                pathLength: { duration: 2, delay: 0.6, ease: "easeInOut" }
              }
            }}
          />
          <motion.path
            d="M100 60L100 140"
            stroke="rgba(66, 153, 225, 0.1)"
            strokeWidth="2"
            strokeLinecap="round"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{
              pathLength: 1,
              transition: {
                pathLength: { duration: 2, delay: 0.9, ease: "easeInOut" }
              }
            }}
          />
        </motion.svg>
      </div>

      {/* 中间散点装饰 - 漂浮动画 */}
      <motion.div
        className="absolute top-1/4 right-1/3"
        animate={{
          y: [0, -12, 0, 8, 0],
          x: [0, 8, 0, -8, 0],
        }}
        transition={{
          duration: 8,
          ease: "easeInOut",
          repeat: Infinity,
        }}
      >
        <motion.svg
          width="12"
          height="12"
          viewBox="0 0 12 12"
          fill="rgba(246, 173, 85, 0.7)"
          initial={{ scale: 0 }}
          animate={{ scale: [0, 1.2, 1] }}
          transition={{ duration: 1, ease: "easeOut" }}
          whileHover={{ scale: 1.5 }}
        >
          <circle cx="6" cy="6" r="6" />
        </motion.svg>
      </motion.div>

      <motion.div
        className="absolute bottom-1/3 left-1/3"
        animate={{
          y: [0, 12, 0, -8, 0],
          x: [0, -8, 0, 8, 0],
        }}
        transition={{
          duration: 10,
          ease: "easeInOut",
          repeat: Infinity,
          delay: 0.5
        }}
      >
        <motion.svg
          width="8"
          height="8"
          viewBox="0 0 8 8"
          fill="rgba(72, 187, 120, 0.7)"
          initial={{ scale: 0 }}
          animate={{ scale: [0, 1.2, 1] }}
          transition={{ duration: 1, delay: 0.2, ease: "easeOut" }}
          whileHover={{ scale: 1.5 }}
        >
          <circle cx="4" cy="4" r="4" />
        </motion.svg>
      </motion.div>

      <motion.div
        className="absolute top-1/3 right-1/4"
        animate={{
          y: [0, -10, 0, 10, 0],
          x: [0, 10, 0, -10, 0],
        }}
        transition={{
          duration: 9,
          ease: "easeInOut",
          repeat: Infinity,
          delay: 1
        }}
      >
        <motion.svg
          width="10"
          height="10"
          viewBox="0 0 10 10"
          fill="rgba(237, 100, 166, 0.7)"
          initial={{ scale: 0 }}
          animate={{ scale: [0, 1.2, 1] }}
          transition={{ duration: 1, delay: 0.4, ease: "easeOut" }}
          whileHover={{ scale: 1.5 }}
        >
          <circle cx="5" cy="5" r="5" />
        </motion.svg>
      </motion.div>

      {/* 添加更多漂浮点 */}
      <motion.div
        className="absolute top-2/3 left-1/4"
        animate={{
          y: [0, 15, 0, -15, 0],
          x: [0, -15, 0, 15, 0],
        }}
        transition={{
          duration: 12,
          ease: "easeInOut",
          repeat: Infinity,
          delay: 1.5
        }}
      >
        <motion.svg
          width="6"
          height="6"
          viewBox="0 0 6 6"
          fill="rgba(129, 140, 248, 0.7)"
          initial={{ scale: 0 }}
          animate={{ scale: [0, 1.2, 1] }}
          transition={{ duration: 1, delay: 0.8, ease: "easeOut" }}
          whileHover={{ scale: 1.5 }}
        >
          <circle cx="3" cy="3" r="3" />
        </motion.svg>
      </motion.div>

      <motion.div
        className="absolute bottom-1/4 right-1/5"
        animate={{
          y: [0, -15, 0, 15, 0],
          x: [0, 15, 0, -15, 0],
        }}
        transition={{
          duration: 11,
          ease: "easeInOut",
          repeat: Infinity,
          delay: 0.7
        }}
      >
        <motion.svg
          width="7"
          height="7"
          viewBox="0 0 7 7"
          fill="rgba(251, 191, 36, 0.7)"
          initial={{ scale: 0 }}
          animate={{ scale: [0, 1.2, 1] }}
          transition={{ duration: 1, delay: 1, ease: "easeOut" }}
          whileHover={{ scale: 1.5 }}
        >
          <circle cx="3.5" cy="3.5" r="3.5" />
        </motion.svg>
      </motion.div>



      {/* 页面中间装饰线 */}
      <div className="absolute left-0 right-0 top-1/2 transform -translate-y-1/2 overflow-hidden opacity-20">
        <motion.svg
          width="100%"
          height="2"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <motion.path
            d="M0,1 L2000,1"
            stroke="rgba(107, 114, 128, 0.5)"
            strokeWidth="1"
            strokeDasharray="10 6"
            initial={{ pathLength: 0 }}
            animate={{
              pathLength: 1,
              pathOffset: [0, 1],
              transition: {
                pathLength: { duration: 2 },
                pathOffset: { duration: 20, repeat: Infinity, ease: "linear" }
              }
            }}
          />
        </motion.svg>
      </div>

      {/* Logo 和标题区域 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="sm:mx-auto sm:w-full sm:max-w-md relative z-10"
      >
        <div className="text-center">
          <motion.svg
            width="80"
            height="80"
            viewBox="0 0 128 128"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="mx-auto"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            whileHover={{
              scale: 1.05,
              filter: "drop-shadow(0px 0px 8px rgba(139, 92, 246, 0.6))",
              transition: { duration: 0.2 }
            }}
          >
            {/* 定义渐变 */}
            <defs>
              <linearGradient id="purplePinkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8B5CF6" />
                <stop offset="100%" stopColor="#EC4899" />
              </linearGradient>

              {/* 字母B的光晕效果 */}
              <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
                <feGaussianBlur stdDeviation="4" result="blur" />
                <feComposite in="SourceGraphic" in2="blur" operator="over" />
              </filter>
            </defs>

            {/* 紫色/粉色渐变背景 */}
            <circle cx="64" cy="64" r="60" fill="url(#purplePinkGradient)" />

            {/* 装饰元素 - 小圆点 */}
            <circle cx="34" cy="44" r="3" fill="#FFFFFF" opacity="0.7" />
            <circle cx="94" cy="84" r="2" fill="#FFFFFF" opacity="0.5" />

            {/* 完全居中且圆润的字母B */}
            <path d="M64,34
                    C76,34 84,42 84,52
                    C84,59 80,64 75,66
                    C82,68 86,74 86,82
                    C86,94 76,100 64,100
                    L47,100
                    C45.5,100 44,98.5 44,97
                    L44,37
                    C44,35.5 45.5,34 47,34
                    L64,34 Z

                    M62,62
                    C67,62 70,59 70,54
                    C70,49 67,46 62,46
                    L58,46
                    L58,62
                    L62,62 Z

                    M64,88
                    C69,88 72,85 72,80
                    C72,75 69,71 64,71
                    L58,71
                    L58,88
                    L64,88 Z"
                 fill="#FFFFFF"
                 filter="url(#glow)" />

            {/* 光泽效果 */}
            <circle cx="64" cy="64" r="24" fill="white" opacity="0.1" />
          </motion.svg>
        </div>
        <motion.h2
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="mt-6 text-center text-3xl font-extrabold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent"
          style={{
            backgroundSize: "200% 200%",
            animation: "gradientShift 3s ease infinite"
          }}
        >
          Welcome Back
        </motion.h2>
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mt-2 text-center text-sm text-gray-600"
        >
          Don't have an account?{' '}
          <motion.span
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
          <Link
            to="/auth/register"
              className="font-medium text-coral-600 hover:text-coral-500 transition-colors"
          >
              Sign up for free
          </Link>
          </motion.span>
        </motion.p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="mt-8 sm:mx-auto sm:w-full sm:max-w-md relative z-10"
      >
        <motion.div
          className="bg-white py-8 px-4 shadow-lg sm:rounded-xl sm:px-10 border border-gray-100 relative"
          initial={{ boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)" }}
          animate={{
            boxShadow: ["0 10px 15px -3px rgba(0, 0, 0, 0.1)", "0 15px 20px -3px rgba(0, 0, 0, 0.15)", "0 10px 15px -3px rgba(0, 0, 0, 0.1)"]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            repeatType: "reverse"
          }}
          whileHover={{
            boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
            scale: 1.01,
            transition: { duration: 0.3 }
          }}
        >
          {/* 装饰角落线条 */}
          <svg className="absolute top-0 right-0 w-20 h-20" viewBox="0 0 80 80" fill="none">
            <motion.path
              d="M0 0L80 0L80 80"
              stroke="rgba(255, 127, 80, 0.3)"
              strokeWidth="2"
              strokeLinecap="round"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            />
          </svg>
          <svg className="absolute bottom-0 left-0 w-20 h-20" viewBox="0 0 80 80" fill="none">
            <motion.path
              d="M80 80L0 80L0 0"
              stroke="rgba(255, 127, 80, 0.3)"
              strokeWidth="2"
              strokeLinecap="round"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            />
          </svg>

          {/* 背景装饰 */}
          <div className="absolute inset-0 overflow-hidden rounded-xl opacity-5">
            <motion.div
              className="absolute inset-0"
              style={{
                background: "radial-gradient(circle at center, rgba(255,127,80,1) 0%, rgba(255,255,255,0) 70%)"
              }}
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.6, 0.3]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            />
      </div>

          <form className="space-y-5" onSubmit={handleSubmit}>
            <AnimatePresence>
            {error && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg relative flex items-center"
                  role="alert"
                >
                  <motion.div
                    animate={{ rotate: [0, 5, -5, 0] }}
                    transition={{ duration: 0.5 }}
                  >
                    <FiAlertCircle className="mr-2 flex-shrink-0" />
                  </motion.div>
                  <span>{error}</span>
                </motion.div>
              )}
            </AnimatePresence>

            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.3 }}
              whileHover={{ y: -2 }}
            >
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <motion.div
                  className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  whileHover={{ scale: 1.1 }}
                >
                  <FiMail className="h-5 w-5 text-gray-400" />
                </motion.div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="appearance-none block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-coral-500 focus:border-coral-500 transition-all duration-200 sm:text-sm"
                  placeholder="<EMAIL>"
                  style={{ height: '46px' }}
                />
                {formData.email && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
                  >
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 0.5 }}
                    >
                      <FiCheck className="h-5 w-5 text-green-500" />
                    </motion.div>
                  </motion.div>
                )}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.4 }}
              whileHover={{ y: -2 }}
            >
              <div className="flex items-center justify-between">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <button
                    type="button"
                    onClick={() => setShowForgotPassword(!showForgotPassword)}
                    className="text-xs font-medium text-coral-600 hover:text-coral-500 transition-colors"
                  >
                    Forgot your password?
                  </button>
                </motion.div>
              </div>
              <div className="mt-1 relative rounded-md shadow-sm">
                <motion.div
                  className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  whileHover={{ scale: 1.1 }}
                >
                  <FiLock className="h-5 w-5 text-gray-400" />
                </motion.div>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="appearance-none block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-coral-500 focus:border-coral-500 transition-all duration-200 sm:text-sm"
                  placeholder="••••••••"
                  style={{ height: '46px' }}
                />
                {formData.password && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
                  >
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 0.5 }}
                    >
                      <FiCheck className="h-5 w-5 text-green-500" />
                    </motion.div>
                  </motion.div>
                )}
              </div>
            </motion.div>

            <AnimatePresence>
              {showForgotPassword && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="p-4 bg-gray-50 rounded-lg"
                >
                  <p className="text-sm text-gray-600">
                    Enter your email and we'll send you a link to reset your password.
                  </p>
                  <div className="mt-3">
                    <input
                      type="email"
                      placeholder="Your email address"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-coral-500 focus:border-coral-500 sm:text-sm"
                    />
                  </div>
                  <div className="mt-3 flex items-center justify-end">
                    <motion.button
                      type="button"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="text-sm px-3 py-1 bg-coral-500 text-white rounded-md hover:bg-coral-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-coral-500"
                    >
                      Send Reset Link
                    </motion.button>
            </div>
                </motion.div>
              )}
            </AnimatePresence>

            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.5 }}
              whileHover={{ y: -2 }}
            >
              <label htmlFor="captcha" className="block text-sm font-medium text-gray-700">
                Verification Code
              </label>
              <div className="mt-1 flex space-x-3">
                <div className="relative flex-1 rounded-md shadow-sm">
                  <motion.div
                    className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                    whileHover={{ scale: 1.1 }}
                  >
                    <FiShield className="h-5 w-5 text-gray-400" />
                  </motion.div>
                  <input
                  id="captcha"
                  name="captcha"
                  type="text"
                  required
                  value={formData.captcha}
                  onChange={handleChange}
                  className="appearance-none block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-coral-500 focus:border-coral-500 transition-all duration-200 sm:text-sm"
                  placeholder="Enter code"
                  style={{ height: '46px' }}
                  />
                  {formData.captcha && (
                    <motion.div
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
                    >
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 0.5 }}
                      >
                        <FiCheck className="h-5 w-5 text-green-500" />
                      </motion.div>
                    </motion.div>
                  )}
              </div>
                <motion.div
                  className="flex-shrink-0 overflow-hidden rounded-lg border border-gray-200"
                  whileHover={{
                    scale: 1.02,
                    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
                  }}
                >
                  <Captcha onChange={setCaptchaCode} />
                </motion.div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.6 }}
              className="pt-2"
            >
              <motion.button
                type="submit"
                disabled={loading}
                className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-md text-sm font-medium text-white bg-gradient-to-r from-coral-500 to-coral-600 hover:from-coral-600 hover:to-coral-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-coral-500 transition-all duration-300 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
                whileHover={!loading ? {
                  scale: 1.02,
                  boxShadow: "0 10px 15px -3px rgba(255, 127, 80, 0.3)"
                } : {}}
                whileTap={!loading ? { scale: 0.98 } : {}}
                animate={!loading ? {
                  background: [
                    "linear-gradient(to right, rgb(251, 113, 133), rgb(244, 114, 182))",
                    "linear-gradient(to right, rgb(251, 113, 133), rgb(217, 70, 239))",
                    "linear-gradient(to right, rgb(165, 180, 252), rgb(192, 132, 252))",
                    "linear-gradient(to right, rgb(129, 140, 248), rgb(79, 70, 229))",
                    "linear-gradient(to right, rgb(251, 113, 133), rgb(244, 114, 182))"
                  ]
                } : {}}
                transition={{
                  background: {
                    duration: 10,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "linear"
                  }
                }}
              >
                {loading ? (
                  <span className="flex items-center">
                    <motion.svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      fill="none"
                      viewBox="0 0 24 24"
                      animate={{
                        rotate: 360
                      }}
                      transition={{
                        repeat: Infinity,
                        duration: 1,
                        ease: "linear"
                      }}
                    >
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </motion.svg>
                    Signing in...
                  </span>
                ) : 'Sign in'}
              </motion.button>
            </motion.div>
          </form>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="mt-6 border-t border-gray-200 pt-6"
          >
            <motion.p
              className="text-xs text-center text-gray-500"
              animate={{
                opacity: [0.7, 1, 0.7]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                repeatType: "mirror"
              }}
            >
              By signing in, you agree to our{' '}
              <motion.span
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link to="/terms" className="text-coral-600 hover:text-coral-500">
                  Terms of Service
                </Link>
              </motion.span>{' '}
              and{' '}
              <motion.span
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link to="/privacy" className="text-coral-600 hover:text-coral-500">
                  Privacy Policy
                </Link>
              </motion.span>
            </motion.p>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* 添加全局CSS动画规则 */}
      <style dangerouslySetInnerHTML={{ __html: `
        @keyframes gradientShift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }

        /* 输入框平滑过渡 */
        input {
          transition: border-color 0.3s ease, box-shadow 0.3s ease;
          height: 46px !important; /* 确保输入框高度固定 */
        }

        /* 确保输入框不会因为动画而改变大小 */
        input:focus {
          height: 46px !important;
          transform: none !important;
          scale: none !important;
        }

        /* 按钮脉冲动画 */
        button[type=submit]:not(:disabled) {
          animation: buttonPulse 2s infinite;
        }

        @keyframes buttonPulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.02); }
          100% { transform: scale(1); }
        }
      `}} />
    </div>
  )
}

export default Login
