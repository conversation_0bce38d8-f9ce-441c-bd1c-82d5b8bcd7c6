import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import api from '../lib/api';
import { motion, AnimatePresence } from 'framer-motion';
import { FaChevronLeft, FaChevronRight, FaHistory, FaExternalLinkAlt, FaRegClock } from 'react-icons/fa';

interface MerchantInfo {
  id: number;
  name: string;
  unique_name: string;
  logo: string;
  website: string;
  track_url: string;
  cashback_value: string;
  description: string;
}

interface ClickRecord {
  id: number;
  click_date: string;
  click_id: string;
  sub1: string;
  created_at: string;
  merchant_info: MerchantInfo;
}

interface ApiResponse {
  total: number;
  page: number;
  page_size: number;
  click_record_list: ClickRecord[];
}

const ShoppingTrips: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const [records, setRecords] = useState<ClickRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    page_size: 20,
    total: 0,
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    });
  };

  const fetchRecords = async (page: number = 1) => {
    try {
      setLoading(true);
      const response = await api.get<ApiResponse>('/click-record', {
        params: {
          page,
          page_size: pagination.page_size,
        },
      });
      
      if (response?.click_record_list) {
        setRecords(response.click_record_list);
        setPagination({
          page: response.page,
          page_size: response.page_size,
          total: response.total,
        });
      }
    } catch (error) {
      console.error('Failed to fetch shopping trips:', error);
      setRecords([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!authLoading && !user) {
      navigate('/auth/login');
      return;
    }
    if (!authLoading && user) {
      fetchRecords();
    }
  }, [user, authLoading, navigate]);

  const handlePageChange = (newPage: number) => {
    fetchRecords(newPage);
  };

  const handleStoreClick = (uniqueName: string) => {
    navigate(`/stores/${uniqueName}`);
  };

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 relative">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
          className="w-16 h-16 rounded-full border-4 border-gray-200 border-t-coral-500"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-coral-50 to-transparent opacity-50"></div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-gray-50 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-coral-50 to-transparent opacity-50"></div>
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="relative z-10 bg-white p-8 rounded-xl shadow-lg max-w-md w-full mx-4"
        >
          <div className="flex flex-col items-center">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
              className="w-12 h-12 rounded-full border-4 border-gray-200 border-t-coral-500 mb-4"
            />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Shopping Trips</h3>
            <p className="text-gray-500 text-center text-sm">Please wait while we fetch your store visit history...</p>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 right-0 h-48 bg-gradient-to-b from-coral-50 to-transparent"></div>
      <div className="absolute -top-20 -right-20 w-64 h-64 rounded-full bg-coral-100 opacity-30 blur-3xl"></div>
      <div className="absolute top-40 -left-20 w-80 h-80 rounded-full bg-blue-100 opacity-20 blur-3xl"></div>
      
      {/* 浮动粒子效果 */}
      {[...Array(10)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-coral-400"
          style={{
            width: Math.random() * 8 + 4,
            height: Math.random() * 8 + 4,
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            opacity: 0.1,
          }}
          animate={{
            y: [0, -100],
            x: [0, Math.random() * 50 - 25],
            opacity: [0, 0.2, 0],
          }}
          transition={{
            duration: Math.random() * 10 + 10,
            repeat: Infinity,
            delay: Math.random() * 5,
          }}
        />
      ))}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* 页面标题 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900">Shopping Trips</h1>
          <p className="text-gray-600 mt-2">Track your store visits and shopping activities</p>
        </motion.div>
        
        {/* 主内容区 */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-xl shadow-sm overflow-hidden relative"
        >
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 via-coral-500 to-blue-400"></div>
          
          {loading ? (
            <div className="flex flex-col justify-center items-center py-20">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="flex flex-col items-center"
              >
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
                  className="w-12 h-12 rounded-full border-4 border-gray-200 border-t-coral-500 mb-4"
                />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Shopping Trips</h3>
                <p className="text-gray-500 text-center text-sm">Please wait while we fetch your store visit history...</p>
              </motion.div>
            </div>
          ) : (
            <>
              {(!records || records.length === 0) ? (
                <div className="text-center py-20">
                  <motion.div
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="mx-auto w-20 h-20 bg-coral-100 rounded-full flex items-center justify-center mb-4">
                      <FaHistory className="text-coral-500 text-2xl" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-900 mb-2">No Shopping Trips Yet</h3>
                    <p className="text-gray-600 mb-6 max-w-md mx-auto">Start shopping through our platform to track your store visits!</p>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => navigate('/stores')}
                      className="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-coral-500 hover:bg-coral-600 focus:outline-none"
                    >
                      Browse Stores
                    </motion.button>
                  </motion.div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <div className="px-6 py-4 bg-gray-50 border-b">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium text-gray-900 flex items-center">
                        <FaHistory className="mr-2 text-coral-500" />
                        Your Recent Shopping Trips
                      </h3>
                      <div className="text-sm text-gray-500">
                        Showing {records.length} of {pagination.total} trips
                      </div>
                    </div>
                  </div>
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Store
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Cashback
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <div className="flex items-center">
                            <FaRegClock className="mr-1" />
                            Click Time
                          </div>
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Click ID
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      <AnimatePresence>
                        {records.map((record, index) => (
                          <motion.tr 
                            key={record.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: index * 0.05 }}
                            exit={{ opacity: 0 }}
                            className="hover:bg-gray-50 transition-colors"
                          >
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                {record.merchant_info?.logo ? (
                                  <motion.img 
                                    whileHover={{ scale: 1.1, rotate: 5 }}
                                    src={record.merchant_info.logo} 
                                    alt={record.merchant_info.name || 'Store Logo'} 
                                    className="h-10 w-10 rounded-lg object-contain border border-gray-200 bg-white p-1 mr-3 shadow-sm"
                                  />
                                ) : (
                                  <div className="h-10 w-10 rounded-lg bg-coral-100 flex items-center justify-center mr-3">
                                    <span className="text-coral-500 font-semibold">
                                      {(record.merchant_info?.name || 'Unknown')[0]}
                                    </span>
                                  </div>
                                )}
                                <motion.div 
                                  whileHover={{ x: 2 }}
                                  className="text-sm font-medium text-gray-900 hover:text-coral-600 cursor-pointer group"
                                  onClick={() => record.merchant_info?.unique_name && handleStoreClick(record.merchant_info.unique_name)}
                                >
                                  {record.merchant_info?.name || 'Unknown Store'}
                                  {record.merchant_info?.unique_name && (
                                    <span className="ml-1 text-coral-500 opacity-0 group-hover:opacity-100 transition-opacity inline-flex items-center">
                                      <FaExternalLinkAlt className="h-3 w-3" />
                                    </span>
                                  )}
                                </motion.div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              {record.merchant_info?.cashback_value ? (
                                <div className="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-coral-100 text-coral-800">
                                  {record.merchant_info.cashback_value}
                                </div>
                              ) : (
                                <div className="text-sm text-gray-500">-</div>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {formatDateTime(record.click_date)}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-500 font-mono">
                                {record.click_id}
                              </div>
                            </td>
                          </motion.tr>
                        ))}
                      </AnimatePresence>
                    </tbody>
                  </table>
                </div>
              )}

              {/* 分页控件将在后续步骤优化 */}
              <div className="mt-4 flex justify-end p-4">
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1}
                    className={`relative inline-flex items-center px-4 py-2 rounded-l-md border text-sm font-medium ${
                      pagination.page === 1
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-gray-700 hover:bg-coral-50 hover:text-coral-600 border-gray-300'
                    }`}
                  >
                    <FaChevronLeft className="h-3 w-3 mr-1" />
                    Previous
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page * pagination.page_size >= pagination.total}
                    className={`relative inline-flex items-center px-4 py-2 rounded-r-md border text-sm font-medium ${
                      pagination.page * pagination.page_size >= pagination.total
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-gray-700 hover:bg-coral-50 hover:text-coral-600 border-gray-300'
                    }`}
                  >
                    Next
                    <FaChevronRight className="h-3 w-3 ml-1" />
                  </motion.button>
                </nav>
              </div>
            </>
          )}
        </motion.div>
        
        {/* 底部波浪装饰 */}
        <div className="w-full h-24 mt-12 relative overflow-hidden">
          <svg
            className="absolute bottom-0 w-full h-24"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1440 320"
            preserveAspectRatio="none"
          >
            <motion.path
              fill="rgba(255, 127, 80, 0.1)"
              d="M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
              initial={{ d: "M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z" }}
              animate={{ 
                d: [
                  "M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z",
                  "M0,224L48,213.3C96,203,192,181,288,186.7C384,192,480,224,576,234.7C672,245,768,235,864,208C960,181,1056,139,1152,138.7C1248,139,1344,181,1392,202.7L1440,224L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
                ] 
              }}
              transition={{ 
                duration: 15, 
                repeat: Infinity, 
                repeatType: "reverse",
                ease: "easeInOut" 
              }}
            />
            <motion.path
              fill="rgba(66, 153, 225, 0.1)"
              d="M0,256L48,261.3C96,267,192,277,288,261.3C384,245,480,203,576,197.3C672,192,768,224,864,213.3C960,203,1056,149,1152,128C1248,107,1344,117,1392,122.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
              initial={{ d: "M0,256L48,261.3C96,267,192,277,288,261.3C384,245,480,203,576,197.3C672,192,768,224,864,213.3C960,203,1056,149,1152,128C1248,107,1344,117,1392,122.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z" }}
              animate={{ 
                d: [
                  "M0,256L48,261.3C96,267,192,277,288,261.3C384,245,480,203,576,197.3C672,192,768,224,864,213.3C960,203,1056,149,1152,128C1248,107,1344,117,1392,122.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z",
                  "M0,288L48,282.7C96,277,192,267,288,234.7C384,203,480,149,576,154.7C672,160,768,224,864,224C960,224,1056,160,1152,149.3C1248,139,1344,181,1392,202.7L1440,224L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
                ] 
              }}
              transition={{ 
                duration: 18, 
                repeat: Infinity, 
                repeatType: "reverse",
                ease: "easeInOut",
                delay: 2
              }}
            />
          </svg>
        </div>
      </div>

      {/* 右下角装饰 */}
      <div className="fixed bottom-10 right-10 w-32 h-32 opacity-20 z-0 pointer-events-none">
        <motion.svg
          viewBox="0 0 200 200"
          xmlns="http://www.w3.org/2000/svg"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1, rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        >
          <path
            fill="#FF7F50"
            d="M47.7,-57.2C59.9,-45.8,67.1,-28.6,70.8,-9.8C74.5,9,74.8,29.5,65.2,43.2C55.7,56.9,36.3,63.9,17.8,65.9C-0.8,67.9,-18.5,65,-36.1,57.2C-53.7,49.4,-71.1,36.9,-76.1,20.1C-81.2,3.3,-73.8,-17.6,-62.3,-33.6C-50.8,-49.6,-35.2,-60.5,-18.6,-65.6C-2,-70.6,15.8,-69.8,31.5,-63.5C47.2,-57.3,60.8,-45.6,47.7,-57.2Z"
            transform="translate(100 100)"
          />
        </motion.svg>
      </div>
    </div>
  );
};

export default ShoppingTrips;
