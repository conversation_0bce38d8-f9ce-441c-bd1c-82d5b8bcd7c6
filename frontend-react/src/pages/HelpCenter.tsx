import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { FaChevronDown, FaSearch } from 'react-icons/fa'

const HelpCenter = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const [activeCategory, setActiveCategory] = useState('getting-started')
  const [openQuestions, setOpenQuestions] = useState<string[]>([])

  const categories = [
    { id: 'getting-started', name: 'Getting Started' },
    { id: 'account', name: 'Account & Settings' },
    { id: 'cashback', name: 'Cashback & Rewards' },
    { id: 'payments', name: 'Payments & Withdrawals' },
    { id: 'shopping', name: 'Shopping & Stores' },
    { id: 'technical', name: 'Technical Support' }
  ]

  const faqs = {
    'getting-started': [
      {
        id: 'gs1',
        question: 'What is BonusEarned?',
        answer: 'BonusEarned is a leading cashback platform that rewards you for shopping at your favorite online stores. We partner with thousands of retailers to offer you cashback on your purchases.'
      },
      {
        id: 'gs2',
        question: 'How does it work?',
        answer: `It's simple:
1. Create a free account
2. Browse our participating stores
3. Click through to the retailer via BonusEarned
4. Shop as usual
5. Earn cashback on your purchase`
      },
      {
        id: 'gs3',
        question: 'Is BonusEarned free to use?',
        answer: 'Yes, BonusEarned is completely free to use. We earn a commission from retailers when you make a purchase, and we share this commission with you as cashback.'
      }
    ],
    'account': [
      {
        id: 'acc1',
        question: 'How do I create an account?',
        answer: 'Click the "Sign Up" button in the top right corner of our website. You can create an account using your email address or sign up with your Google or Facebook account.'
      },
      {
        id: 'acc2',
        question: 'How do I reset my password?',
        answer: 'Click "Forgot Password" on the login page and follow the instructions sent to your email address. For security reasons, password reset links expire after 24 hours.'
      }
    ],
    'cashback': [
      {
        id: 'cb1',
        question: 'When will I receive my cashback?',
        answer: 'Cashback typically appears as pending within 48 hours of your purchase. The validation period varies by retailer but usually takes 30-90 days before the cashback becomes payable.'
      },
      {
        id: 'cb2',
        question: 'Why is my cashback pending?',
        answer: 'Cashback remains pending during the retailer\'s return and validation period. This ensures that the purchase is final and meets all cashback criteria before payment.'
      }
    ],
    'payments': [
      {
        id: 'pay1',
        question: 'Why hasn\'t my cashback tracked?',
        answer: 'Common reasons include:\n- Not clicking through BonusEarned before shopping\n- Using other discount codes or rewards programs\n- Items excluded from cashback\n- Ad blockers or privacy settings preventing tracking'
      },
      {
        id: 'pay2',
        question: 'How long do withdrawals take?',
        answer: 'Processing times vary by payment method:\n- PayPal: 1-3 business days\n- Bank Transfer: 3-5 business days\n- Gift Cards: Instant to 24 hours'
      }
    ],
    'shopping': [
      {
        id: 'shop1',
        question: 'Which stores offer cashback?',
        answer: 'We partner with thousands of popular retailers across various categories including fashion, electronics, travel, and more. View our complete store directory for the current list.'
      },
      {
        id: 'shop2',
        question: 'Why didn\'t I receive cashback for my purchase?',
        answer: 'Common reasons include:\n- Not clicking through BonusEarned before shopping\n- Using other discount codes or rewards programs\n- Items excluded from cashback\n- Ad blockers or privacy settings preventing tracking'
      }
    ],
    'technical': [
      {
        id: 'tech1',
        question: 'What browsers are supported?',
        answer: 'BonusEarned works with all major modern browsers including Chrome, Firefox, Safari, and Edge. For the best experience, please ensure your browser is up to date.'
      },
      {
        id: 'tech2',
        question: 'How do I enable cookies?',
        answer: 'Cookies must be enabled to track your cashback. Visit our browser guide for step-by-step instructions on enabling cookies for your specific browser.'
      }
    ]
  }

  const toggleQuestion = (id: string) => {
    setOpenQuestions(prev =>
      prev.includes(id) ? prev.filter(q => q !== id) : [...prev, id]
    )
  }

  const filteredFaqs = searchQuery
    ? Object.values(faqs).flat().filter(faq =>
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : faqs[activeCategory as keyof typeof faqs]

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto"
      >
        <h1 className="text-4xl font-bold text-gray-900 mb-8">Help Center</h1>
        <div className="bg-white rounded-xl shadow-sm p-8">
          <div className="space-y-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="relative"
            >
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search help articles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-3 pl-12 rounded-lg border border-gray-300 focus:border-purple-500 focus:ring-purple-500"
                />
                <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>
            </motion.div>

            {!searchQuery && (
              <div className="flex space-x-4 overflow-x-auto pb-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`px-4 py-2 rounded-full whitespace-nowrap ${
                      activeCategory === category.id
                        ? 'bg-gradient-to-r from-purple-500 to-pink-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
            )}

            <div className="space-y-4">
              {filteredFaqs.map((faq) => (
                <motion.div
                  key={faq.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border border-gray-200 rounded-lg overflow-hidden"
                >
                  <button
                    onClick={() => toggleQuestion(faq.id)}
                    className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50"
                  >
                    <span className="font-medium text-gray-900">{faq.question}</span>
                    <FaChevronDown
                      className={`transform transition-transform ${
                        openQuestions.includes(faq.id) ? 'rotate-180' : ''
                      }`}
                    />
                  </button>
                  <AnimatePresence>
                    {openQuestions.includes(faq.id) && (
                      <motion.div
                        initial={{ height: 0 }}
                        animate={{ height: 'auto' }}
                        exit={{ height: 0 }}
                        className="overflow-hidden"
                      >
                        <div className="px-6 py-4 bg-gray-50 whitespace-pre-line">
                          {faq.answer}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </div>

            <div className="mt-8 p-6 bg-gray-50 rounded-lg">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Still need help?</h2>
              <p className="text-gray-600 mb-4">
                Can't find what you're looking for? Our support team is here to help.
              </p>
              <a
                href="/contact"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
              >
                Contact Support
              </a>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default HelpCenter
