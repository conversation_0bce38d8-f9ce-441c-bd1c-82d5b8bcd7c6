import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FaArrowRight, FaQuestionCircle, FaCreditCard, FaLaptop, FaMobileAlt, FaShoppingBag } from 'react-icons/fa'

// 导入HowItWorks组件
import HowItWorksSection from '../components/landing/HowItWorks'

const HowItWorks: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* 使用HowItWorks组件 */}
      <HowItWorksSection />

      {/* 额外说明部分 */}
      <section className="py-16 px-4 bg-gray-900">
        <div className="max-w-6xl mx-auto">
          <motion.div 
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl font-bold mb-4 text-white">Frequently Asked Questions</h2>
            <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mb-6"></div>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Everything you need to know about our cashback platform and how to maximize your rewards
            </p>
          </motion.div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {[
              {
                icon: <FaQuestionCircle className="text-4xl text-pink-500" />,
                title: "What is cashback?",
                description: "Cashback is a reward program where a percentage of your purchase amount is returned to you. It's essentially getting paid to shop at your favorite stores."
              },
              {
                icon: <FaCreditCard className="text-4xl text-purple-500" />,
                title: "How do I get paid?",
                description: "You can withdraw your cashback balance via PayPal, bank transfer, or gift cards once you reach the minimum withdrawal threshold specified in your account settings."
              },
              {
                icon: <FaLaptop className="text-4xl text-pink-400" />,
                title: "How long does it take to receive cashback?",
                description: "Cashback typically appears as pending within 48 hours and becomes available for withdrawal after the store's return period (usually 30-90 days)."
              },
              {
                icon: <FaMobileAlt className="text-4xl text-purple-400" />,
                title: "Can I use the platform on mobile?",
                description: "Yes! Our platform is fully responsive and works on all devices. You can earn cashback whether you're shopping on desktop, tablet, or mobile phone."
              },
              {
                icon: <FaShoppingBag className="text-4xl text-pink-500" />,
                title: "Which stores can I shop at?",
                description: "We partner with thousands of popular retailers across various categories including fashion, electronics, travel, home goods, and more."
              },
              {
                icon: <FaArrowRight className="text-4xl text-purple-500" />,
                title: "How do I start?",
                description: "Simply create a free account, browse our participating stores, click through to the retailer via our platform, and shop as usual to earn cashback on your purchase."
              }
            ].map((item, index) => (
              <motion.div
                key={index}
                className="bg-gray-800 p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border border-gray-700 hover:border-purple-500/50"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="mb-4">{item.icon}</div>
                <h3 className="text-xl font-semibold mb-2 text-white">{item.title}</h3>
                <p className="text-gray-300">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* 注册号召 */}
      <section className="py-20 px-4 bg-gradient-to-r from-purple-800 to-pink-700 text-white relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 overflow-hidden opacity-20">
          <div className="absolute -left-10 -top-10 w-64 h-64 rounded-full bg-purple-500 filter blur-3xl"></div>
          <div className="absolute right-0 bottom-0 w-96 h-96 rounded-full bg-pink-600 filter blur-3xl"></div>
        </div>
        
        <div className="max-w-5xl mx-auto text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl font-bold mb-6">Ready to Start Earning?</h2>
            <p className="text-xl mb-10 max-w-3xl mx-auto">
              Join thousands of savvy shoppers who are already earning cashback on their everyday purchases.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link 
                to="/auth/register" 
                className="px-8 py-3 bg-white text-purple-700 font-semibold rounded-full shadow-lg hover:bg-gray-100 transition-colors"
              >
                Sign Up for Free
              </Link>
              <Link 
                to="/stores" 
                className="px-8 py-3 bg-transparent border-2 border-white text-white font-semibold rounded-full hover:bg-white/10 transition-colors"
              >
                Browse Stores
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default HowItWorks 