.coupons-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

@media (min-width: 768px) {
  .coupons-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.coupon-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
  height: 100%;
}

.coupon-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0;
}

.coupon-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 4px;
}

.coupon-type {
  color: #9F7AEA;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cashback-badge {
  color: #9F7AEA;
  font-weight: 600;
  font-size: 13px;
  padding: 4px 8px;
  border-radius: 6px;
  background: rgba(159, 122, 234, 0.1);
  display: inline-flex;
  align-items: center;
}

.coupon-title {
  font-size: 16px;
  color: #333;
  font-weight: 600;
  line-height: 1.4;
}

.coupon-code-section {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.coupon-code-button {
  display: flex;
  align-items: stretch;
  background: #f5f5f5;
  border: 1px solid #9F7AEA;
  border-radius: 8px;
  padding: 0;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  min-height: 40px;
  box-shadow: 0 2px 4px rgba(159, 122, 234, 0.1);
}

.coupon-code-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(159, 122, 234, 0.2);
}

.coupon-code-button:active {
  transform: translateY(0);
}

.coupon-code-button .code-text {
  padding: 10px 12px;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  letter-spacing: 1px;
  background: #fff;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coupon-code-button .get-code-text {
  padding: 10px 12px;
  font-size: 14px;
  font-weight: 600;
  color: white;
  border-top-right-radius: 7px;
  border-bottom-right-radius: 7px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(5px);
}

.modal {
  background: white;
  border-radius: 16px;
  padding: 24px;
  width: 95%;
  max-width: 480px;
  position: relative;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.close-button {
  position: absolute;
  right: 16px;
  top: 16px;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 10;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: rgba(0,0,0,0.05);
  color: #333;
}

.close-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modal-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.merchant-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 12px;
}

.merchant-logo {
  width: 80px;
  height: 80px;
  object-fit: contain;
  border-radius: 12px;
  background: white;
  padding: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
}

.merchant-info h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.share-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.share-info h3 {
  font-size: 1.25rem;
  font-weight: 500;
  color: #4B5563;
  text-align: center;
  margin: 0;
}

.share-url {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.share-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  font-size: 14px;
  color: #374151;
  background: #F9FAFB;
  transition: all 0.2s;
}

.share-input:focus {
  outline: none;
  border-color: #9F7AEA;
  box-shadow: 0 0 0 2px rgba(159, 122, 234, 0.2);
}

.copy-button {
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.copy-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.countdown {
  display: inline-flex;
  align-items: center;
}

.coupon-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.coupon-title {
  color: #1F2937;
  text-align: center;
}

.code-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.code-display {
  width: 100%;
}

.coupon-code {
  letter-spacing: 1px;
}

.coupon-code-container {
  position: relative;
}

.instructions {
  border-left: 3px solid #DBEAFE;
}

.instructions h4 {
  color: #1E40AF;
  font-size: 16px;
}

.instructions ol {
  padding-left: 1.5rem;
}

.instructions li {
  margin-bottom: 0.5rem;
}

.description {
  border-left: 3px solid #F3F4F6;
}

.description h4 {
  color: #374151;
  font-size: 16px;
}

.description p {
  color: #6B7280;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.shop-now-button {
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
}

.shop-now-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  animation: slideUp 0.3s ease-out forwards;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.track-url-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #E5E7EB;
  border-radius: 8px 0 0 8px;
  font-size: 14px;
  color: #374151;
  background: #F9FAFB;
  transition: all 0.2s;
}

.track-url-input:focus {
  outline: none;
  border-color: #9F7AEA;
  box-shadow: 0 0 0 2px rgba(159, 122, 234, 0.2);
}

.track-url-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4B5563;
}

/* 模态框按钮 */
.modal-content button.shop-now-button {
  background: linear-gradient(to right, #9F7AEA, #ED64A6);
  color: white;
  transition: all 0.3s;
}

.modal-content button.shop-now-button:hover {
  background: linear-gradient(to right, #805AD5, #D53F8C);
  transform: translateY(-1px);
}
