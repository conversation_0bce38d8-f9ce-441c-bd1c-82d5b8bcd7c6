import React, { useState, useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import api from '../../lib/api'
import { motion, AnimatePresence } from 'framer-motion'
import { FaChevronLeft, FaChevronRight, FaShoppingBag, FaRegClock, FaDollarSign, FaCheckCircle } from 'react-icons/fa'

// 订单状态枚举
enum OrderStatus {
  All = 0,
  Pending = 1,  // 待确认，默认订单为待确认
  Approved = 2, // 已确认，当上级联盟已经支付该订单时，才为已确认
  Rejected = 3, // 已取消，当上级联盟取消该订单时，为已取消
  Paying = 4,   // 支付中，不需要用户发起提现请求，规定好时间自动从已确认变为支付中
  Paid = 5,     // 已结算，当自己平台支付该订单时，为已结算
}

// 订单状态配置
const orderStatusConfig = [
  { value: OrderStatus.All, label: 'All', color: 'bg-gray-100 text-gray-800' },
  { value: OrderStatus.Pending, label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  { value: OrderStatus.Approved, label: 'Approved', color: 'bg-green-100 text-green-800' },
  { value: OrderStatus.Rejected, label: 'Rejected', color: 'bg-red-100 text-red-800' },
  { value: OrderStatus.Paying, label: 'Paying', color: 'bg-purple-100 text-purple-800' },
  { value: OrderStatus.Paid, label: 'Paid', color: 'bg-blue-100 text-blue-800' },
];

interface Order {
  conversion_id: string
  user_code: string
  merchant_info: {
    id: number
    name: string
    unique_name: string
    merchant_code: string
    logo: string
    website: string
    track_url: string
    cashback_type: number
    cashback_value: string
    description: string
    category: {
      id: number
      name: string
      icon: string
    }
    featured: boolean
    country: string
    supported_countries: string[] | null
    created_at: string
    updated_at: string
  }
  merchant_code: string
  order_id: string
  click_id: string
  order_amount: string
  cashback_amount: string
  status: string
  order_time: string | null
  approve_time: string | null
  cancel_time: string | null
  paid_time: string | null
  cancel_reason: string
}

interface OrdersResponse {
  total: number
  page: number
  page_size: number
  order_list: Order[] | null
}

interface Balance {
  approved_amount: number
  paid_amount: number
  pending_amount: number
  total_amount: number
}

interface ProfileResponse {
  user_balance: Balance
}

const Orders = () => {
  const { user, loading: authLoading } = useAuth()
  const navigate = useNavigate()
  const [orders, setOrders] = useState<Order[]>([])
  const [balance, setBalance] = useState<Balance | null>(null)
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus>(OrderStatus.All)
  const [pagination, setPagination] = useState({
    current_page: 1,
    page_size: 10,
    total: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!authLoading && !user) {
      navigate('/auth/login')
      return
    }

    if (!authLoading && user) {
      window.scrollTo({
        top: 0,
        behavior: 'instant'
      })
      handleStatusChange(OrderStatus.All)
      fetchBalance()
    }
  }, [authLoading, user, navigate])

  const handleStatusChange = async (status: OrderStatus) => {
    setSelectedStatus(status);
    setPagination(prev => ({ ...prev, current_page: 1 }));
    try {
      setLoading(true);
      const params: any = {
        page: 1,
        page_size: pagination.page_size
      };
      
      if (status !== OrderStatus.All && status > 0) {
        params.status = status;
      }

      const response = await api.get<OrdersResponse>('/orders', { params });
      
      if (response.order_list) {
        setOrders(response.order_list);
        setPagination(prev => ({
          ...prev,
          current_page: response.page,
          total: response.total
        }));
      } else {
        setOrders([]);
        setPagination(prev => ({
          ...prev,
          total: 0
        }));
      }
    } catch (error) {
      setError('Failed to load orders');
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = async (page: number) => {
    window.scrollTo({
      top: 0,
      behavior: 'instant'
    });
    try {
      setLoading(true);
      const params: any = {
        page,
        page_size: pagination.page_size
      };
      
      if (selectedStatus !== OrderStatus.All && selectedStatus > 0) {
        params.status = selectedStatus;
      }

      const response = await api.get<OrdersResponse>('/orders', { params });
      
      if (response.order_list) {
        setOrders(response.order_list);
        setPagination(prev => ({
          ...prev,
          current_page: response.page,
          total: response.total
        }));
      } else {
        setOrders([]);
        setPagination(prev => ({
          ...prev,
          total: 0
        }));
      }
    } catch (error) {
      setError('Failed to load orders');
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchBalance = async () => {
    try {
      const response = await api.get<ProfileResponse>('/users/me/profile')
      if (response.user_balance) {
        setBalance(response.user_balance)
      }
    } catch (error) {
      console.error('Error loading balance:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      case 'paying':
        return 'bg-purple-100 text-purple-800'
      case 'paid':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusDisplay = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending'
      case 'approved':
        return 'Approved'
      case 'rejected':
        return 'Rejected'
      case 'paying':
        return 'Paying'
      case 'paid':
        return 'Paid'
      default:
        return status
    }
  }

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 relative">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
          className="w-16 h-16 rounded-full border-4 border-gray-200 border-t-coral-500"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-coral-50 to-transparent opacity-50"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-gray-50 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-coral-50 to-transparent opacity-50"></div>
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="relative z-10 bg-white p-8 rounded-xl shadow-lg max-w-md w-full mx-4"
        >
          <div className="flex flex-col items-center">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
              className="w-12 h-12 rounded-full border-4 border-gray-200 border-t-coral-500 mb-4"
            />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Orders</h3>
            <p className="text-gray-500 text-center text-sm">Please wait while we fetch your cashback history...</p>
          </div>
        </motion.div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-gray-50 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-coral-50 to-transparent opacity-50"></div>
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="relative z-10 bg-white p-8 rounded-xl shadow-lg max-w-md w-full mx-4"
        >
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to Load Orders</h3>
            <p className="text-gray-500 text-center mb-6">{error}</p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => handleStatusChange(OrderStatus.All)}
              className="px-4 py-2 bg-coral-500 text-white rounded-lg font-medium shadow-sm"
            >
              Try Again
            </motion.button>
          </div>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 right-0 h-48 bg-gradient-to-b from-coral-50 to-transparent"></div>
      <div className="absolute -top-20 -right-20 w-64 h-64 rounded-full bg-coral-100 opacity-30 blur-3xl"></div>
      <div className="absolute top-40 -left-20 w-80 h-80 rounded-full bg-blue-100 opacity-20 blur-3xl"></div>
      
      {/* 浮动粒子效果 */}
      {[...Array(10)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-coral-400"
          style={{
            width: Math.random() * 8 + 4,
            height: Math.random() * 8 + 4,
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            opacity: 0.1,
          }}
          animate={{
            y: [0, -100],
            x: [0, Math.random() * 50 - 25],
            opacity: [0, 0.2, 0],
          }}
          transition={{
            duration: Math.random() * 10 + 10,
            repeat: Infinity,
            delay: Math.random() * 5,
          }}
        />
      ))}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* 页面标题 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900">Orders & Cashback</h1>
          <p className="text-gray-600 mt-2">Track your purchases and earned cashback</p>
        </motion.div>
        
        {/* Balance Section */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-white rounded-xl shadow-sm overflow-hidden mb-8 relative"
        >
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-coral-400 via-coral-500 to-coral-400"></div>
          <div className="p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-5 flex items-center">
              <FaDollarSign className="mr-2 text-coral-500" />
              Account Balance
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-5">
              <motion.div 
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-5 shadow-sm border border-gray-100 relative overflow-hidden"
              >
                <div className="absolute bottom-0 right-0 w-16 h-16 -mb-6 -mr-6 rounded-full bg-gray-200 opacity-50"></div>
                <div className="relative z-10">
                  <div className="text-sm font-medium text-gray-500 mb-1">Total Amount</div>
                  <div className="text-2xl font-bold text-gray-900">${balance?.total_amount.toFixed(2) || '0.00'}</div>
                </div>
              </motion.div>
              
              <motion.div 
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-5 shadow-sm border border-green-100 relative overflow-hidden"
              >
                <div className="absolute bottom-0 right-0 w-16 h-16 -mb-6 -mr-6 rounded-full bg-green-200 opacity-50"></div>
                <div className="relative z-10">
                  <div className="text-sm font-medium text-green-600 mb-1">Approved Amount</div>
                  <div className="text-2xl font-bold text-green-600">${balance?.approved_amount.toFixed(2) || '0.00'}</div>
                </div>
              </motion.div>
              
              <motion.div 
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-5 shadow-sm border border-yellow-100 relative overflow-hidden"
              >
                <div className="absolute bottom-0 right-0 w-16 h-16 -mb-6 -mr-6 rounded-full bg-yellow-200 opacity-50"></div>
                <div className="relative z-10">
                  <div className="text-sm font-medium text-yellow-600 mb-1">Pending Amount</div>
                  <div className="text-2xl font-bold text-yellow-600">${balance?.pending_amount.toFixed(2) || '0.00'}</div>
                </div>
              </motion.div>
              
              <motion.div 
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-5 shadow-sm border border-blue-100 relative overflow-hidden"
              >
                <div className="absolute bottom-0 right-0 w-16 h-16 -mb-6 -mr-6 rounded-full bg-blue-200 opacity-50"></div>
                <div className="relative z-10">
                  <div className="text-sm font-medium text-blue-600 mb-1">Paid Amount</div>
                  <div className="text-2xl font-bold text-blue-600">${balance?.paid_amount.toFixed(2) || '0.00'}</div>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Status Filter */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-white rounded-xl shadow-sm p-6 mb-8 relative overflow-hidden"
        >
          <div className="absolute top-0 right-0 w-40 h-40 -mt-20 -mr-20 rounded-full bg-coral-50"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 -mb-12 -ml-12 rounded-full bg-blue-50"></div>
          
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <FaRegClock className="mr-2 text-coral-500" />
            Filter by Status
          </h3>
          
          <div className="flex flex-wrap gap-3 relative z-10">
            {orderStatusConfig.map((status, index) => (
              <motion.button
                key={status.value}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ 
                  duration: 0.3, 
                  delay: 0.1 * index,
                  type: "spring",
                  stiffness: 300 
                }}
                onClick={() => handleStatusChange(status.value)}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  selectedStatus === status.value
                    ? `${status.color} ring-2 ring-offset-2 ring-coral-500 shadow-md`
                    : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200 shadow-sm'
                }`}
              >
                {status.label}
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Orders Section */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="bg-white rounded-xl shadow-sm overflow-hidden relative"
        >
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 via-coral-500 to-blue-400"></div>
          
          {(!orders || orders.length === 0) ? (
            <div className="text-center py-20">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                <div className="mx-auto w-20 h-20 bg-coral-100 rounded-full flex items-center justify-center mb-4">
                  <FaShoppingBag className="text-coral-500 text-2xl" />
                </div>
                <h3 className="text-xl font-medium text-gray-900 mb-2">No Cashback History Yet</h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">Start shopping through our platform to earn cashback on your purchases!</p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => navigate('/stores')}
                  className="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-coral-500 hover:bg-coral-600 focus:outline-none"
                >
                  Browse Stores
                </motion.button>
              </motion.div>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Store
                      </th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order ID
                      </th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order Time
                      </th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order Amount
                      </th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Cashback
                      </th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    <AnimatePresence>
                      {orders.map((order, index) => (
                        <motion.tr 
                          key={order.conversion_id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.05 }}
                          exit={{ opacity: 0 }}
                          className="hover:bg-gray-50 transition-colors"
                        >
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {order.merchant_info?.logo ? (
                                <motion.img 
                                  whileHover={{ scale: 1.1, rotate: 5 }}
                                  src={order.merchant_info.logo} 
                                  alt={order.merchant_info.name} 
                                  className="h-10 w-10 rounded-lg object-contain border border-gray-200 bg-white p-1 mr-3 shadow-sm"
                                />
                              ) : (
                                <div className="h-10 w-10 rounded-lg bg-coral-100 flex items-center justify-center mr-3">
                                  <span className="text-coral-500 font-semibold">
                                    {(order.merchant_info?.name || 'Unknown')[0]}
                                  </span>
                                </div>
                              )}
                              <motion.div 
                                whileHover={{ x: 2 }}
                                className="text-sm font-medium text-gray-900 hover:text-coral-600 cursor-pointer"
                                onClick={() => order.merchant_info?.unique_name && navigate(`/stores/${order.merchant_info.unique_name}`)}
                              >
                                {order.merchant_info?.name || 'Unknown Store'}
                              </motion.div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500 font-mono">{order.order_id}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">
                              {order.order_time ? new Date(order.order_time).toLocaleDateString(undefined, {year: 'numeric', month: 'short', day: 'numeric'}) : '-'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">${parseFloat(order.order_amount).toFixed(2)}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-coral-600">${parseFloat(order.cashback_amount).toFixed(2)}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(order.status)}`}>
                              {getStatusDisplay(order.status)}
                            </span>
                          </td>
                        </motion.tr>
                      ))}
                    </AnimatePresence>
                  </tbody>
                </table>
              </div>

              {/* Pagination - Updated */}
              <div className="bg-gray-50 px-6 py-4 flex items-center justify-between border-t border-gray-200">
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Showing{' '}
                      <span className="font-medium">
                        {(pagination.current_page - 1) * pagination.page_size + 1}
                      </span>{' '}
                      to{' '}
                      <span className="font-medium">
                        {Math.min(pagination.current_page * pagination.page_size, pagination.total)}
                      </span>{' '}
                      of <span className="font-medium">{pagination.total}</span> results
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handlePageChange(pagination.current_page - 1)}
                        disabled={pagination.current_page === 1}
                        className={`relative inline-flex items-center px-4 py-2 rounded-l-md border text-sm font-medium ${
                          pagination.current_page === 1
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-white text-gray-700 hover:bg-coral-50 hover:text-coral-600 border-gray-300'
                        }`}
                      >
                        <FaChevronLeft className="h-3 w-3 mr-1" />
                        Previous
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handlePageChange(pagination.current_page + 1)}
                        disabled={pagination.current_page * pagination.page_size >= pagination.total}
                        className={`relative inline-flex items-center px-4 py-2 rounded-r-md border text-sm font-medium ${
                          pagination.current_page * pagination.page_size >= pagination.total
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-white text-gray-700 hover:bg-coral-50 hover:text-coral-600 border-gray-300'
                        }`}
                      >
                        Next
                        <FaChevronRight className="h-3 w-3 ml-1" />
                      </motion.button>
                    </nav>
                  </div>
                </div>
                <div className="flex sm:hidden justify-between w-full">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handlePageChange(pagination.current_page - 1)}
                    disabled={pagination.current_page === 1}
                    className={`relative inline-flex items-center px-4 py-2 rounded-md border text-sm font-medium ${
                      pagination.current_page === 1
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-gray-700 hover:bg-coral-50 hover:text-coral-600 border-gray-300'
                    }`}
                  >
                    <FaChevronLeft className="h-3 w-3 mr-1" />
                    Previous
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handlePageChange(pagination.current_page + 1)}
                    disabled={pagination.current_page * pagination.page_size >= pagination.total}
                    className={`relative inline-flex items-center px-4 py-2 rounded-md border text-sm font-medium ${
                      pagination.current_page * pagination.page_size >= pagination.total
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-gray-700 hover:bg-coral-50 hover:text-coral-600 border-gray-300'
                    }`}
                  >
                    Next
                    <FaChevronRight className="h-3 w-3 ml-1" />
                  </motion.button>
                </div>
              </div>
            </>
          )}
        </motion.div>

        {/* 底部波浪装饰 */}
        <div className="w-full h-24 mt-12 relative overflow-hidden">
          <svg
            className="absolute bottom-0 w-full h-24"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1440 320"
            preserveAspectRatio="none"
          >
            <motion.path
              fill="rgba(255, 127, 80, 0.1)"
              d="M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
              initial={{ d: "M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z" }}
              animate={{ 
                d: [
                  "M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z",
                  "M0,224L48,213.3C96,203,192,181,288,186.7C384,192,480,224,576,234.7C672,245,768,235,864,208C960,181,1056,139,1152,138.7C1248,139,1344,181,1392,202.7L1440,224L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
                ] 
              }}
              transition={{ 
                duration: 15, 
                repeat: Infinity, 
                repeatType: "reverse",
                ease: "easeInOut" 
              }}
            />
            <motion.path
              fill="rgba(66, 153, 225, 0.1)"
              d="M0,256L48,261.3C96,267,192,277,288,261.3C384,245,480,203,576,197.3C672,192,768,224,864,213.3C960,203,1056,149,1152,128C1248,107,1344,117,1392,122.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
              initial={{ d: "M0,256L48,261.3C96,267,192,277,288,261.3C384,245,480,203,576,197.3C672,192,768,224,864,213.3C960,203,1056,149,1152,128C1248,107,1344,117,1392,122.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z" }}
              animate={{ 
                d: [
                  "M0,256L48,261.3C96,267,192,277,288,261.3C384,245,480,203,576,197.3C672,192,768,224,864,213.3C960,203,1056,149,1152,128C1248,107,1344,117,1392,122.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z",
                  "M0,288L48,282.7C96,277,192,267,288,234.7C384,203,480,149,576,154.7C672,160,768,224,864,224C960,224,1056,160,1152,149.3C1248,139,1344,181,1392,202.7L1440,224L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
                ] 
              }}
              transition={{ 
                duration: 18, 
                repeat: Infinity, 
                repeatType: "reverse",
                ease: "easeInOut",
                delay: 2
              }}
            />
          </svg>
        </div>
      </div>
      
      {/* 右下角装饰 */}
      <div className="fixed bottom-10 right-10 w-32 h-32 opacity-20 z-0 pointer-events-none">
        <motion.svg
          viewBox="0 0 200 200"
          xmlns="http://www.w3.org/2000/svg"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1, rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        >
          <path
            fill="#FF7F50"
            d="M47.7,-57.2C59.9,-45.8,67.1,-28.6,70.8,-9.8C74.5,9,74.8,29.5,65.2,43.2C55.7,56.9,36.3,63.9,17.8,65.9C-0.8,67.9,-18.5,65,-36.1,57.2C-53.7,49.4,-71.1,36.9,-76.1,20.1C-81.2,3.3,-73.8,-17.6,-62.3,-33.6C-50.8,-49.6,-35.2,-60.5,-18.6,-65.6C-2,-70.6,15.8,-69.8,31.5,-63.5C47.2,-57.3,60.8,-45.6,47.7,-57.2Z"
            transform="translate(100 100)"
          />
        </motion.svg>
      </div>
    </div>
  )
}

export default Orders
