import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import * as FaIcons from 'react-icons/fa'
import { useStore } from '../../contexts/StoreContext'
import { Category } from '../../types'

const footerLinks = {
  Shop: [
    { label: 'Featured Stores', href: '/stores?featured=true' },
    { label: 'Popular Brands', href: '/stores?sort=popular' },
    { label: 'All Stores', href: '/stores' },
    { label: 'Top Cashback Offers', href: '/stores?sort=cashback' }
  ],
  Support: [
    { label: 'Help Center', href: '/help' },
    { label: 'How It Works', href: '/how-it-works' },
    { label: 'Contact Us', href: '/contact' }
  ],
  Legal: [
    { label: 'Terms of Service', href: '/terms' },
    { label: 'Privacy Policy', href: '/privacy' },
    { label: 'Cookie Policy', href: '/privacy#cookies' }
  ],
  Company: [
    { label: 'About Us', href: '/about' }
  ]
}

const Footer = () => {
  const currentYear = new Date().getFullYear()
  const { getCategories } = useStore()
  const [categories, setCategories] = useState<Category[]>([])

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categoriesData = await getCategories()
        setCategories(categoriesData)
      } catch (error) {
        console.error('Failed to fetch categories:', error)
      }
    }

    fetchCategories()
  }, [getCategories])

  return (
    <footer className="bg-white border-t mt-auto">
      <div className="container mx-auto py-10">

        {/* Main Footer Links with Popular Categories */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8 px-4 mb-8">
          {/* Shop Section */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Shop</h3>
            <ul className="space-y-3">
              {footerLinks.Shop.map((link) => (
                <li key={link.label}>
                  <Link
                    to={link.href}
                    className="text-gray-600 hover:text-purple-500 transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Popular Categories Section */}
          <div className="col-span-2 md:col-span-1 lg:col-span-2">
            <h3 className="font-semibold text-gray-900 mb-4">Popular Categories</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-2">
              {categories.length > 0 ? (
                categories.slice(0, 8).map((category) => (
                  <Link
                    key={category.id}
                    to={`/stores?category_id=${category.id}`}
                    className="flex items-center text-gray-600 hover:text-purple-500 transition-colors"
                  >
                    <span className="mr-2">
                      {category.icon && (FaIcons as any)[category.icon] ?
                        React.createElement((FaIcons as any)[category.icon], { className: "mr-1" }) :
                        <span className="w-4 h-4 inline-block mr-1">•</span>}
                    </span>
                    <span>{category.name}</span>
                  </Link>
                ))
              ) : (
                <div className="text-gray-500">Loading categories...</div>
              )}
            </div>
          </div>

          {/* Support Section */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Support</h3>
            <ul className="space-y-3">
              {footerLinks.Support.map((link) => (
                <li key={link.label}>
                  <Link
                    to={link.href}
                    className="text-gray-600 hover:text-purple-500 transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Section */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Legal</h3>
            <ul className="space-y-3">
              {footerLinks.Legal.map((link) => (
                <li key={link.label}>
                  <Link
                    to={link.href}
                    className="text-gray-600 hover:text-purple-500 transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Section */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Company</h3>
            <ul className="space-y-3">
              {footerLinks.Company.map((link) => (
                <li key={link.label}>
                  <Link
                    to={link.href}
                    className="text-gray-600 hover:text-purple-500 transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Social Media & About */}
        <div className="border-t border-gray-200 pt-6 pb-4 px-4">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">About BonusEarned</h3>
              <p className="text-gray-600 mb-4">
                BonusEarned has been a trusted cashback partner since its founding, offering over 5,000 brands with amazing cashback deals to all members. Join for free and maximize your savings with every purchase.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Why Shop with BonusEarned?</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="text-purple-500 mr-2">✓</span>
                  <span className="text-gray-600">Thousands of deals updated daily</span>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-500 mr-2">✓</span>
                  <span className="text-gray-600">Best cashback rates from top brands</span>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-500 mr-2">✓</span>
                  <span className="text-gray-600">Exclusive coupon codes not available elsewhere</span>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-500 mr-2">✓</span>
                  <span className="text-gray-600">Fast payouts via PayPal or direct deposit</span>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-500 mr-2">✓</span>
                  <span className="text-gray-600">100% free to join and use</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-200 pt-6 px-4">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-col space-y-1">
              <div className="flex items-center">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 128 128"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="mr-2"
                >
                  {/* 定义渐变 */}
                  <defs>
                    <linearGradient id="footerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#8B5CF6" />
                      <stop offset="100%" stopColor="#EC4899" />
                    </linearGradient>
                  </defs>

                  {/* 紫色/粉色渐变背景 */}
                  <circle cx="64" cy="64" r="60" fill="url(#footerGradient)" />

                  {/* 装饰元素 - 小圆点 */}
                  <circle cx="34" cy="44" r="3" fill="#FFFFFF" opacity="0.7" />
                  <circle cx="94" cy="84" r="2" fill="#FFFFFF" opacity="0.5" />

                  {/* 完全居中且圆润的字母B */}
                  <path d="M64,34
                          C76,34 84,42 84,52
                          C84,59 80,64 75,66
                          C82,68 86,74 86,82
                          C86,94 76,100 64,100
                          L47,100
                          C45.5,100 44,98.5 44,97
                          L44,37
                          C44,35.5 45.5,34 47,34
                          L64,34 Z

                          M62,62
                          C67,62 70,59 70,54
                          C70,49 67,46 62,46
                          L58,46
                          L58,62
                          L62,62 Z

                          M64,88
                          C69,88 72,85 72,80
                          C72,75 69,71 64,71
                          L58,71
                          L58,88
                          L64,88 Z"
                      fill="#FFFFFF" />

                  {/* 光泽效果 */}
                  <circle cx="64" cy="64" r="24" fill="white" opacity="0.1" />
                </svg>
                <span className="font-bold bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-400 bg-clip-text text-transparent">BonusEarned</span>
              </div>
              <div className="text-sm text-gray-600">
                © {currentYear} BonusEarned. All Rights Reserved.
              </div>
            </div>
            <div className="text-xs text-gray-500 max-w-2xl text-center md:text-right">
              DISCLOSURE: Cash back rates and amounts are subject to change and may vary.
              <br />This site uses cookies to provide you with a better experience.
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
