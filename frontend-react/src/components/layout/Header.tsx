import React, { useState, useEffect, useRef } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { useStore } from '../../contexts/StoreContext'
import { motion, AnimatePresence } from 'framer-motion'
import { toast } from 'react-toastify'
import api from '../../lib/api'
import logoSvg from '../../assets/logo.svg'
import * as FaIcons from 'react-icons/fa'
import CountrySelector from '../CountrySelector'

interface Category {
  id: number
  name: string
  slug: string
  icon?: string
}

export default function Header() {
  const navigate = useNavigate()
  const { user, logout } = useAuth()
  const { getCategories } = useStore()
  const [isScrolled, setIsScrolled] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showCategoryMenu, setShowCategoryMenu] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [loadingCategories, setLoadingCategories] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)
  const categoryMenuRef = useRef<HTMLDivElement>(null)
  const searchTimeoutRef = useRef<NodeJS.Timeout>()

  // 获取动态渲染图标组件的函数
  const getCategoryIcon = (iconName: string | undefined) => {
    if (!iconName) return null;
    const IconComponent = (FaIcons as any)[iconName];
    return IconComponent ? <IconComponent className="w-4 h-4" /> : null;
  };

  const [showAllCategories, setShowAllCategories] = useState(false);
  const visibleCategoriesCount = 10; // 默认显示的分类数量

  // 获取需要显示的分类
  const getVisibleCategories = () => {
    if (showAllCategories) {
      return categories;
    }
    return categories.slice(0, visibleCategoriesCount);
  };

  // 处理滚动
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // 加载分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoadingCategories(true)
        const data = await getCategories()
        setCategories(data)
      } catch (error) {
        console.error('Failed to fetch categories:', error)
        toast.error('Failed to load categories')
      } finally {
        setLoadingCategories(false)
      }
    }
    fetchCategories()
  }, [getCategories])

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }
      if (categoryMenuRef.current && !categoryMenuRef.current.contains(event.target as Node)) {
        setShowCategoryMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // 处理搜索
  const handleSearch = async (query: string) => {
    setSearchQuery(query)

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }

    searchTimeoutRef.current = setTimeout(async () => {
      if (query.trim()) {
        setIsSearching(true)
        try {
          await api.get('/merchants', {
            params: {
              search: query.trim(),
              page: 1,
              page_size: 10
            }
          })
          navigate(`/stores?search=${encodeURIComponent(query.trim())}`)
        } catch (error) {
          console.error('Search error:', error)
          toast.error('Search failed. Please try again.')
        } finally {
          setIsSearching(false)
        }
      }
    }, 300)
  }

  // 处理分类点击
  const handleCategoryClick = (categoryId: number) => {
    navigate(`/stores?category_id=${categoryId}`)
    setShowCategoryMenu(false)
  }

  return (
    <header
      className={`fixed top-0 left-0 right-0 bg-white z-50 transition-all duration-300 ${
        isScrolled ? 'border-b border-coral-100' : ''
      }`}
    >
      <div className="container mx-auto">
        <nav className="flex items-center justify-between h-16 px-4">
          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="lg:hidden p-2 rounded-md text-gray-600 hover:text-coral-600 hover:bg-coral-50"
          >
            <svg
              className="h-6 w-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              {isMobileMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>

          {/* Logo */}
          <Link to="/home" className="flex items-center">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="h-12 flex items-center"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            >
              <div className="flex items-center">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 128 128"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="mr-2 flex-shrink-0"
                >
                  {/* 定义渐变 */}
                  <defs>
                    <linearGradient id="navGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#8B5CF6" />
                      <stop offset="100%" stopColor="#EC4899" />
                    </linearGradient>
                  </defs>

                  {/* 紫色/粉色渐变背景 */}
                  <circle cx="64" cy="64" r="60" fill="url(#navGradient)" />

                  {/* 装饰元素 - 小圆点 */}
                  <circle cx="34" cy="44" r="3" fill="#FFFFFF" opacity="0.7" />
                  <circle cx="94" cy="84" r="2" fill="#FFFFFF" opacity="0.5" />

                  {/* 完全居中且圆润的字母B */}
                  <path d="M64,34
                          C76,34 84,42 84,52
                          C84,59 80,64 75,66
                          C82,68 86,74 86,82
                          C86,94 76,100 64,100
                          L47,100
                          C45.5,100 44,98.5 44,97
                          L44,37
                          C44,35.5 45.5,34 47,34
                          L64,34 Z

                          M62,62
                          C67,62 70,59 70,54
                          C70,49 67,46 62,46
                          L58,46
                          L58,62
                          L62,62 Z

                          M64,88
                          C69,88 72,85 72,80
                          C72,75 69,71 64,71
                          L58,71
                          L58,88
                          L64,88 Z"
                      fill="#FFFFFF" />

                  {/* 光泽效果 */}
                  <circle cx="64" cy="64" r="24" fill="white" opacity="0.1" />
                </svg>
                <div
                  className="text-2xl font-bold text-purple-800"
                  style={{
                    fontFamily: "'Arial Rounded MT Bold', 'Segoe UI', 'Helvetica Neue', sans-serif",
                    letterSpacing: "0.5px",
                    fontWeight: "700",
                    paddingTop: "2px",
                    lineHeight: "1.2"
                  }}
                >
                  <motion.span
                    animate={{
                      y: [0, -1, 0, 1, 0],
                      scale: [1, 1.02, 1, 0.98, 1]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    BonusEarned
                  </motion.span>
                </div>
              </div>
            </motion.div>
          </Link>

          {/* Search Bar - Hidden on mobile, shown in mobile menu */}
          <div className="hidden lg:block flex-1 max-w-2xl mx-8">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                placeholder="Search stores..."
                className="w-full pl-10 pr-4 py-2 border border-purple-200 rounded-lg focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                {isSearching ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="w-5 h-5 border-2 border-purple-500 border-t-transparent rounded-full"
                  />
                ) : (
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                )}
              </div>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-4">
            {/* Country Selector */}
            <CountrySelector />

            {/* Coupons */}
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                to="/coupons"
                className="text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
              >
                Coupons
              </Link>
            </motion.div>

            {/* Categories */}
            <div className="relative" ref={categoryMenuRef}>
              <button
                onClick={() => setShowCategoryMenu(!showCategoryMenu)}
                className="text-gray-700 hover:text-purple-500 transition-colors px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-1"
              >
                <span>Categories</span>
                <svg
                  className={`w-4 h-4 transition-transform duration-200 ${showCategoryMenu ? 'transform rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <AnimatePresence>
                {showCategoryMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    className="absolute top-full right-0 mt-2 bg-white rounded-lg shadow-lg py-2 z-50 overflow-hidden"
                    style={{
                      width: categories.length > 15 ? '500px' : categories.length > 8 ? '350px' : '200px',
                      maxHeight: '80vh'
                    }}
                  >
                    {loadingCategories ? (
                      <div className="px-4 py-2 text-gray-500">Loading...</div>
                    ) : categories.length === 0 ? (
                      <div className="px-4 py-2 text-gray-500">No categories found</div>
                    ) : (
                      <>
                        <div className={`max-h-[70vh] overflow-y-auto custom-scrollbar px-2 ${
                          categories.length > 15 ? 'grid grid-cols-3 gap-1' :
                          categories.length > 8 ? 'grid grid-cols-2 gap-1' : ''
                        }`}>
                          {getVisibleCategories().map((category) => (
                            <button
                              key={category.id}
                              onClick={() => handleCategoryClick(category.id)}
                              className="w-full text-left px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-500 transition-colors rounded-md truncate flex items-center gap-2"
                            >
                              <span className="flex-shrink-0 w-5 h-5 flex items-center justify-center opacity-80 text-purple-500">
                                {getCategoryIcon(category.icon)}
                              </span>
                              <span className="truncate">{category.name}</span>
                            </button>
                          ))}
                        </div>

                        {categories.length > visibleCategoriesCount && (
                          <div className="mt-2 px-2">
                            <button
                              onClick={() => setShowAllCategories(!showAllCategories)}
                              className="w-full text-center py-2 text-sm text-coral-500 hover:bg-coral-50 rounded-md flex items-center justify-center"
                            >
                              {showAllCategories ? (
                                <>
                                  <span>Show less</span>
                                  <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 15l7-7 7 7" />
                                  </svg>
                                </>
                              ) : (
                                <>
                                  <span>Show all categories</span>
                                  <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                                  </svg>
                                </>
                              )}
                            </button>
                          </div>
                        )}
                      </>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Blog */}
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                to="/blog"
                className="text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
              >
                Blog
              </Link>
            </motion.div>

            {/* User Menu or Auth Buttons */}
            {user?.nickname ? (
              <div className="relative" ref={userMenuRef}>
                <motion.button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-2 text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className="w-8 h-8 rounded-full bg-coral-100 flex items-center justify-center">
                    <span className="text-coral-600 font-medium">
                      {user.nickname.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <span>{user.nickname}</span>
                  <svg
                    className={`w-4 h-4 transition-transform duration-200 ${showUserMenu ? 'transform rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </motion.button>

                <AnimatePresence>
                  {showUserMenu && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.2 }}
                      className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                    >
                      <div className="py-1">
                        <Link
                          to="/profile"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setShowUserMenu(false)}
                        >
                          Profile
                        </Link>
                        <Link
                          to="/orders"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setShowUserMenu(false)}
                        >
                          Orders
                        </Link>
                        <Link
                          to="/shopping-trips"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setShowUserMenu(false)}
                        >
                          Shopping Trips
                        </Link>
                        <button
                          onClick={() => {
                            logout()
                            setShowUserMenu(false)
                          }}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          Logout
                        </button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    to="/auth/login"
                    className="border border-purple-500 px-4 py-2 rounded-lg text-sm font-medium hover:bg-purple-50 transition-colors duration-200 bg-gradient-to-r from-purple-500 to-pink-400 bg-clip-text text-transparent"
                  >
                    Login
                  </Link>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    to="/auth/register"
                    className="bg-gradient-to-r from-purple-500 to-pink-400 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-purple-600 hover:to-pink-500 transition-all duration-200"
                  >
                    Register
                  </Link>
                </motion.div>
              </div>
            )}
          </div>

          {/* Mobile Navigation Menu */}
          <AnimatePresence>
            {isMobileMenuOpen && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="lg:hidden absolute top-16 left-0 right-0 bg-white border-b border-coral-100 shadow-lg"
              >
                {/* Mobile Search */}
                <div className="p-4">
                  <div className="relative">
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => handleSearch(e.target.value)}
                      placeholder="Search stores..."
                      className="w-full pl-10 pr-4 py-2 border border-purple-200 rounded-lg focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200"
                    />
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      {isSearching ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-purple-500 border-t-transparent rounded-full"
                        />
                      ) : (
                        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      )}
                    </div>
                  </div>
                </div>

                {/* Mobile Navigation Links */}
                <div className="px-4 pb-4 space-y-2">
                  {/* Country Selector for Mobile */}
                  <div className="px-3 py-2">
                    <div className="text-sm font-medium text-gray-700 mb-2">Country</div>
                    <CountrySelector />
                  </div>

                  <Link
                    to="/coupons"
                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-purple-600 hover:bg-coral-50"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Coupons
                  </Link>

                  <button
                    onClick={() => setShowCategoryMenu(!showCategoryMenu)}
                    className="w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-purple-600 hover:bg-coral-50"
                  >
                    Categories
                  </button>
                  {showCategoryMenu && (
                    <div className="pl-6 max-h-[50vh] overflow-y-auto custom-scrollbar">
                      {loadingCategories ? (
                        <div className="px-3 py-2 text-gray-500">Loading...</div>
                      ) : categories.length === 0 ? (
                        <div className="px-3 py-2 text-gray-500">No categories found</div>
                      ) : (
                        <>
                          <div className={`${categories.length > 12 ? 'grid grid-cols-2 gap-1' : ''}`}>
                            {getVisibleCategories().map((category) => (
                              <button
                                key={category.id}
                                onClick={() => {
                                  handleCategoryClick(category.id)
                                  setIsMobileMenuOpen(false)
                                }}
                                className="w-full text-left px-3 py-2 text-gray-700 hover:text-purple-600 hover:bg-purple-50 rounded-md truncate flex items-center gap-2"
                              >
                                <span className="flex-shrink-0 w-5 h-5 flex items-center justify-center opacity-80 text-purple-500">
                                  {getCategoryIcon(category.icon)}
                                </span>
                                <span className="truncate">{category.name}</span>
                              </button>
                            ))}
                          </div>

                          {categories.length > visibleCategoriesCount && (
                            <button
                              onClick={() => setShowAllCategories(!showAllCategories)}
                              className="w-full text-center py-2 mt-2 text-sm text-coral-500 hover:bg-coral-50 rounded-md flex items-center justify-center"
                            >
                              {showAllCategories ? (
                                <>
                                  <span>Show less</span>
                                  <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 15l7-7 7 7" />
                                  </svg>
                                </>
                              ) : (
                                <>
                                  <span>Show all categories</span>
                                  <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                                  </svg>
                                </>
                              )}
                            </button>
                          )}
                        </>
                      )}
                    </div>
                  )}
                  <Link
                    to="/blog"
                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-purple-600 hover:bg-coral-50"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Blog
                  </Link>
                  {user?.nickname ? (
                    <>
                      <Link
                        to="/profile"
                        className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-purple-600 hover:bg-coral-50"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        Profile
                      </Link>
                      <Link
                        to="/orders"
                        className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-purple-600 hover:bg-coral-50"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        My Orders
                      </Link>
                      <Link
                        to="/shopping-trips"
                        className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-purple-600 hover:bg-coral-50"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        Shopping Trips
                      </Link>
                      <button
                        onClick={() => {
                          logout()
                          setIsMobileMenuOpen(false)
                        }}
                        className="w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-purple-600 hover:bg-coral-50"
                      >
                        Logout
                      </button>
                    </>
                  ) : (
                    <div className="space-y-2">
                      <Link
                        to="/auth/login"
                        className="block w-full text-center px-4 py-2 rounded-lg text-coral-500 border border-coral-500 hover:bg-coral-50"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        Login
                      </Link>
                      <Link
                        to="/auth/register"
                        className="block w-full text-center px-4 py-2 rounded-lg bg-coral-500 text-white hover:bg-coral-600"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        Register
                      </Link>
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </nav>
      </div>
    </header>
  )
}
