import React from 'react'
import { motion } from 'framer-motion'

const WhyChooseUs: React.FC = () => {
  const features = [
    {
      icon: "✨",
      title: "VERIFIED OFFERS",
      description: "Every offer on our platform is meticulously verified for authenticity, ensuring you receive genuine savings with each transaction.",
      gradient: "from-pink-500 to-purple-600",
      delay: 0
    },
    {
      icon: "🔄",
      title: "RELIABLE TRACKING",
      description: "Our proprietary tracking technology ensures your cashback is recorded with exceptional accuracy, providing dependable updates on your accumulated rewards.",
      gradient: "from-purple-500 to-indigo-600",
      delay: 0.2
    },
    {
      icon: "📱",
      title: "SEAMLESS EXPERIENCE",
      description: "Enjoy a fluid, intuitive interface that makes finding and using cashback offers as effortless as it is rewarding.",
      gradient: "from-indigo-500 to-blue-600",
      delay: 0.4
    },
    {
      icon: "🛡️",
      title: "SECURE PLATFORM",
      description: "Your data and transactions are protected with enterprise-grade security, giving you peace of mind while you shop and save.",
      gradient: "from-pink-500 to-red-600",
      delay: 0.6
    },
    {
      icon: "💰",
      title: "COMPETITIVE RATES",
      description: "We negotiate the best cashback rates with our merchant partners, ensuring you get maximum value with every purchase.",
      gradient: "from-orange-500 to-pink-600",
      delay: 0.8
    },
    {
      icon: "🌐",
      title: "GLOBAL MERCHANTS",
      description: "Access cashback offers from local and international brands, expanding your shopping horizons while maximizing savings.",
      gradient: "from-blue-500 to-indigo-600",
      delay: 1.0
    }
  ];

  return (
    <section className="w-full py-32 px-4 relative z-20 overflow-hidden">
      <div className="max-w-7xl mx-auto">
        {/* Decorative Background Elements */}
        <div className="absolute inset-0 opacity-30 pointer-events-none overflow-hidden">
          <motion.div 
            className="absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-gradient-to-r from-pink-500 to-purple-600 blur-3xl"
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 90, 0],
              opacity: [0.3, 0.4, 0.3],
            }}
            transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
          />
          <motion.div 
            className="absolute bottom-1/4 left-1/4 w-80 h-80 rounded-full bg-gradient-to-r from-indigo-500 to-blue-600 blur-3xl"
            animate={{
              scale: [1, 1.3, 1],
              rotate: [0, -90, 0],
              opacity: [0.3, 0.5, 0.3],
            }}
            transition={{ duration: 18, repeat: Infinity, ease: "linear", delay: 2 }}
          />
        </div>
        
        {/* Section Header */}
        <motion.div 
          className="text-center mb-24 relative z-10"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.h2 
            className="text-6xl font-bold bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-400 bg-clip-text text-transparent mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            WHY CHOOSE US
          </motion.h2>
          
          {/* Artistic separator */}
          <div className="flex justify-center space-x-2 mb-10">
            <motion.div 
              className="w-2 h-2 rounded-full bg-pink-500"
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
            />
            <motion.div 
              className="w-20 h-1 rounded-full bg-purple-500 mt-0.5"
              animate={{ width: [80, 100, 80] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 0.3 }}
            />
            <motion.div 
              className="w-2 h-2 rounded-full bg-indigo-500"
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 0.6 }}
            />
          </div>
          
          <motion.p 
            className="text-xl text-gray-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Discover why our platform transcends ordinary cashback experiences
          </motion.p>
        </motion.div>
        
        {/* Feature Cards - 3D Floating Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12 relative z-10">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="relative"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: feature.delay }}
              whileHover={{ 
                y: -15,
                transition: { duration: 0.3 }
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-600/20 rounded-2xl blur-xl transform group-hover:scale-105 transition-all duration-300" />
              
              <div className="relative group bg-black/40 backdrop-blur-xl border border-white/10 rounded-2xl p-8 overflow-hidden h-full flex flex-col">
                {/* Abstract background element */}
                <div className="absolute -right-20 -top-20 w-40 h-40 bg-gradient-to-br from-pink-500/10 to-purple-600/10 rounded-full blur-2xl" />
                <div className="absolute -left-20 -bottom-20 w-40 h-40 bg-gradient-to-br from-indigo-500/10 to-blue-600/10 rounded-full blur-2xl" />
                
                <motion.div 
                  className={`w-16 h-16 bg-gradient-to-br ${feature.gradient} rounded-2xl flex items-center justify-center mb-6 shadow-lg text-2xl`}
                  whileHover={{ rotate: 5 }}
                  animate={{
                    boxShadow: [
                      '0 0 0 rgba(167, 139, 250, 0)',
                      '0 0 20px rgba(167, 139, 250, 0.5)',
                      '0 0 0 rgba(167, 139, 250, 0)'
                    ]
                  }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  {feature.icon}
                </motion.div>
                
                <h3 className="text-xl font-bold text-white mb-4 tracking-wider">{feature.title}</h3>
                
                <p className="text-gray-300 mb-6 flex-grow">
                  {feature.description}
                </p>
                
                <motion.div
                  className={`w-12 h-1 bg-gradient-to-r ${feature.gradient} rounded mt-auto`}
                  initial={{ width: 0 }}
                  whileInView={{ width: 48 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8, delay: feature.delay + 0.3 }}
                />
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default WhyChooseUs 