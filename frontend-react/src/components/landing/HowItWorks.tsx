import React from 'react'
import { motion } from 'framer-motion'
import { FaTag, FaGift, FaMoneyBillWave } from 'react-icons/fa'

const HowItWorks: React.FC = () => {
  return (
    <section className="w-full py-32 px-4 relative z-20 overflow-hidden">
      <div className="max-w-7xl mx-auto">
        {/* Decorative Background Elements */}
        <div className="absolute inset-0 pointer-events-none">
          <motion.div 
            className="absolute bottom-1/3 -right-24 w-64 h-64 rounded-full bg-gradient-to-r from-indigo-500/20 to-blue-600/20 blur-3xl"
            animate={{ 
              scale: [1, 1.2, 1], 
              rotate: [0, -45, 0],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.div 
            className="absolute top-1/4 -left-24 w-80 h-80 rounded-full bg-gradient-to-r from-pink-500/20 to-purple-600/20 blur-3xl"
            animate={{ 
              scale: [1, 1.3, 1], 
              rotate: [0, 45, 0],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{ duration: 15, repeat: Infinity, ease: "easeInOut", delay: 2 }}
          />
        </div>
        
        {/* Section Header */}
        <motion.div 
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.h2 
            className="text-6xl font-bold bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-400 bg-clip-text text-transparent mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            HOW IT WORKS
          </motion.h2>
          
          {/* Artistic separator */}
          <div className="flex justify-center space-x-2 mb-10">
            <motion.div 
              className="w-2 h-2 rounded-full bg-pink-500"
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
            />
            <motion.div 
              className="w-20 h-1 rounded-full bg-purple-500 mt-0.5"
              animate={{ width: [80, 100, 80] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 0.3 }}
            />
            <motion.div 
              className="w-2 h-2 rounded-full bg-indigo-500"
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 0.6 }}
            />
          </div>
          
          <motion.p 
            className="text-xl text-gray-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Our platform is designed to make earning rewards simple, transparent, and delightful
          </motion.p>
        </motion.div>
        
        {/* Steps */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-16 mt-20 relative">
          {/* Connecting line */}
          <div className="absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-pink-500/30 via-purple-500/30 to-indigo-500/30 hidden md:block -translate-y-1/2" />
          
          {/* Step 1 */}
          <motion.div 
            className="relative"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            <div className="bg-black/30 backdrop-blur-xl rounded-2xl p-8 border border-white/10 h-full flex flex-col items-center text-center relative">
              <motion.div 
                className="w-16 h-16 rounded-full bg-gradient-to-br from-pink-500 to-purple-600 flex items-center justify-center mb-6 absolute -top-8 shadow-xl shadow-pink-500/20"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <FaTag className="w-7 h-7 text-white" />
              </motion.div>
              
              <div className="mt-8">
                <motion.div 
                  className="w-12 h-12 rounded-full flex items-center justify-center bg-gray-800/80 border border-gray-700 text-white font-bold text-xl mx-auto mb-6"
                  animate={{ 
                    boxShadow: ['0 0 0 rgba(236, 72, 153, 0)', '0 0 20px rgba(236, 72, 153, 0.5)', '0 0 0 rgba(236, 72, 153, 0)']
                  }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  1
                </motion.div>
                
                <h3 className="text-2xl font-bold text-white mb-4">Shop</h3>
                <p className="text-gray-300">
                  Browse our curated selection of premium merchants and exclusive deals across various categories
                </p>
              </div>
            </div>
          </motion.div>
          
          {/* Step 2 */}
          <motion.div 
            className="relative"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.2 }}
          >
            <div className="bg-black/30 backdrop-blur-xl rounded-2xl p-8 border border-white/10 h-full flex flex-col items-center text-center relative">
              <motion.div 
                className="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-indigo-600 flex items-center justify-center mb-6 absolute -top-8 shadow-xl shadow-purple-500/20"
                whileHover={{ scale: 1.1, rotate: -5 }}
                transition={{ duration: 0.3 }}
              >
                <FaGift className="w-7 h-7 text-white" />
              </motion.div>
              
              <div className="mt-8">
                <motion.div 
                  className="w-12 h-12 rounded-full flex items-center justify-center bg-gray-800/80 border border-gray-700 text-white font-bold text-xl mx-auto mb-6"
                  animate={{ 
                    boxShadow: ['0 0 0 rgba(168, 85, 247, 0)', '0 0 20px rgba(168, 85, 247, 0.5)', '0 0 0 rgba(168, 85, 247, 0)']
                  }}
                  transition={{ duration: 3, repeat: Infinity, delay: 1 }}
                >
                  2
                </motion.div>
                
                <h3 className="text-2xl font-bold text-white mb-4">Earn</h3>
                <p className="text-gray-300">
                  Automatically collect cashback and rewards with every purchase, tracked reliably through our advanced system
                </p>
              </div>
            </div>
          </motion.div>
          
          {/* Step 3 */}
          <motion.div 
            className="relative"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.4 }}
          >
            <div className="bg-black/30 backdrop-blur-xl rounded-2xl p-8 border border-white/10 h-full flex flex-col items-center text-center relative">
              <motion.div 
                className="w-16 h-16 rounded-full bg-gradient-to-br from-indigo-500 to-blue-600 flex items-center justify-center mb-6 absolute -top-8 shadow-xl shadow-indigo-500/20"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <FaMoneyBillWave className="w-7 h-7 text-white" />
              </motion.div>
              
              <div className="mt-8">
                <motion.div 
                  className="w-12 h-12 rounded-full flex items-center justify-center bg-gray-800/80 border border-gray-700 text-white font-bold text-xl mx-auto mb-6"
                  animate={{ 
                    boxShadow: ['0 0 0 rgba(99, 102, 241, 0)', '0 0 20px rgba(99, 102, 241, 0.5)', '0 0 0 rgba(99, 102, 241, 0)']
                  }}
                  transition={{ duration: 3, repeat: Infinity, delay: 2 }}
                >
                  3
                </motion.div>
                
                <h3 className="text-2xl font-bold text-white mb-4">Enjoy</h3>
                <p className="text-gray-300">
                  Redeem your cashback through multiple payout options or keep accumulating for bigger rewards
                </p>
              </div>
            </div>
          </motion.div>
        </div>
        
        {/* Additional Info */}
        <motion.div 
          className="mt-24 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <p className="text-lg text-gray-300 max-w-3xl mx-auto">
            Join thousands of savvy shoppers who have already discovered the art of rewarding shopping. Our platform is continuously expanding with new merchants and exclusive offers.
          </p>
        </motion.div>
      </div>
    </section>
  )
}

export default HowItWorks 