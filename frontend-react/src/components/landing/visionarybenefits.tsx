import React from 'react'
import { motion } from 'framer-motion'
import { FaMoneyBillWave, FaLightbulb, FaShieldAlt } from 'react-icons/fa'

const VisionaryBenefits: React.FC = () => {
  return (
    <section className="w-full py-32 px-4 relative z-20">
      <div className="max-w-7xl mx-auto">
        <motion.div 
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.h2 
            className="text-6xl font-bold bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-400 bg-clip-text text-transparent mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            VISIONARY BENEFITS
          </motion.h2>
          
          {/* Artistic separator */}
          <div className="flex justify-center space-x-2 mb-10">
            <motion.div 
              className="w-2 h-2 rounded-full bg-pink-500"
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
            />
            <motion.div 
              className="w-12 h-1 rounded-full bg-purple-500 mt-0.5"
              animate={{ width: [48, 60, 48] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 0.3 }}
            />
            <motion.div 
              className="w-2 h-2 rounded-full bg-indigo-500"
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 0.6 }}
            />
          </div>
          
          <motion.p 
            className="text-xl text-gray-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Embark on a rewarding journey where art meets commerce, elevating your shopping experience to transcendental heights
          </motion.p>
        </motion.div>
        
        {/* Artistic Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
          {[
            {
              icon: <FaMoneyBillWave className="w-10 h-10 text-pink-300" />,
              title: "ELEVATED RETURNS",
              description: "Curated rewards that transform ordinary purchases into extraordinary savings masterpieces.",
              colors: "from-pink-500 to-purple-600",
              delay: 0
            },
            {
              icon: <FaLightbulb className="w-10 h-10 text-purple-300" />,
              title: "VISIONARY DEALS",
              description: "Discover offers that transcend conventional shopping, carefully verified by our artisans.",
              colors: "from-purple-500 to-indigo-600",
              delay: 0.2
            },
            {
              icon: <FaShieldAlt className="w-10 h-10 text-indigo-300" />,
              title: "SEAMLESS SECURITY",
              description: "Protected transactions flowing like brushstrokes on canvas, creating a masterpiece of trust.",
              colors: "from-indigo-500 to-blue-600",
              delay: 0.4
            }
          ].map((item, index) => (
            <motion.div 
              key={index}
              className="relative group"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: item.delay }}
              whileHover={{ y: -10, transition: { duration: 0.3 } }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-600/20 rounded-2xl blur-xl transform group-hover:scale-105 transition-all duration-300" />
              
              <div className="relative bg-black/40 backdrop-blur-xl border border-white/10 rounded-2xl p-8 overflow-hidden h-full flex flex-col">
                {/* Abstract background element */}
                <div className="absolute -right-20 -top-20 w-40 h-40 bg-gradient-to-br from-pink-500/10 to-purple-600/10 rounded-full blur-2xl" />
                <div className="absolute -left-20 -bottom-20 w-40 h-40 bg-gradient-to-br from-indigo-500/10 to-blue-600/10 rounded-full blur-2xl" />
                
                <motion.div 
                  className={`w-20 h-20 bg-gradient-to-br ${item.colors} rounded-xl flex items-center justify-center mb-6 shadow-lg`}
                  whileHover={{ rotate: 5 }}
                  animate={{
                    boxShadow: [
                      '0 0 0 rgba(167, 139, 250, 0)',
                      '0 0 20px rgba(167, 139, 250, 0.5)',
                      '0 0 0 rgba(167, 139, 250, 0)'
                    ],
                    transition: { duration: 3, repeat: Infinity }
                  }}
                >
                  {item.icon}
                </motion.div>
                
                <h3 className="text-xl font-bold text-white mb-4 tracking-wider">{item.title}</h3>
                
                <p className="text-gray-300 mb-6 flex-grow">
                  {item.description}
                </p>
                
                <motion.div
                  className="w-12 h-1 bg-gradient-to-r from-pink-500 to-purple-500 rounded mt-auto"
                  initial={{ width: 0 }}
                  whileInView={{ width: 48 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8, delay: item.delay + 0.3 }}
                />
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default VisionaryBenefits 