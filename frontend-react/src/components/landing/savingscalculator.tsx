import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FaArrowRight } from 'react-icons/fa'

interface SavingsCalculatorProps {
  calculatorAmount: number;
  setCalculatorAmount: (amount: number) => void;
  calculatorCashback: number;
  setCalculatorCashback: (rate: number) => void;
  calculatorResult: number;
}

const SavingsCalculator: React.FC<SavingsCalculatorProps> = ({
  calculatorAmount,
  setCalculatorAmount,
  calculatorCashback,
  setCalculatorCashback,
  calculatorResult
}) => {
  // Add timeframe state
  const [timeframe, setTimeframe] = useState<number>(2); // Default is Yearly (index 2)
  
  // Define different subscription services and their monthly prices
  const subscriptions = [
    { name: "Netflix", price: 60 },
    { name: "Spotify Premium", price: 15 },
    { name: "Apple Music", price: 16 },
    { name: "Disney+", price: 50 },
    { name: "Xbox Game Pass", price: 25 },
    { name: "Amazon Prime", price: 18 }
  ];
  
  // Calculate total savings based on current timeframe
  const getTotalSavings = () => {
    if (timeframe === 0) return calculatorResult; // Monthly
    if (timeframe === 1) return calculatorResult * 6; // 6 Months
    return calculatorResult * 12; // Yearly
  };
  
  // Randomly select a subscription service for comparison
  const getRandomSubscription = () => {
    const savings = getTotalSavings();
    // Use annual savings divided by possible service prices
    const index = Math.floor((calculatorResult * 100) % subscriptions.length);
    const subscription = subscriptions[index];
    
    const period = timeframe === 0 ? "month" : timeframe === 1 ? "half-year" : "year";
    const months = Math.ceil(savings / subscription.price);
    
    if (months <= 1) {
      return `That's equivalent to ${months} month of free ${subscription.name} subscription!`;
    } else if (months < 12) {
      return `That's equivalent to ${months} months of free ${subscription.name} subscription!`;
    } else {
      const years = Math.floor(months / 12);
      const remainingMonths = months % 12;
      if (remainingMonths > 0) {
        return `That's equivalent to ${years} year${years > 1 ? 's' : ''} and ${remainingMonths} month${remainingMonths > 1 ? 's' : ''} of free ${subscription.name} subscription!`;
      } else {
        return `That's equivalent to ${years} year${years > 1 ? 's' : ''} of free ${subscription.name} subscription!`;
      }
    }
  };
  
  return (
    <section className="w-full py-32 px-4 relative z-20 overflow-hidden">
      <div className="max-w-7xl mx-auto">
        {/* Decorative Background Elements */}
        <div className="absolute inset-0 pointer-events-none">
          <motion.div 
            className="absolute top-1/3 -right-24 w-64 h-64 rounded-full bg-gradient-to-r from-pink-500/20 to-purple-600/20 blur-3xl"
            animate={{ 
              scale: [1, 1.2, 1], 
              rotate: [0, 45, 0],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.div 
            className="absolute bottom-1/4 -left-24 w-80 h-80 rounded-full bg-gradient-to-r from-indigo-500/20 to-blue-600/20 blur-3xl"
            animate={{ 
              scale: [1, 1.3, 1], 
              rotate: [0, -45, 0],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{ duration: 15, repeat: Infinity, ease: "easeInOut", delay: 2 }}
          />
        </div>
        
        {/* Section Header */}
        <motion.div 
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.h2 
            className="text-6xl font-bold bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-400 bg-clip-text text-transparent mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            SAVINGS CALCULATOR
          </motion.h2>
          
          {/* Artistic separator */}
          <div className="flex justify-center space-x-2 mb-10">
            <motion.div 
              className="w-2 h-2 rounded-full bg-pink-500"
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
            />
            <motion.div 
              className="w-24 h-1 rounded-full bg-purple-500 mt-0.5"
              animate={{ width: [96, 120, 96] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 0.3 }}
            />
            <motion.div 
              className="w-2 h-2 rounded-full bg-indigo-500"
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 0.6 }}
            />
          </div>
          
          <motion.p 
            className="text-xl text-gray-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Visualize your potential savings with our advanced calculator
          </motion.p>
        </motion.div>
        
        {/* Calculator Card */}
        <motion.div 
          className="max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          <div className="bg-black/40 backdrop-blur-xl border border-white/10 rounded-2xl p-8 md:p-12 relative overflow-hidden">
            {/* Decorative Elements */}
            <div className="absolute -right-20 -top-20 w-64 h-64 bg-gradient-to-br from-pink-500/10 to-purple-600/10 rounded-full blur-xl" />
            <div className="absolute -left-20 -bottom-20 w-64 h-64 bg-gradient-to-br from-indigo-500/10 to-blue-600/10 rounded-full blur-xl" />
            
            <div className="relative z-10">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                {/* Left Column - Inputs */}
                <div>
                  <h3 className="text-2xl font-bold text-white mb-6">Your Shopping Details</h3>
                  
                  <div className="space-y-6">
                    {/* Spending Amount Slider */}
                    <div>
                      <label className="block text-gray-300 text-sm font-medium mb-2">
                        Monthly Shopping Budget
                      </label>
                      <div className="relative">
                        <input
                          type="range"
                          min="50"
                          max="5000"
                          step="50"
                          value={calculatorAmount}
                          onChange={(e) => setCalculatorAmount(parseInt(e.target.value))}
                          className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                        />
                        <div className="absolute -top-8 left-0 right-0">
                          <div className="relative">
                            <motion.div 
                              className="absolute -top-1"
                              style={{ left: `calc(${(calculatorAmount - 50) / (5000 - 50) * 100}% - 24px)` }}
                              animate={{ y: [0, -5, 0] }}
                              transition={{ duration: 1, repeat: Infinity, repeatType: "reverse" }}
                            >
                              <div className="bg-gradient-to-r from-pink-500 to-purple-600 text-white px-3 py-1 rounded-lg text-sm font-bold">
                                ${calculatorAmount}
                              </div>
                            </motion.div>
                          </div>
                        </div>
                      </div>
                      <div className="flex justify-between mt-2 text-xs text-gray-400">
                        <span>$50</span>
                        <span>$5,000</span>
                      </div>
                    </div>
                    
                    {/* Cashback Rate Slider */}
                    <div>
                      <label className="block text-gray-300 text-sm font-medium mb-2">
                        Average Cashback Rate
                      </label>
                      <div className="relative">
                        <input
                          type="range"
                          min="1"
                          max="20"
                          step="0.5"
                          value={calculatorCashback}
                          onChange={(e) => setCalculatorCashback(parseFloat(e.target.value))}
                          className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                        />
                        <div className="absolute -top-8 left-0 right-0">
                          <div className="relative">
                            <motion.div 
                              className="absolute -top-1"
                              style={{ left: `calc(${(calculatorCashback - 1) / (20 - 1) * 100}% - 24px)` }}
                              animate={{ y: [0, -5, 0] }}
                              transition={{ duration: 1, repeat: Infinity, repeatType: "reverse", delay: 0.5 }}
                            >
                              <div className="bg-gradient-to-r from-indigo-500 to-blue-600 text-white px-3 py-1 rounded-lg text-sm font-bold">
                                {calculatorCashback}%
                              </div>
                            </motion.div>
                          </div>
                        </div>
                      </div>
                      <div className="flex justify-between mt-2 text-xs text-gray-400">
                        <span>1%</span>
                        <span>20%</span>
                      </div>
                    </div>
                    
                    {/* Frequency Selection */}
                    <div>
                      <label className="block text-gray-300 text-sm font-medium mb-2">
                        Calculation Timeframe
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        {["Monthly", "6 Months", "Yearly"].map((period, i) => (
                          <motion.button
                            key={i}
                            className={`py-2 px-4 rounded-lg text-sm font-medium ${
                              i === timeframe 
                                ? "bg-gradient-to-r from-pink-500 to-purple-600 text-white" 
                                : "bg-gray-800/50 text-gray-300 hover:bg-gray-800"
                            }`}
                            whileHover={{ y: -3 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => setTimeframe(i)}
                          >
                            {period}
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Right Column - Results */}
                <div>
                  <h3 className="text-2xl font-bold text-white mb-6">Your Potential Savings</h3>
                  
                  <div className="space-y-6">
                    {/* Monthly Savings */}
                    <div className={`bg-gray-900/50 rounded-xl p-6 border ${timeframe === 0 ? 'border-pink-500/30' : 'border-gray-800'}`}>
                      <div className="flex justify-between items-center mb-2">
                        <h4 className={`${timeframe === 0 ? 'text-white font-medium' : 'text-gray-300'}`}>Monthly Cashback</h4>
                        <span className="text-xs bg-pink-500/20 text-pink-300 px-2 py-1 rounded">30 Days</span>
                      </div>
                      <motion.div 
                        className={`text-3xl font-bold ${timeframe === 0 ? 'text-white' : 'bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent'}`}
                        key={`monthly-${calculatorResult}-${timeframe}`}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        ${calculatorResult.toFixed(2)}
                      </motion.div>
                      {timeframe === 0 && (
                        <div className="mt-3 text-sm text-gray-300">
                          {getRandomSubscription()}
                        </div>
                      )}
                    </div>
                    
                    {/* 6 Month Savings */}
                    <div className={`bg-gray-900/50 rounded-xl p-6 border ${timeframe === 1 ? 'border-purple-500/30' : 'border-gray-800'}`}>
                      <div className="flex justify-between items-center mb-2">
                        <h4 className={`${timeframe === 1 ? 'text-white font-medium' : 'text-gray-300'}`}>6-Month Cashback</h4>
                        <span className="text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded">180 Days</span>
                      </div>
                      <motion.div 
                        className={`text-3xl font-bold ${timeframe === 1 ? 'text-white' : 'bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent'}`}
                        key={`sixmonth-${calculatorResult}-${timeframe}`}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        ${(calculatorResult * 6).toFixed(2)}
                      </motion.div>
                      {timeframe === 1 && (
                        <div className="mt-3 text-sm text-gray-300">
                          {getRandomSubscription()}
                        </div>
                      )}
                    </div>
                    
                    {/* Yearly Savings */}
                    <div className={`rounded-xl p-6 border ${timeframe === 2 ? 'bg-gradient-to-r from-pink-500/20 to-purple-600/20 border-purple-500/30' : 'bg-gray-900/50 border-gray-800'}`}>
                      <div className="flex justify-between items-center mb-2">
                        <h4 className={`${timeframe === 2 ? 'text-white font-medium' : 'text-gray-300'}`}>Annual Cashback</h4>
                        <span className="text-xs bg-white/20 text-white px-2 py-1 rounded">365 Days</span>
                      </div>
                      <motion.div 
                        className={`text-3xl font-bold ${timeframe === 2 ? 'text-white' : 'bg-gradient-to-r from-indigo-400 to-blue-400 bg-clip-text text-transparent'}`}
                        key={`yearly-${calculatorResult}-${timeframe}`}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        ${(calculatorResult * 12).toFixed(2)}
                      </motion.div>
                      {timeframe === 2 && (
                        <div className="mt-3 text-sm text-gray-300">
                          {getRandomSubscription()}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Call to Action */}
              <motion.div 
                className="mt-12 text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                <motion.div
                  whileHover={{ scale: 1.05, y: -5 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    to="/auth/register"
                    className="inline-flex items-center px-8 py-3 rounded-xl bg-gradient-to-r from-pink-500 to-purple-600 text-white font-medium shadow-lg shadow-purple-500/20 transition-all duration-300"
                  >
                    START SAVING NOW
                    <FaArrowRight className="ml-2" />
                  </Link>
                </motion.div>
                <p className="text-gray-400 mt-4 text-sm">
                  Join thousands of smart shoppers maximizing their rewards
                </p>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default SavingsCalculator 