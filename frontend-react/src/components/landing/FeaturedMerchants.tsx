import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FaChevronRight, FaShoppingBag, FaArrowRight } from 'react-icons/fa'

interface FeaturedMerchantsProps {
  isLoading: boolean;
  featuredStores: any[];
  renderStoreCard: (store: any) => React.ReactNode;
  container: any;
}

const FeaturedMerchants: React.FC<FeaturedMerchantsProps> = ({ 
  isLoading, 
  featuredStores,
  renderStoreCard,
  container 
}) => {
  return (
    <section className="w-full py-32 px-4 relative z-20 overflow-hidden">
      <div className="max-w-7xl mx-auto">
        {/* Decorative Background Elements */}
        <div className="absolute inset-0 pointer-events-none">
          <motion.div 
            className="absolute top-1/4 -right-24 w-64 h-64 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-600/20 blur-3xl"
            animate={{ 
              scale: [1, 1.2, 1], 
              rotate: [0, 45, 0],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.div 
            className="absolute bottom-1/3 -left-24 w-80 h-80 rounded-full bg-gradient-to-r from-pink-500/20 to-purple-600/20 blur-3xl"
            animate={{ 
              scale: [1, 1.3, 1], 
              rotate: [0, -45, 0],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{ duration: 15, repeat: Infinity, ease: "easeInOut", delay: 2 }}
          />
        </div>
        
        {/* Section Header */}
        <motion.div 
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.h2 
            className="text-6xl font-bold bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-400 bg-clip-text text-transparent mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            TOP BRANDS, MAXIMUM CASHBACK
          </motion.h2>
          
          {/* Artistic separator */}
          <div className="flex justify-center space-x-2 mb-10">
            <motion.div 
              className="w-2 h-2 rounded-full bg-pink-500"
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
            />
            <motion.div 
              className="w-24 h-1 rounded-full bg-purple-500 mt-0.5"
              animate={{ width: [96, 120, 96] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 0.3 }}
            />
            <motion.div 
              className="w-2 h-2 rounded-full bg-indigo-500"
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 0.6 }}
            />
          </div>
          
          <motion.p 
            className="text-xl text-gray-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Explore our curated selection of premier brands offering exclusive cashback opportunities
          </motion.p>
        </motion.div>
        
        {/* Featured Merchants Grid */}
        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <motion.div 
              className="h-20 w-20 rounded-full border-4 border-transparent border-t-purple-500 border-b-pink-500"
              animate={{ rotate: 360 }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
            />
          </div>
        ) : (
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            variants={container}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
          >
            {featuredStores.map(store => renderStoreCard(store))}
          </motion.div>
        )}
        
        {/* View All Merchants Button */}
        <motion.div 
          className="flex justify-center mt-20"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <motion.div
            whileHover={{ scale: 1.05, y: -5 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              to="/stores"
              className="group relative inline-flex items-center px-12 py-4 overflow-hidden rounded-xl bg-black/30 backdrop-blur-xl border border-purple-500/30 text-white text-lg font-bold transition-all duration-300 hover:border-purple-500/60 hover:shadow-lg hover:shadow-purple-500/20"
            >
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-purple-600/0 via-white/10 to-purple-600/0"
                initial={{ x: '-100%' }}
                animate={{ x: '100%' }}
                transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 1 }}
              />
              EXPLORE ALL MERCHANTS
              <motion.div
                className="ml-2"
                animate={{ x: [0, 3, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatType: 'reverse' }}
              >
                <FaArrowRight className="group-hover:translate-x-1 transition-transform duration-300" />
              </motion.div>
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default FeaturedMerchants