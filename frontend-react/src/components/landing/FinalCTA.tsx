import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FaGift } from 'react-icons/fa'

const FinalCTA: React.FC = () => {
  return (
    <section className="py-32 px-4 relative z-20">
      <motion.div 
        className="max-w-4xl mx-auto bg-gradient-to-r from-black/40 to-black/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl overflow-hidden relative"
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
        whileHover={{
          boxShadow: '0 25px 50px -12px rgba(167, 139, 250, 0.3)',
          transition: { duration: 0.4 }
        }}
      >
        {/* Artistic Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <svg viewBox="0 0 100 100" preserveAspectRatio="none" className="absolute h-full w-full">
            <motion.path
              d="M0,0 L100,0 L100,100 L0,100 Z"
              fill="none"
              stroke="rgba(167, 139, 250, 0.2)"
              strokeWidth="0.5"
              vectorEffect="non-scaling-stroke"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 4, repeat: Infinity, repeatType: "loop", ease: "easeInOut" }}
            />
            <motion.path
              d="M0,20 Q50,0 100,20 L100,100 L0,100 Z"
              fill="none"
              stroke="rgba(251, 113, 133, 0.2)"
              strokeWidth="0.5"
              vectorEffect="non-scaling-stroke"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 5, repeat: Infinity, repeatType: "loop", ease: "easeInOut", delay: 0.5 }}
            />
            <motion.path
              d="M0,50 Q50,30 100,50 L100,100 L0,100 Z"
              fill="none"
              stroke="rgba(147, 197, 253, 0.2)"
              strokeWidth="0.5"
              vectorEffect="non-scaling-stroke"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 6, repeat: Infinity, repeatType: "loop", ease: "easeInOut", delay: 1 }}
            />
          </svg>
          {[...Array(10)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute bg-white/5 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: `${10 + Math.random() * 20}px`,
                height: `${10 + Math.random() * 20}px`,
              }}
              animate={{
                y: [0, -30, 0],
                x: [0, Math.random() * 20 - 10, 0],
                opacity: [0.2, 0.5, 0.2],
                scale: [1, 1.2, 1],
                transition: { 
                  duration: 5 + Math.random() * 5, 
                  repeat: Infinity,
                  repeatType: "reverse" 
                }
              }}
            />
          ))}
        </div>
        
        <div className="px-10 py-16 text-center relative z-10">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            <h2 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-400 bg-clip-text text-transparent mb-8">
              ENTER THE DIMENSION OF REWARDS
            </h2>
            <div className="h-0.5 w-32 bg-gradient-to-r from-pink-500 to-purple-600 mx-auto mb-8" />
            <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
              Join the collective of visionaries who have transcended ordinary shopping. Begin your artistic journey into a world of extraordinary rewards.
            </p>
          </motion.div>
          
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.3 }}
          >
            <Link
              to="/auth/register"
              className="group relative inline-flex items-center px-12 py-5 overflow-hidden rounded-xl bg-gradient-to-r from-pink-500 to-purple-600 text-white text-xl font-medium shadow-2xl transition-all duration-500"
            >
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-purple-600/0 via-white/20 to-purple-600/0"
                initial={{ x: '-100%' }}
                animate={{ x: '100%' }}
                transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 1 }}
              />
              <span className="relative z-10 flex items-center">
                MATERIALIZE YOUR ACCOUNT
                <motion.div 
                  className="ml-3 text-xl"
                  animate={{
                    rotate: [0, 15, 0, -15, 0],
                    transition: { duration: 3, repeat: Infinity }
                  }}
                >
                  <FaGift />
                </motion.div>
              </span>
            </Link>
          </motion.div>
          
          <motion.p
            className="mt-8 text-gray-400"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.5 }}
          >
            No credit card required. Eternal access.
          </motion.p>
        </div>
      </motion.div>
    </section>
  )
}

export default FinalCTA 