import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { <PERSON>a<PERSON><PERSON>, FaChevronRight, FaShoppingBag, FaTag } from 'react-icons/fa'

interface HeroSectionProps {
  particles: Array<{id: number, x: number, y: number, size: number, speed: number, color: string}>;
  mousePosition: { x: number, y: number };
  animatedStoreCount: number;
  animatedCouponCount: number;
}

const HeroSection: React.FC<HeroSectionProps> = ({ 
  particles, 
  mousePosition, 
  animatedStoreCount, 
  animatedCouponCount 
}) => {
  return (
    <>
      {/* 粒子动画背景 */}
      <div className="absolute inset-0 z-0">
        {particles.map(particle => (
          <motion.div
            key={particle.id}
            className="absolute rounded-full z-0"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: `${particle.size}px`,
              height: `${particle.size}px`,
              background: particle.color,
            }}
            animate={{
              y: [`${particle.y}%`, `${(particle.y + particle.speed * 100) % 100}%`],
              x: [`${particle.x}%`, `${(particle.x + Math.sin(particle.id) * 10) % 100}%`],
              opacity: [0.7, 0.3, 0.7],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 20 + particle.id % 10,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        ))}
      </div>
      
      {/* Glow Effects */}
      <div className="absolute inset-0 z-10 pointer-events-none">
        <motion.div 
          className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full"
          animate={{
            boxShadow: ['0 0 100px 50px rgba(251, 113, 133, 0.3)', '0 0 150px 80px rgba(251, 113, 133, 0.4)', '0 0 100px 50px rgba(251, 113, 133, 0.3)'],
            scale: [1, 1.2, 1],
          }}
          transition={{ duration: 8, repeat: Infinity, repeatType: "reverse" }}
        />
        <motion.div 
          className="absolute bottom-1/4 right-1/4 w-64 h-64 rounded-full"
          animate={{
            boxShadow: ['0 0 100px 50px rgba(147, 197, 253, 0.3)', '0 0 150px 80px rgba(147, 197, 253, 0.4)', '0 0 100px 50px rgba(147, 197, 253, 0.3)'],
            scale: [1, 1.2, 1],
          }}
          transition={{ duration: 10, repeat: Infinity, repeatType: "reverse", delay: 2 }}
        />
        <motion.div 
          className="absolute top-1/2 right-1/3 w-80 h-80 rounded-full"
          animate={{
            boxShadow: ['0 0 100px 50px rgba(167, 139, 250, 0.3)', '0 0 150px 80px rgba(167, 139, 250, 0.4)', '0 0 100px 50px rgba(167, 139, 250, 0.3)'],
            scale: [1, 1.2, 1],
          }}
          transition={{ duration: 9, repeat: Infinity, repeatType: "reverse", delay: 1 }}
        />
      </div>

      {/* Artistic Hero Section */}
      <section className="min-h-screen w-full flex items-center justify-center px-4 relative z-20">
        <div className="max-w-7xl mx-auto">
          {/* Cosmic Design Elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {/* Abstract Art Elements */}
            <motion.svg 
              width="100%" 
              height="100%" 
              viewBox="0 0 100 100" 
              preserveAspectRatio="none"
              className="absolute inset-0"
            >
              <motion.path
                d="M0,50 Q25,30 50,50 T100,50 V100 H0 Z"
                fill="none"
                stroke="rgba(251, 113, 133, 0.2)"
                strokeWidth="0.2"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 4, repeat: Infinity, repeatType: "loop", ease: "easeInOut" }}
              />
              <motion.path
                d="M0,65 Q25,45 50,65 T100,65 V100 H0 Z"
                fill="none"
                stroke="rgba(147, 197, 253, 0.2)"
                strokeWidth="0.2"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 5, repeat: Infinity, repeatType: "loop", ease: "easeInOut", delay: 0.5 }}
              />
              <motion.path
                d="M0,80 Q25,60 50,80 T100,80 V100 H0 Z"
                fill="none"
                stroke="rgba(167, 139, 250, 0.2)"
                strokeWidth="0.2"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 6, repeat: Infinity, repeatType: "loop", ease: "easeInOut", delay: 1 }}
              />
            </motion.svg>
          </div>

          {/* Content Container with 3D effect */}
          <motion.div 
            className="relative z-30 text-center"
            style={{
              perspective: "1000px",
              transformStyle: "preserve-3d",
              transform: `perspective(1000px) rotateY(${mousePosition.x * 0.01}deg) rotateX(${-mousePosition.y * 0.01}deg)`
            }}
          >
            {/* Abstract Decorative Element */}
            <motion.div 
              className="absolute -top-20 -left-20 w-40 h-40 rounded-full mix-blend-screen opacity-70"
              animate={{
                background: [
                  'radial-gradient(circle, rgba(251, 113, 133, 0.8) 0%, rgba(251, 113, 133, 0) 70%)',
                  'radial-gradient(circle, rgba(147, 197, 253, 0.8) 0%, rgba(147, 197, 253, 0) 70%)',
                  'radial-gradient(circle, rgba(167, 139, 250, 0.8) 0%, rgba(167, 139, 250, 0) 70%)',
                  'radial-gradient(circle, rgba(251, 113, 133, 0.8) 0%, rgba(251, 113, 133, 0) 70%)',
                ],
                scale: [1, 1.2, 0.9, 1],
                rotate: [0, 90, 180, 270, 360],
              }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            />
            
            {/* Glitch Title Effect */}
            <motion.div
              className="mb-10 relative"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1 }}
            >
              <motion.h1 
                className="text-6xl md:text-8xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-400 leading-tight mb-6 filter drop-shadow-lg" 
                initial={{ y: 50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8 }}
                data-text="CASHBACK REIMAGINED"
              >
                CASHBACK REIMAGINED
              </motion.h1>
              
              {/* Artistic Glowing Line */}
              <motion.div 
                className="w-40 h-1 bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 mx-auto my-10"
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 160, opacity: 1 }}
                transition={{ duration: 1, delay: 0.8 }}
              />
              
              <motion.p 
                className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto mb-12 font-light"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                Experience shopping transformed through artistic rewards. 
                <span className="block mt-3 text-purple-300">Where every purchase becomes a masterpiece of savings.</span>
              </motion.p>
            </motion.div>
            
            {/* 3D Floating CTAs */}
            <motion.div 
              className="flex flex-col md:flex-row gap-5 justify-center mt-12"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <motion.div
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
                drag 
                dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
                dragElastic={0.1}
              >
                <Link
                  to="/auth/register"
                  className="group relative px-10 py-4 overflow-hidden rounded-xl bg-gradient-to-r from-pink-500 to-purple-600 text-white text-xl font-medium shadow-2xl transition-all duration-300 hover:shadow-purple-500/25 hover:shadow-xl flex items-center justify-center"
                >
                  <motion.span
                    className="absolute inset-0 bg-gradient-to-r from-purple-600/0 via-white/10 to-purple-600/0"
                    initial={{ x: '-100%' }}
                    animate={{ x: '100%' }}
                    transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 1 }}
                  />
                  <motion.span 
                    className="absolute -inset-x-1 bottom-0 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: [0, 1, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                  BEGIN JOURNEY <FaChevronRight className="ml-2 relative z-10" />
                </Link>
              </motion.div>
              
              <motion.div
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
                drag 
                dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
                dragElastic={0.1}
              >
                <Link
                  to="/auth/login"
                  className="group relative px-10 py-4 overflow-hidden rounded-xl border-2 border-purple-400 text-purple-100 text-xl font-medium transition-all duration-300 hover:text-white flex items-center justify-center"
                >
                  <span className="relative z-10">ENTER PORTAL</span> <FaUser className="ml-2 relative z-10" />
                  <motion.div 
                    className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  />
                </Link>
              </motion.div>
            </motion.div>
          </motion.div>
          
          {/* Artistic Stats */}
          <motion.div 
            className="mt-40 flex flex-col lg:flex-row gap-8 justify-center items-center relative z-20"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1.2 }}
          >
            <motion.div 
              className="bg-black/30 border border-purple-500/20 backdrop-blur-xl rounded-2xl px-10 py-8 max-w-lg w-full"
              whileHover={{ 
                y: -10, 
                boxShadow: "0 20px 40px -10px rgba(167, 139, 250, 0.3)",
                transition: { duration: 0.3 }
              }}
            >
              <div className="flex items-center gap-6">
                <motion.div 
                  className="w-20 h-20 bg-gradient-to-br from-pink-500 to-purple-600 rounded-2xl flex items-center justify-center"
                  animate={{ 
                    rotate: [0, 10, 0, -10, 0],
                    scale: [1, 1.05, 1, 0.95, 1],
                    transition: { duration: 8, repeat: Infinity, ease: "easeInOut" }
                  }}
                >
                  <FaShoppingBag className="w-10 h-10 text-white" />
                </motion.div>
                <div>
                  <motion.h2 
                    className="text-5xl font-bold bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent"
                    animate={{ 
                      scale: [1, 1.03, 1],
                      transition: { duration: 2, repeat: Infinity }
                    }}
                  >
                    {animatedStoreCount.toLocaleString()}+
                  </motion.h2>
                  <p className="text-xl text-purple-200 mt-2">Featured Merchants</p>
                </div>
              </div>
            </motion.div>
            
            <motion.div 
              className="bg-black/30 border border-pink-500/20 backdrop-blur-xl rounded-2xl px-10 py-8 max-w-lg w-full"
              whileHover={{ 
                y: -10, 
                boxShadow: "0 20px 40px -10px rgba(251, 113, 133, 0.3)",
                transition: { duration: 0.3 }
              }}
            >
              <div className="flex items-center gap-6">
                <motion.div 
                  className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center"
                  animate={{ 
                    rotate: [0, -10, 0, 10, 0],
                    scale: [1, 0.95, 1, 1.05, 1],
                    transition: { duration: 8, repeat: Infinity, ease: "easeInOut", delay: 1 }
                  }}
                >
                  <FaTag className="w-10 h-10 text-white" />
                </motion.div>
                <div>
                  <motion.h2 
                    className="text-5xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"
                    animate={{ 
                      scale: [1, 1.03, 1],
                      transition: { duration: 2, repeat: Infinity, delay: 0.3 }
                    }}
                  >
                    {animatedCouponCount.toLocaleString()}+
                  </motion.h2>
                  <p className="text-xl text-purple-200 mt-2">Active Rewards</p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </>
  )
}

export default HeroSection 