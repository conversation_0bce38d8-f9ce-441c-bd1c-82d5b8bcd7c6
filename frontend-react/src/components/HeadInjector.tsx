import React, { useEffect } from 'react';
import { config } from '../config';

/**
 * HeadInjector Component
 *
 * This component injects custom HTML content into the document's <head> tag
 * based on the configuration defined in the config file.
 *
 * It uses useEffect to safely modify the DOM only on the client side.
 */
const HeadInjector: React.FC = () => {
  useEffect(() => {
    // Only inject in development mode - in production, content is injected at build time
    if (config.env === 'live' || import.meta.env.PROD) {
      return;
    }

    // Create a unique identifier for our injected elements
    const dataAttribute = 'data-custom-head-content';

    // First, remove any previously injected elements to avoid duplicates
    const existingElements = document.querySelectorAll(`[${dataAttribute}]`);
    existingElements.forEach(element => element.remove());

    // If there's no content to inject, return early
    if (!config.headContent || config.headContent.length === 0) {
      return;
    }

    // Get the document's head
    const head = document.head;

    // Create a document fragment to temporarily hold all elements
    // This is more efficient than manipulating the DOM multiple times
    const fragment = document.createDocumentFragment();

    // Create a temporary container for parsing HTML strings
    const container = document.createElement('div');

    // Process each string in the headContent array
    config.headContent.forEach((htmlString, index) => {
      if (!htmlString || typeof htmlString !== 'string') return;

      try {
        // Parse the HTML string
        container.innerHTML = htmlString.trim();

        // Add each child from the container to our fragment
        while (container.firstChild) {
          const element = container.firstChild;

          // Add our data attribute to track this element
          if (element instanceof Element) {
            element.setAttribute(dataAttribute, `${index}`);
          }

          // Move the element to our fragment
          fragment.appendChild(element);
        }
      } catch (error) {
        console.error(`Failed to inject head content item ${index}:`, error);
      }
    });

    // Finally, append all new elements to the head
    head.appendChild(fragment);

    // Cleanup function to remove injected elements when component unmounts
    return () => {
      const injectedElements = document.querySelectorAll(`[${dataAttribute}]`);
      injectedElements.forEach(element => element.remove());
    };
  }, []);

  // This component doesn't render anything visible
  return null;
};

export default HeadInjector;