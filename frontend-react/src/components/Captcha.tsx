import React, { useEffect, useRef, useState } from 'react';

interface CaptchaProps {
  width?: number;
  height?: number;
  onChange?: (value: string) => void;
}

const Captcha: React.FC<CaptchaProps> = ({ 
  width = 120, 
  height = 40, 
  onChange 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [code, setCode] = useState('');

  const generateCode = () => {
    const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = 0; i < 4; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
    return result;
  };

  const drawCaptcha = (code: string) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 设置背景色
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);

    // 绘制文字
    ctx.font = 'bold 24px Arial';
    ctx.textBaseline = 'middle';
    
    // 随机颜色和位置绘制每个字符
    for (let i = 0; i < code.length; i++) {
      const x = (width / 5) * (i + 1);
      const y = height / 2;
      const rot = (Math.random() - 0.5) * 0.3;
      
      ctx.save();
      ctx.translate(x, y);
      ctx.rotate(rot);
      
      // 使用珊瑚橙色系
      ctx.fillStyle = `rgb(${255 - Math.random() * 50}, ${127 - Math.random() * 30}, ${80 - Math.random() * 20})`;
      ctx.fillText(code[i], -10, 0);
      ctx.restore();
    }

    // 添加干扰线
    for (let i = 0; i < 3; i++) {
      ctx.beginPath();
      ctx.moveTo(Math.random() * width, Math.random() * height);
      ctx.lineTo(Math.random() * width, Math.random() * height);
      ctx.strokeStyle = `rgba(${255 - Math.random() * 50}, ${127 - Math.random() * 30}, ${80 - Math.random() * 20}, 0.5)`;
      ctx.stroke();
    }

    // 添加干扰点
    for (let i = 0; i < 30; i++) {
      ctx.beginPath();
      ctx.arc(Math.random() * width, Math.random() * height, 1, 0, 2 * Math.PI);
      ctx.fillStyle = `rgba(${255 - Math.random() * 50}, ${127 - Math.random() * 30}, ${80 - Math.random() * 20}, 0.5)`;
      ctx.fill();
    }
  };

  const refreshCaptcha = () => {
    const newCode = generateCode();
    setCode(newCode);
    onChange?.(newCode);
  };

  useEffect(() => {
    refreshCaptcha();
  }, []);

  useEffect(() => {
    if (code) {
      drawCaptcha(code);
    }
  }, [code]);

  return (
    <div style={{ cursor: 'pointer' }} onClick={refreshCaptcha}>
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        style={{ 
          border: '1px solid #e0e0e0',
          borderRadius: '4px',
        }}
      />
    </div>
  );
};

export default Captcha;
