import React from 'react';
import { Helmet } from 'react-helmet';

const StoreDetail = ({ store }) => {
  return (
    <div className="bg-gray-50 min-h-screen pb-12">
      <Helmet>
        <title>Verified {store.name} Coupons, Promo Codes & Cashback | Bonus Earned</title>
        <meta name="description" content={`Get verified ${store.name} coupons, promo codes and cashback offers. Save money on your next purchase with Bonus Earned.`} />
      </Helmet>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col lg:flex-row items-center lg:items-start lg:space-x-8">
          <div className="w-full lg:w-1/4 flex flex-col items-center lg:items-start mb-6 lg:mb-0">
            <div className="w-40 h-40 rounded-lg shadow-md bg-white p-4 flex items-center justify-center">
              {store.logo ? (
                <img
                  src={store.logo}
                  alt={`${store.name} logo`}
                  className="max-w-full max-h-full object-contain"
                />
              ) : (
                <div className="text-3xl font-bold text-gray-400">
                  {store.name.charAt(0)}
                </div>
              )}
            </div>
          </div>
          <div className="w-full lg:w-3/4">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-coral-600">{store.name}</h1>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoreDetail; 