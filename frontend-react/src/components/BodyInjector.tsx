import React, { useEffect } from 'react';
import { config } from '../config';

/**
 * BodyInjector Component
 *
 * This component injects custom HTML content before the closing </body> tag
 * based on the configuration defined in the config file.
 *
 * It uses useEffect to safely modify the DOM only on the client side.
 * Content is injected exactly as specified without any modifications to ensure
 * compatibility with external platform validation.
 */
const BodyInjector: React.FC = () => {
  useEffect(() => {
    // Only inject in development mode - in production, content is injected at build time
    if (config.env === 'live' || import.meta.env.PROD) {
      return;
    }

    // Keep track of injected elements for cleanup without modifying them
    const injectedElements: Element[] = [];

    // If there's no content to inject, return early
    if (!config.bodyContentList || config.bodyContentList.length === 0) {
      return;
    }

    // Get the document's body
    const body = document.body;

    // Create a document fragment to temporarily hold all elements
    // This is more efficient than manipulating the DOM multiple times
    const fragment = document.createDocumentFragment();

    // Create a temporary container for parsing HTML strings
    const container = document.createElement('div');

    // Process each string in the bodyContentList array
    config.bodyContentList.forEach((htmlString, index) => {
      if (!htmlString || typeof htmlString !== 'string') return;

      try {
        // Parse the HTML string
        container.innerHTML = htmlString.trim();

        // Add each child from the container to our fragment
        while (container.firstChild) {
          const element = container.firstChild;

          // Track this element for cleanup without modifying it
          if (element instanceof Element) {
            injectedElements.push(element);
          }

          // Move the element to our fragment (without any modifications)
          fragment.appendChild(element);
        }
      } catch (error) {
        console.error(`Failed to inject body content item ${index}:`, error);
      }
    });

    // Finally, append all new elements to the body (before closing </body>)
    body.appendChild(fragment);

    // Cleanup function to remove injected elements when component unmounts
    return () => {
      injectedElements.forEach(element => {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      });
    };
  }, []);

  // This component doesn't render anything visible
  return null;
};

export default BodyInjector;
