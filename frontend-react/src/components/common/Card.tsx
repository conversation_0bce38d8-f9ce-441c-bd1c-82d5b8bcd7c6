import React from 'react'
import { motion } from 'framer-motion'

interface CardProps {
  children: React.ReactNode
  className?: string
  hover?: boolean
  onClick?: () => void
}

const Card: React.FC<CardProps> = ({
  children,
  className = '',
  hover = false,
  onClick
}) => {
  const Component = onClick ? motion.button : motion.div
  
  return (
    <Component
      className={`bg-white rounded-lg shadow-sm ${hover ? 'hover:shadow-md' : ''} ${className}`}
      onClick={onClick}
      whileHover={hover ? { y: -5 } : undefined}
      transition={{ duration: 0.2 }}
    >
      {children}
    </Component>
  )
}

export default Card
