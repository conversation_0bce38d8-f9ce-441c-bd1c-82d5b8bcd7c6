import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import CouponButton from './CouponButton';

interface CouponCardProps {
  id?: number;
  merchantId?: number;
  merchantName: string;
  merchantLogo: string;
  merchantUniqueName?: string;
  title: string;
  code: string;
  description: string;
  endedAt?: string;
  merchantTrackUrl?: string;
  onClick?: () => void;
}

const CouponCard: React.FC<CouponCardProps> = ({
  merchantName,
  merchantLogo,
  merchantUniqueName,
  title,
  code,
  description,
  endedAt,
  onClick
}) => {
  // 判断优惠券是否已过期
  const isExpired = endedAt ? new Date(endedAt) < new Date() : false;

  // 格式化截止日期
  const formatEndDate = (dateStr?: string) => {
    if (!dateStr) return 'No expiration date';
    const date = new Date(dateStr);
    return `Expires: ${date.toLocaleDateString()}`;
  };

  // 截断长描述文本
  const truncateDescription = (text: string, maxLength: number = 80) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + '...';
  };

  return (
    <motion.div
      className="bg-white rounded-xl shadow-sm overflow-hidden h-full flex flex-col border border-gray-100"
      whileHover={{ y: -5, boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)" }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* 商家Logo和名称 */}
      <Link to={merchantUniqueName ? `/stores/${merchantUniqueName}` : '/stores'} className="block border-b border-gray-100">
        <div className="p-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 flex-shrink-0 bg-gray-100 rounded-full flex items-center justify-center overflow-hidden">
              {merchantLogo ? (
                <img
                  src={merchantLogo}
                  alt={merchantName}
                  className="w-full h-full object-contain"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(merchantName)}&background=f0f0f0&color=9f7aea&size=48`;
                  }}
                />
              ) : (
                <span className="text-xl font-bold bg-gradient-to-r from-purple-500 to-pink-400 bg-clip-text text-transparent">{merchantName.charAt(0)}</span>
              )}
            </div>
            <div className="text-sm font-medium text-gray-800 truncate">{merchantName}</div>
          </div>
        </div>
      </Link>

      {/* 优惠券内容 */}
      <div className="p-4 flex-1 flex flex-col">
        <h3 className="text-sm font-semibold line-clamp-2 mb-2 text-gray-900">{title}</h3>
        <p className="text-xs text-gray-600 mb-4 line-clamp-2">{truncateDescription(description, 100)}</p>

        {/* 过期标记或优惠券按钮 */}
        <div className="mt-auto">
          {isExpired ? (
            <div>
              <div className="bg-gray-100 text-gray-500 text-xs py-1 px-3 rounded-full inline-block mb-2">
                <span>Expired</span>
              </div>
              <div className="text-xs text-gray-400">{formatEndDate(endedAt)}</div>
            </div>
          ) : (
            <>
              {endedAt && (
                <div className="text-xs text-gray-500 mb-2">{formatEndDate(endedAt)}</div>
              )}
              <CouponButton code={code} onClick={onClick} />
            </>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default CouponCard;