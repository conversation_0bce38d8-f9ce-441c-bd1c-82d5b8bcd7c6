.coupon-button {
  position: relative;
  transition: all 0.3s ease;
}

.coupon-button:hover {
  transform: translateY(-1px);
}

.coupon-button::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 12px;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-15deg);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% {
    transform: skewX(-15deg) translateX(-100px);
  }
  50% {
    transform: skewX(-15deg) translateX(100px);
  }
  100% {
    transform: skewX(-15deg) translateX(100px);
  }
}
