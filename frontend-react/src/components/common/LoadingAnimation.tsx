import React from 'react'
import { motion } from 'framer-motion'

export const LoadingAnimation = () => {
  return (
    <div className="flex justify-center items-center space-x-2 hardware-accelerate">
      <div className="w-2 h-2 bg-coral-500 rounded-full animate-pulse" style={{ animationDelay: '0ms' }} />
      <div className="w-2 h-2 bg-coral-500 rounded-full animate-pulse" style={{ animationDelay: '200ms' }} />
      <div className="w-2 h-2 bg-coral-500 rounded-full animate-pulse" style={{ animationDelay: '400ms' }} />
    </div>
  )
}

export default LoadingAnimation
