import React from 'react'
import { motion } from 'framer-motion'

interface SkeletonProps {
  type: 'store' | 'category' | 'text'
  count?: number
}

export const SkeletonLoader: React.FC<SkeletonProps> = ({ type, count = 1 }) => {
  const shimmer = {
    hidden: { x: '-100%' },
    visible: { 
      x: '100%',
      transition: {
        repeat: Infinity,
        duration: 1.5,
        ease: 'linear'
      }
    }
  }

  const renderSkeleton = () => {
    switch (type) {
      case 'store':
        return (
          <div className="relative bg-gray-100 rounded-xl p-4 overflow-hidden">
            <div className="w-16 h-16 bg-gray-200 rounded-lg mb-3" />
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
            <div className="h-3 bg-gray-200 rounded w-1/2" />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
              variants={shimmer}
              initial="hidden"
              animate="visible"
            />
          </div>
        )
      case 'category':
        return (
          <div className="relative bg-gray-100 rounded-xl p-4 overflow-hidden">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gray-200 rounded-lg" />
              <div className="h-4 bg-gray-200 rounded w-24" />
            </div>
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
              variants={shimmer}
              initial="hidden"
              animate="visible"
            />
          </div>
        )
      case 'text':
        return (
          <div className="relative overflow-hidden">
            <div className="h-4 bg-gray-100 rounded w-full" />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
              variants={shimmer}
              initial="hidden"
              animate="visible"
            />
          </div>
        )
    }
  }

  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <React.Fragment key={index}>
          {renderSkeleton()}
        </React.Fragment>
      ))}
    </>
  )
}

export default SkeletonLoader
