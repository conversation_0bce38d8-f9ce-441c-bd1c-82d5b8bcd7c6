import React from 'react';
import { motion } from 'framer-motion';
import './CouponButton.css';

interface CouponButtonProps {
  code: string;
  onClick?: () => void;
  className?: string;
}

const CouponButton: React.FC<CouponButtonProps> = ({ code, onClick, className = '' }) => {
  // 只显示优惠券代码的前半部分
  const halfCode = (code: string) => {
    return code.slice(0, Math.ceil(code.length / 2));
  };

  return (
    <motion.button
      className={`coupon-button group inline-flex h-10 items-stretch overflow-hidden shadow-md transition-all duration-300 ${className}`}
      onClick={onClick}
      whileHover={{ scale: 1.02, boxShadow: "0 4px 12px rgba(159, 122, 234, 0.2)" }}
      whileTap={{ scale: 0.98 }}
    >
      <span
        className="inline-flex min-w-[100px] items-center justify-center bg-white px-3 font-mono text-sm font-medium text-gray-900 border-2 border-r-0 border-purple-500 rounded-l-lg relative overflow-hidden group-hover:bg-gray-50 transition-colors duration-300"
        style={{ letterSpacing: '0.5px' }}
      >
        {/* 背景装饰元素 */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* 代码文本 */}
        <div className="relative whitespace-nowrap">
          {halfCode(code)}
        </div>

        {/* 剪刀图标 */}
        <div className="absolute left-0 top-0 h-full w-5 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform -translate-x-full group-hover:translate-x-0">
          <svg className="w-4 h-4 text-purple-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6 9C7.65685 9 9 7.65685 9 6C9 4.34315 7.65685 3 6 3C4.34315 3 3 4.34315 3 6C3 7.65685 4.34315 9 6 9Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M6 21C7.65685 21 9 19.6569 9 18C9 16.3431 7.65685 15 6 15C4.34315 15 3 16.3431 3 18C3 19.6569 4.34315 21 6 21Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8.5 6.5L20 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8.5 17.5L20 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      </span>
      <span className="inline-flex items-center justify-center px-3 bg-gradient-to-r from-purple-500 to-pink-400 font-medium text-sm text-white rounded-r-lg whitespace-nowrap group-hover:from-purple-600 group-hover:to-pink-500 transition-all duration-300">
        Get Code
      </span>
    </motion.button>
  );
};

export default CouponButton;
