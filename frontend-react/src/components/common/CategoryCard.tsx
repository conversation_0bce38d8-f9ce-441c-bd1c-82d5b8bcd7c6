import React, { ReactNode } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaShoppingBag, FaArrowRight } from 'react-icons/fa';

interface CategoryCardProps {
  id: number;
  name: string;
  icon: string | ReactNode;
  index: number;
}

const CategoryCard: React.FC<CategoryCardProps> = ({ id, name, icon, index }) => {
  return (
    <Link
      to={`/stores?category_id=${id}`}
      className="block h-full"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.05 }}
        className="flex flex-col items-center justify-center h-full p-2"
        whileHover={{ y: -5 }}
      >
        {/* 图标容器 */}
        <div className="relative mb-3 w-16 h-16 rounded-full bg-gradient-to-br from-purple-50 to-pink-50 flex items-center justify-center">
          {/* 图标 */}
          {typeof icon === 'string' ? (
            icon ? (
              <img 
                src={icon} 
                alt={name}
                className="w-8 h-8 object-contain drop-shadow-sm"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  (e.currentTarget.nextSibling as HTMLElement).style.display = 'block';
                }}
              />
            ) : (
              <FaShoppingBag className="w-7 h-7 text-purple-500" />
            )
          ) : (
            <div className="flex items-center justify-center">
              {icon || <FaShoppingBag className="w-7 h-7 text-purple-500" />}
            </div>
          )}
          <FaShoppingBag className="w-7 h-7 text-purple-500 hidden" />
        </div>
        
        {/* 分类名称 */}
        <div className="text-center">
          <h3 className="text-gray-800 font-medium text-sm lg:text-base hover:text-purple-600 transition-colors duration-300">
            {name}
          </h3>
          
          {/* 浏览商店按钮 */}
          <div className="flex items-center justify-center text-xs bg-gradient-to-r from-purple-500 to-pink-400 bg-clip-text text-transparent font-medium mt-1 opacity-0 group-hover:opacity-100 transition-all duration-300">
            <span className="border-b border-purple-300 pb-0.5">Browse</span>
            <FaArrowRight size={10} className="ml-1" />
          </div>
        </div>
      </motion.div>
    </Link>
  );
};

export default CategoryCard;
