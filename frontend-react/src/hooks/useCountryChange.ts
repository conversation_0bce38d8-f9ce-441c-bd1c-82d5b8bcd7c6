import { useEffect, useRef } from 'react'
import { useSelectedCountry } from './useSelectedCountry'

// 监听国家变化的钩子
export const useCountryChange = (callback: () => void) => {
  const selectedCountry = useSelectedCountry()
  const previousCountryRef = useRef(selectedCountry.code)

  useEffect(() => {
    // 如果国家发生变化，执行回调
    if (previousCountryRef.current !== selectedCountry.code) {
      previousCountryRef.current = selectedCountry.code
      callback()
    }
  }, [selectedCountry.code, callback])

  return selectedCountry
}
