import { useState, useEffect } from 'react'
import { Country } from '../types'

// 默认美国
const defaultCountry: Country = {
  id: 1,
  name: 'United States',
  code: 'US',
  flag: '🇺🇸',
  merchant_count: 6013,
  status: 1
}

// 获取当前选择的国家的钩子
export const useSelectedCountry = () => {
  const [selectedCountry, setSelectedCountry] = useState<Country>(defaultCountry)

  useEffect(() => {
    // 从localStorage获取保存的国家
    try {
      const savedCountry = localStorage.getItem('selectedCountry')
      if (savedCountry) {
        const country = JSON.parse(savedCountry)
        setSelectedCountry(country)
      }
    } catch (error) {
      console.warn('Failed to parse saved country from localStorage:', error)
      setSelectedCountry(defaultCountry)
    }

    // 监听localStorage变化
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'selectedCountry' && e.newValue) {
        try {
          const country = JSON.parse(e.newValue)
          setSelectedCountry(country)
        } catch (error) {
          console.warn('Failed to parse country from storage event:', error)
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  return selectedCountry
}
