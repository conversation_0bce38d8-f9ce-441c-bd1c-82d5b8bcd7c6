import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import api from '../lib/api'

export interface Country {
  id: number
  name: string
  code: string
  flag: string
  merchant_count: number
  status: number
}

interface CountryContextType {
  selectedCountry: Country | null
  countries: Country[]
  setSelectedCountry: (country: Country) => void
  getCountries: () => Promise<Country[]>
  loading: boolean
}

const CountryContext = createContext<CountryContextType | undefined>(undefined)

export const useCountry = () => {
  const context = useContext(CountryContext)
  if (context === undefined) {
    throw new Error('useCountry must be used within a CountryProvider')
  }
  return context
}

interface CountryProviderProps {
  children: React.ReactNode
}

export const CountryProvider: React.FC<CountryProviderProps> = ({ children }) => {
  const [selectedCountry, setSelectedCountryState] = useState<Country | null>(null)
  const [countries, setCountries] = useState<Country[]>([])
  const [loading, setLoading] = useState(false)

  // 默认美国
  const defaultCountry: Country = {
    id: 1,
    name: 'United States',
    code: 'US',
    flag: '🇺🇸',
    merchant_count: 6013,
    status: 1
  }

  // 获取国家列表
  const getCountries = useCallback(async (): Promise<Country[]> => {
    try {
      setLoading(true)
      const response = await api.get<{
        total: number
        page: number
        page_size: number
        country_list: Country[]
      }>('/countries', {
        params: {
          page: 1,
          page_size: 100
        }
      })

      const countryList = response.country_list || []
      setCountries(countryList)
      return countryList
    } catch (error) {
      console.error('Error fetching countries:', error)
      // 如果获取失败，返回默认国家列表
      const defaultCountries = [defaultCountry]
      setCountries(defaultCountries)
      return defaultCountries
    } finally {
      setLoading(false)
    }
  }, [])

  // 设置选中的国家
  const setSelectedCountry = useCallback((country: Country) => {
    setSelectedCountryState(country)
    // 保存到localStorage
    localStorage.setItem('selectedCountry', JSON.stringify(country))
  }, [])

  // 初始化
  useEffect(() => {
    const initializeCountry = async () => {
      // 从localStorage获取保存的国家
      const savedCountry = localStorage.getItem('selectedCountry')
      if (savedCountry) {
        try {
          const country = JSON.parse(savedCountry)
          setSelectedCountryState(country)
        } catch (error) {
          console.error('Error parsing saved country:', error)
          setSelectedCountryState(defaultCountry)
        }
      } else {
        setSelectedCountryState(defaultCountry)
      }

      // 获取国家列表
      await getCountries()
    }

    initializeCountry()
  }, [getCountries])

  const value: CountryContextType = {
    selectedCountry,
    countries,
    setSelectedCountry,
    getCountries,
    loading
  }

  return (
    <CountryContext.Provider value={value}>
      {children}
    </CountryContext.Provider>
  )
}
