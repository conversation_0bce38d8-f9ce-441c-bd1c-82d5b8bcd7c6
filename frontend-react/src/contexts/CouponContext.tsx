import React, { createContext, useContext, useCallback } from 'react';
import api from '../lib/api';
import { Coupon, CouponResponse } from '../api/coupon';

interface GetCouponsParams {
  page?: number;
  page_size?: number;
  search?: string;
  merchant_id?: number;
}

interface CouponContextType {
  getCoupons: (params: GetCouponsParams) => Promise<CouponResponse>;
  getMerchantCoupons: (merchantId: number) => Promise<Coupon[]>;
}

const CouponContext = createContext<CouponContextType | undefined>(undefined);

export function CouponProvider({ children }: { children: React.ReactNode }) {
  const getCoupons = useCallback(async (params: GetCouponsParams): Promise<CouponResponse> => {
    try {
      const usercode = localStorage.getItem('usercode');
      const headers: Record<string, string> = {};
      
      if (usercode) {
        headers['X-User-Code'] = usercode;
      }
      
      const response = await api.get<CouponResponse>('/coupons', { 
        params,
        headers 
      });
      
      return {
        total: response.total || 0,
        page: response.page || 1,
        page_size: response.page_size || 10,
        coupon_list: response.coupon_list || []
      };
    } catch (error) {
      console.error('Error fetching coupons:', error);
      return {
        total: 0,
        page: 1,
        page_size: 10,
        coupon_list: []
      };
    }
  }, []);
  
  const getMerchantCoupons = useCallback(async (merchantId: number): Promise<Coupon[]> => {
    try {
      const usercode = localStorage.getItem('usercode');
      const headers: Record<string, string> = {};
      
      if (usercode) {
        headers['X-User-Code'] = usercode;
      }
      
      const response = await api.get<Coupon[]>(`/coupons`, { 
        params: { merchant_id: merchantId },
        headers 
      });
      
      return response || [];
    } catch (error) {
      console.error(`Error fetching coupons for merchant ${merchantId}:`, error);
      return [];
    }
  }, []);

  const value = {
    getCoupons,
    getMerchantCoupons
  };

  return (
    <CouponContext.Provider value={value}>
      {children}
    </CouponContext.Provider>
  );
}

export function useCoupon() {
  const context = useContext(CouponContext);
  if (context === undefined) {
    throw new Error('useCoupon must be used within a CouponProvider');
  }
  return context;
} 