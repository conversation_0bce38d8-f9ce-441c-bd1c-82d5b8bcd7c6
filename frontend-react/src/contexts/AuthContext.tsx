import React, { createContext, useContext, useState, useCallback, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import api from '../lib/api'
import { User } from '../types'

interface AuthContextType {
  user: User | null
  loading: boolean
  error: string | null
  login: (email: string, password: string) => Promise<void>
  register: (email: string, password: string, nickname: string, phone?: string) => Promise<void>
  logout: () => Promise<void>
}

interface LoginResponse {
  user_info: {
    email: string
    user_code: string
    nickname: string
    payment_info?: {
      paypal_email?: string
    }
  }
  token: string
}

// 创建带有默认值的上下文，避免undefined错误
const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: false,
  error: null,
  login: async () => {},
  register: async () => {},
  logout: async () => {},
})

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const navigate = useNavigate()
  const location = useLocation()

  const login = useCallback(async (email: string, password: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.post<LoginResponse>('/auth/login', {
        email,
        password,
      })
      
      // 保存 token、用户信息和 usercode 到本地存储
      const { user_info, token } = response
      localStorage.setItem('token', token)
      localStorage.setItem('user', JSON.stringify(user_info))
      localStorage.setItem('usercode', user_info.user_code)
      
      setUser({
        id: '', // 这个字段可能需要从后端获取
        email: user_info.email,
        nickname: user_info.nickname,
        user_code: user_info.user_code,
        status: 1,
        payment_info: user_info.payment_info
      })
    } catch (error) {
      console.error('Login error:', error)
      setError('Login failed. Please check your credentials and try again.')
      throw error
    } finally {
      setLoading(false)
    }
  }, [])

  const register = useCallback(async (email: string, password: string, nickname: string, phone?: string) => {
    setLoading(true)
    setError(null)
    
    try {
      await api.post('/auth/register', {
        email,
        password,
        nickname,
        phone,
      })
      
      // 注册成功后自动登录
      await login(email, password)
    } catch (error: any) {
      console.error('Registration error:', error)
      // 添加更具体的错误信息
      const errorMsg = error.message || 'Registration failed. Please try again later.'
      setError(errorMsg)
      throw error
    } finally {
      setLoading(false)
    }
  }, [login])

  const logout = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      await api.post('/auth/logout')
    } catch (error) {
      console.error('Logout failed:', error)
    } finally {
      // 清除本地存储和状态
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('usercode')
      setUser(null)
      setLoading(false)

      // 如果当前在首页，刷新页面；否则跳转到首页
      if (location.pathname === '/') {
        window.location.reload()
      } else {
        navigate('/')
      }
    }
  }, [navigate, location.pathname])

  // 初始化时从本地存储加载用户信息
  useEffect(() => {
    const initAuth = async () => {
      setLoading(true)
      try {
        const savedToken = localStorage.getItem('token')
        const savedUser = localStorage.getItem('user')
        const savedUsercode = localStorage.getItem('usercode')

        if (savedToken && savedUser && savedUsercode) {
          try {
            // 先设置用户信息
            setUser(JSON.parse(savedUser))
            
            // 在后台验证 token，如果失败也不立即登出用户
            await api.get('/users/me/profile')
          } catch (error: any) {
            console.error('Token validation error:', error)
            // 只有在确认是 401 错误时才清除认证信息
            if (error.status === 401) {
              localStorage.removeItem('token')
              localStorage.removeItem('user')
              localStorage.removeItem('usercode')
              setUser(null)
            }
            // 其他错误（如网络问题）则保持用户登录状态
            console.error('Failed to validate token:', error)
          }
        } else {
          setUser(null)
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error)
        setUser(null)
      } finally {
        setLoading(false)
      }
    }

    initAuth()
  }, [])

  const contextValue: AuthContextType = {
    user,
    loading,
    error,
    login,
    register,
    logout,
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  // 不再需要检查undefined，因为我们提供了默认值
  return context
}
