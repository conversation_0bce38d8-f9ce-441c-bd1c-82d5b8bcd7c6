import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import viteImagemin from 'vite-plugin-imagemin'
import { visualizer } from 'rollup-plugin-visualizer'
import { splitVendorChunkPlugin } from 'vite'
import viteCompression from 'vite-plugin-compression'
import fs from 'fs'

// Helper function to extract string literals from TypeScript/JavaScript code
function extractStringLiterals(code: string): string[] {
  const items: string[] = [];
  let i = 0;

  while (i < code.length) {
    const char = code[i];

    // Skip whitespace and comments
    if (char === ' ' || char === '\n' || char === '\t' || char === '\r') {
      i++;
      continue;
    }

    // Skip single-line comments
    if (char === '/' && code[i + 1] === '/') {
      while (i < code.length && code[i] !== '\n') {
        i++;
      }
      continue;
    }

    // Skip multi-line comments
    if (char === '/' && code[i + 1] === '*') {
      i += 2;
      while (i < code.length - 1 && !(code[i] === '*' && code[i + 1] === '/')) {
        i++;
      }
      i += 2;
      continue;
    }

    // Skip commas and other punctuation
    if (char === ',' || char === ';') {
      i++;
      continue;
    }

    // Handle string literals
    if (char === '"' || char === "'" || char === '`') {
      const quote = char;
      i++; // Skip opening quote
      let content = '';

      while (i < code.length) {
        if (code[i] === quote) {
          // Found closing quote
          items.push(content);
          i++; // Skip closing quote
          break;
        } else if (code[i] === '\\') {
          // Handle escaped characters
          i++; // Skip backslash
          if (i < code.length) {
            const escaped = code[i];
            switch (escaped) {
              case 'n': content += '\n'; break;
              case 't': content += '\t'; break;
              case 'r': content += '\r'; break;
              case '\\': content += '\\'; break;
              case '"': content += '"'; break;
              case "'": content += "'"; break;
              case '`': content += '`'; break;
              default: content += escaped; break;
            }
            i++;
          }
        } else {
          content += code[i];
          i++;
        }
      }
    } else {
      i++;
    }
  }

  return items;
}

// Custom plugin to inject head and body content into index.html during build
function htmlContentInjectionPlugin() {
  return {
    name: 'html-content-injection',
    transformIndexHtml: {
      enforce: 'pre',
      transform(html: string, context: any) {
        // Only inject during build, not in dev mode
        if (context.server) {
          return html;
        }

        try {
          // Read the config file to extract headContent and bodyContentList
          const configPath = path.resolve(__dirname, 'src/config/index.ts');
          const configContent = fs.readFileSync(configPath, 'utf-8');

          let modifiedHtml = html;

          // Process headContent - use a more sophisticated approach
          const headContentMatch = configContent.match(/headContent:\s*\[([\s\S]*?)\],?\s*(?:\/\/.*?$|$|\n\s*\/\/|\n\s*bodyContentList)/m);
          if (headContentMatch) {
            const headContentStr = headContentMatch[1];
            const headItems = extractStringLiterals(headContentStr);

            if (headItems.length > 0) {
              const headContentToInject = headItems.join('\n    ');
              modifiedHtml = modifiedHtml.replace(
                '</head>',
                `    ${headContentToInject}\n  </head>`
              );
            }
          }

          // Process bodyContentList
          const bodyContentMatch = configContent.match(/bodyContentList:\s*\[([\s\S]*?)\],?\s*(?:\/\/.*?$|$|\n\s*\/\/|\n\s*\})/m);
          if (bodyContentMatch) {
            const bodyContentStr = bodyContentMatch[1];
            const bodyItems = extractStringLiterals(bodyContentStr);

            if (bodyItems.length > 0) {
              const bodyContentToInject = bodyItems.join('\n    ');
              modifiedHtml = modifiedHtml.replace(
                '</body>',
                `    ${bodyContentToInject}\n  </body>`
              );
            }
          }

          return modifiedHtml;
        } catch (error) {
          console.warn('Failed to inject head/body content:', error);
          return html;
        }
      }
    }
  };
}

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
    base: '/',  // 使用绝对路径，因为应用部署在域名根目录
    plugins: [
      htmlContentInjectionPlugin(),
      react({
        babel: {
          plugins: [
            ['@babel/plugin-transform-react-jsx', { runtime: 'automatic' }]
          ]
        }
      }),
      splitVendorChunkPlugin(),
      viteImagemin({
        gifsicle: {
          optimizationLevel: 7,
          interlaced: false,
        },
        optipng: {
          optimizationLevel: 7,
        },
        mozjpeg: {
          quality: 75, // 降低质量以获取更好的压缩率
          progressive: true, // 使用渐进式JPEG
        },
        pngquant: {
          quality: [0.7, 0.8], // 降低质量范围以获取更好的压缩
          speed: 4,
        },
        svgo: {
          plugins: [
            {
              name: 'removeViewBox',
              active: false, // 不要移除viewBox以保持SVG响应式
            },
            {
              name: 'removeEmptyAttrs',
              active: true,
            },
            {
              name: 'cleanupIDs',
              active: true,
            },
            {
              name: 'removeDimensions',
              active: true,
            },
          ],
        },
        webp: {
          quality: 80, // 添加WebP转换支持
        },
      }),
      // Gzip压缩
      viteCompression({
        algorithm: 'gzip',
        ext: '.gz',
        threshold: 10240, // 只有大于10kb的文件才会被压缩
      }),
      // Brotli压缩 (比Gzip更高效)
      viteCompression({
        algorithm: 'brotliCompress',
        ext: '.br',
        threshold: 10240,
        compressionOptions: {
          level: 11, // 最高压缩级别
        },
      }),
      visualizer({
        open: false,
        gzipSize: true,
        brotliSize: true,
      }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    server: {
      port: 3001,
      host: true,
      proxy: {
        '/api': {
          target: 'http://localhost:8080',
          changeOrigin: true,
        },
      },
      historyApiFallback: true,  // 添加这个配置来支持 HTML5 History API
    },
    build: {
      outDir: 'dist',
      sourcemap: mode !== 'live',
      rollupOptions: {
        output: {
          manualChunks: {
            'react-vendor': ['react', 'react-dom', 'react/jsx-runtime'],
            'router-vendor': ['react-router-dom'],
            'ui-vendor': ['framer-motion', '@headlessui/react'],
            'form-vendor': ['react-hook-form', 'yup'],
            'utils-vendor': ['lodash', 'axios', 'dayjs'],
            'markdown-vendor': ['react-markdown', 'rehype-raw', 'remark-gfm'],
            'syntax-vendor': ['prismjs', 'react-syntax-highlighter'],
          },
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name ?? '';
            const extType = info.split('.').at(1) ?? '';
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType)) {
              return `assets/images/[name]-[hash][extname]`;
            }
            if (/woff|woff2|eot|ttf|otf/i.test(extType)) {
              return `assets/fonts/[name]-[hash][extname]`;
            }
            return `assets/[name]-[hash][extname]`;
          },
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
        },
      },
      chunkSizeWarningLimit: 2000,
      cssCodeSplit: true,
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: mode === 'live',
          drop_debugger: mode === 'live',
          pure_funcs: mode === 'live' ? ['console.log', 'console.debug', 'console.info'] : [],
        },
      },
      // 新增构建优化配置
      assetsInlineLimit: 4096, // 小于4kb的资源内联为base64，减少请求
      reportCompressedSize: false, // 禁用压缩大小报告，加快构建
      emptyOutDir: true, // 清空输出目录
    },
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        'framer-motion',
        'react-icons/fa',
        'axios',
        'dayjs'
      ],
      exclude: ['@vite/client', '@vite/env'],
      esbuildOptions: {
        target: 'es2020' // 更现代的目标，减小包大小
      }
    },
    esbuild: {
      logOverride: { 'this-is-undefined-in-esm': 'silent' }
    }
  }
})
