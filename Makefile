.PHONY: all build clean run migrate test test-api frontend-build frontend-build-prod

# Build all services
build:
	CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o bin/api cmd/api/main.go cmd/api/wire_gen.go
	CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o bin/track cmd/track/main.go
	CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o bin/task cmd/task/main.go
	CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o bin/migrate cmd/migrate/main.go
	$(MAKE) frontend-build-prod

# Build for local development
build-local:
	CGO_ENABLED=0 go build -o bin/api cmd/api/main.go
	CGO_ENABLED=0 go build -o bin/track cmd/track/main.go
	CGO_ENABLED=0 go build -o bin/migrate cmd/migrate/main.go

# Clean build artifacts
clean:
	rm -rf bin/
	rm -rf frontend/.next
	rm -rf frontend/out
	rm -rf deploy/
	rm -f deploy.tar.gz

# Run database migrations
migrate:
	go run cmd/migrate/main.go

# Run API service (local)
run-api:
	go run cmd/api/main.go cmd/api/wire_gen.go -env local

# Run Track service (local)
run-track:
	go run cmd/track/main.go -env local

# Run task service (local)
run-task:
	go run cmd/task/main.go -env local

# Run API service (live)
run-api-live:
	./bin/api -env live

# Run Track service (live)
run-track-live:
	./bin/track -env live

# Build frontend for development
frontend-build:
	cd frontend && npm install && npm run build

# Build frontend for production
frontend-build-prod:
	cd frontend-react && npm install && npm run build:live

# Build and package for deployment
deploy-package: clean build
	mkdir -p deploy
	# Copy backend binaries
	cp -r bin deploy/

	# Copy frontend static files
	cp -r frontend/out deploy/frontend

	# Create deployment package
	tar -czf deploy.tar.gz deploy/
	rm -rf deploy/

# Deploy instructions
deploy-help:
	@echo "Deployment Instructions:"
	@echo "1. Build and create deployment package:"
	@echo "   make deploy-package"
	@echo ""
	@echo "2. Upload deploy.tar.gz to your server:"
	@echo "   scp deploy.tar.gz user@your-server:/path/to/app"
	@echo ""
	@echo "3. On the server, extract the package:"
	@echo "   tar -xzf deploy.tar.gz"
	@echo ""
	@echo "4. Configure your web server (e.g., nginx) to serve the static files:"
	@echo "   Location: deploy/frontend"
	@echo ""
	@echo "Example nginx configuration:"
	@echo "server {"
	@echo "    listen 80;"
	@echo "    server_name your-domain.com;"
	@echo "    root /path/to/app/deploy/frontend;"
	@echo "    index index.html;"
	@echo "    location / {"
	@echo "        try_files \$$uri \$$uri.html \$$uri/ /index.html;"
	@echo "    }"
	@echo "}"
