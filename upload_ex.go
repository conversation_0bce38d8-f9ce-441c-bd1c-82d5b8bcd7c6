package main

import (
	"crypto/sha256"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/cheggaaa/pb/v3"
	"github.com/gin-gonic/gin"
	"github.com/pkg/sftp"
	"github.com/robfig/cron/v3"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"golang.org/x/crypto/ssh"
)

var (
	log = logrus.New()
	// 内存池，用于复用缓冲区
	bufPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 32*1024)
		},
	}
	// 用于跟踪上传进度
	totalBytes    int64
	uploadedBytes int64
	// Node.js版本要求
	requiredNodeVersion = "20.11.1"
	// 保存Node.js安装路径
	nodejsInstallPath = ""
)

// 检查并安装Node.js环境
func checkAndInstallNode() error {
	// 检查Node.js是否已安装
	nodeCmd := exec.Command("node", "-v")
	nodeOutput, err := nodeCmd.Output()
	if err != nil {
		log.Info("Node.js未安装，开始下载安装...")
		return downloadAndInstallNode()
	}

	// 检查npm是否已安装
	npmCmd := exec.Command("npm", "-v")
	_, err = npmCmd.Output()
	if err != nil {
		log.Info("npm未安装，开始下载安装...")
		return downloadAndInstallNode()
	}

	// 检查Node.js版本是否满足要求
	currentVersion := strings.TrimSpace(string(nodeOutput))
	if !isVersionSatisfied(currentVersion, requiredNodeVersion) {
		log.Infof("当前Node.js版本%s不满足要求，需要版本>=%s，开始更新...", currentVersion, requiredNodeVersion)
		return downloadAndInstallNode()
	}

	return nil
}

// 下载并安装Node.js
func downloadAndInstallNode() error {
	osType := runtime.GOOS
	arch := runtime.GOARCH

	// 验证支持的架构
	var archSuffix string
	switch arch {
	case "amd64":
		archSuffix = "x64"
	case "arm64":
		archSuffix = "arm64"
	default:
		return fmt.Errorf("不支持的架构: %s", arch)
	}

	// 检查解压工具可用性（macOS/Linux 需要 tar，Windows 需要 unzip）
	if osType != "windows" {
		if _, err := exec.LookPath("tar"); err != nil {
			return fmt.Errorf("未找到 tar 命令，请确保系统中已安装 tar")
		}
	} else {
		if _, err := exec.LookPath("unzip"); err != nil {
			return fmt.Errorf("未找到 unzip 命令，请确保系统中已安装 unzip")
		}
	}

	// 构建下载URL
	baseURL := "https://nodejs.org/dist/v" + requiredNodeVersion
	var fileName string
	var extractDir string
	switch osType {
	case "darwin":
		fileName = fmt.Sprintf("node-v%s-darwin-%s.tar.gz", requiredNodeVersion, archSuffix)
		extractDir = fmt.Sprintf("node-v%s-darwin-%s", requiredNodeVersion, archSuffix)
	case "linux":
		fileName = fmt.Sprintf("node-v%s-linux-%s.tar.gz", requiredNodeVersion, archSuffix)
		extractDir = fmt.Sprintf("node-v%s-linux-%s", requiredNodeVersion, archSuffix)
	case "windows":
		fileName = fmt.Sprintf("node-v%s-win-%s.zip", requiredNodeVersion, archSuffix)
		extractDir = fmt.Sprintf("node-v%s-win-%s", requiredNodeVersion, archSuffix)
	default:
		return fmt.Errorf("不支持的操作系统: %s", osType)
	}

	url := fmt.Sprintf("%s/%s", baseURL, fileName)
	logrus.Infof("开始下载Node.js: %s", url)

	// 设置HTTP客户端，模拟 wget 的请求头
	client := &http.Client{
		Timeout: 60 * time.Second,
	}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 添加常见的请求头，模拟 wget
	req.Header.Set("User-Agent", "Wget/1.21.3")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Connection", "Keep-Alive")

	// 重试机制
	maxAttempts := 3
	delay := 5 * time.Second
	var resp *http.Response
	for attempt := 1; attempt <= maxAttempts; attempt++ {
		logrus.Infof("尝试第 %d/%d 次下载...", attempt, maxAttempts)
		resp, err = client.Do(req)
		if err != nil {
			logrus.Warnf("下载失败，错误: %v", err)
			if attempt == maxAttempts {
				return fmt.Errorf("下载失败，经过 %d 次重试，错误: %v", maxAttempts, err)
			}
			logrus.Infof("等待 %v 后重试...", delay)
			time.Sleep(delay)
			continue
		}

		// 检查状态码
		if resp.StatusCode != http.StatusOK {
			logrus.Warnf("下载失败，状态码: %d", resp.StatusCode)
			resp.Body.Close()
			if attempt == maxAttempts {
				return fmt.Errorf("下载失败，经过 %d 次重试，状态码: %d", maxAttempts, resp.StatusCode)
			}
			logrus.Infof("等待 %v 后重试...", delay)
			time.Sleep(delay)
			continue
		}

		logrus.Info("下载成功")
		break
	}

	if resp == nil {
		return fmt.Errorf("下载失败，经过 %d 次重试后仍无法获取响应", maxAttempts)
	}
	defer resp.Body.Close()

	// 使用 os.TempDir + 手动创建目录
	tempBase := os.TempDir()
	tmpDir := filepath.Join(tempBase, fmt.Sprintf("nodejs-%d", time.Now().UnixNano()))
	if err := os.Mkdir(tmpDir, 0755); err != nil {
		return fmt.Errorf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	// 保存下载文件
	filePath := filepath.Join(tmpDir, fileName)
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	// 使用缓冲写入
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("保存文件失败: %v", err)
	}

	// 根据操作系统解压
	if osType == "windows" {
		cmd := exec.Command("unzip", filePath, "-d", tmpDir)
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("解压文件失败: %v", err)
		}
	} else {
		// Linux 和 macOS 使用 tar
		cmd := exec.Command("tar", "-xzf", filePath, "-C", tmpDir)
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("解压文件失败: %v", err)
		}
	}

	// 确定 Node.js 二进制路径
	nodeBinDir := filepath.Join(tmpDir, extractDir, "bin")
	nodePath := filepath.Join(nodeBinDir, "node")
	if _, err := os.Stat(nodePath); os.IsNotExist(err) {
		return fmt.Errorf("未找到Node.js可执行文件: %s", nodePath)
	}

	// 确保 node 可执行文件有执行权限（macOS/Linux）
	if osType != "windows" {
		if err := os.Chmod(nodePath, 0755); err != nil {
			return fmt.Errorf("设置 node 可执行权限失败: %v", err)
		}
	}

	// 更新 PATH（仅当前进程）
	currentPath := os.Getenv("PATH")
	newPath := nodeBinDir + string(os.PathListSeparator) + currentPath
	if err := os.Setenv("PATH", newPath); err != nil {
		return fmt.Errorf("设置环境变量失败: %v", err)
	}

	// 确保npm可执行文件存在并添加到PATH
	npmPath := filepath.Join(nodeBinDir, "npm")
	if osType == "windows" {
		npmPath += ".cmd"
	}

	// 设置npm可执行权限
	if osType != "windows" {
		if err := os.Chmod(npmPath, 0755); err != nil {
			return fmt.Errorf("设置npm可执行权限失败: %v", err)
		}
	}

	// 验证 Node.js 安装
	cmd := exec.Command(nodePath, "--version")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("验证Node.js安装失败: %v, 输出: %s", err, output)
	}

	// 验证并初始化npm
	npmInitPath := filepath.Join(nodeBinDir, "npm")
	if osType == "windows" {
		npmInitPath += ".cmd"
	}

	// 验证npm
	npmCmd := exec.Command(npmInitPath, "--version")
	npmOutput, err := npmCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("验证npm安装失败: %v, 输出: %s", err, npmOutput)
	}

	// 保存安装路径以供后续使用
	nodejsInstallPath = tmpDir
	logrus.Infof("Node.js安装完成，版本: %s", strings.TrimSpace(string(output)))
	logrus.Infof("npm安装完成，版本: %s", strings.TrimSpace(string(npmOutput)))
	return nil
}

// 检查版本是否满足要求
func isVersionSatisfied(current, required string) bool {
	current = strings.TrimPrefix(current, "v")
	required = strings.TrimPrefix(required, "v")

	currentParts := strings.Split(current, ".")
	requiredParts := strings.Split(required, ".")

	for i := 0; i < len(requiredParts); i++ {
		if i >= len(currentParts) {
			return false
		}
		currentNum, _ := strconv.Atoi(currentParts[i])
		requiredNum, _ := strconv.Atoi(requiredParts[i])
		if currentNum < requiredNum {
			return false
		}
		if currentNum > requiredNum {
			return true
		}
	}
	return true
}

func init() {
	// 检查并安装Node.js环境
	if err := checkAndInstallNode(); err != nil {
		log.Fatal("Node.js环境配置失败: ", err)
	}

	// 初始化配置
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")

	// 设置默认值
	viper.SetDefault("ssh.host", "**************")
	viper.SetDefault("ssh.user", "root")
	viper.SetDefault("ssh.password", "your-password")
	viper.SetDefault("ssh.port", 22)
	viper.SetDefault("ssh.remote_path", "/opt/1panel/apps/openresty/openresty/www/sites/allreviews.top/index")
	viper.SetDefault("local.dist", "dist")
	viper.SetDefault("local.backup_dir", "backups")
	viper.SetDefault("retry.max_attempts", 5)
	viper.SetDefault("retry.delay", "3s")
	viper.SetDefault("upload.workers", 50)     // 并发上传工作协程数
	viper.SetDefault("upload.buffer_size", 32) // 缓冲区大小(KB)

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// 配置文件不存在时创建默认配置
			if err := viper.SafeWriteConfig(); err != nil {
				log.Fatal("无法创建配置文件: ", err)
			}
		} else {
			log.Fatal("无法读取配置文件: ", err)
		}
	}

	// 配置日志
	log.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	// 创建日志文件
	logFile, err := os.OpenFile("deploy.log", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Fatal("无法创建日志文件: ", err)
	}
	log.SetOutput(logFile)
}

type PostRequest struct {
	Title       string   `json:"title" binding:"required"`
	Excerpt     string   `json:"excerpt" binding:"required"`
	Image       string   `json:"image" binding:"required"`
	Category    string   `json:"category" binding:"required"`
	Tags        []string `json:"tags"`
	Brand       Brand    `json:"brand" binding:"required"`
	Content     string   `json:"content" binding:"required"`
	PublishDate string   `json:"publish_date" binding:"required"`
}

type Brand struct {
	Name        string `json:"name" binding:"required"`
	Logo        string `json:"logo"`
	Website     string `json:"website" binding:"required"`
	Description string `json:"description"`
}

type DeployRequest struct {
	Token string `json:"token" binding:"required"`
}

func createPost(c *gin.Context) {
	var req PostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{
			"code":    400,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	// 生成文件名
	baseFileName := strings.ReplaceAll(req.Title, " ", "-")
	baseFileName = strings.ReplaceAll(baseFileName, "'", "")
	baseFileName = strings.ReplaceAll(baseFileName, "\"", "")
	baseFileName = strings.ReplaceAll(baseFileName, ".", "")
	baseFileName = strings.ReplaceAll(baseFileName, "?", "")
	baseFileName = strings.ReplaceAll(baseFileName, "!", "")
	baseFileName = strings.ReplaceAll(baseFileName, ":", "")
	baseFileName = strings.ReplaceAll(baseFileName, "/", "-")
	baseFileName = strings.ReplaceAll(baseFileName, "\\", "-")

	// 检查文件是否存在，如果存在则添加数字后缀
	postsDir := "src/content/posts"
	fileName := baseFileName + ".md"
	filePath := filepath.Join(postsDir, fileName)

	counter := 1
	for {
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			break
		}
		fileName = fmt.Sprintf("%s_%d.md", baseFileName, counter)
		filePath = filepath.Join(postsDir, fileName)
		counter++
	}

	// 生成文章内容
	tagsStr := strings.Join(req.Tags, "\", \"") // 将标签数组转换为字符串

	content := fmt.Sprintf(`---
title: "%s"
excerpt: "%s"
publishDate: %s
image: "%s"
category: "%s"
tags: ["%s"]
brand: {
  name: "%s", 
  logo: "%s", 
  website: "%s", 
  description: "%s" 
}
---
%s`,
		req.Title,
		req.Excerpt,
		req.PublishDate,
		req.Image,
		req.Category,
		tagsStr,
		req.Brand.Name,
		req.Brand.Logo,
		req.Brand.Website,
		req.Brand.Description,
		req.Content)

	// 创建文件并写入内容
	if err := os.WriteFile(filePath, []byte(content), 0644); err != nil {
		c.JSON(500, gin.H{
			"code":    500,
			"message": "Failed to create post file",
			"data":    nil,
		})
		return
	}

	c.JSON(200, gin.H{
		"code":    200,
		"message": "Post created successfully",
		"data": gin.H{
			"fileName": fileName,
		},
	})
}

func main() {
	// 创建新的cron调度器
	c := cron.New(cron.WithSeconds())

	// 添加定时任务，每天23:35:40执行
	_, err := c.AddFunc("10 35 10,16 * * *", func() {
		if err := deploy(); err != nil {
			log.Printf("部署失败: %v\n", err)
		} else {
			log.Println("部署成功")
		}
	})

	if err != nil {
		log.Fatal("添加定时任务失败:", err)
	}

	// 启动cron调度器
	c.Start()
	log.Println("定时部署任务已启动，将在每天23:35:40执行")

	// 设置Gin路由
	r := gin.Default()
	r.POST("/api/posts", createPost)
	r.POST("/api/deploy", startDeploy)
	// 启动HTTP服务器（非阻塞）
	go func() {
		if err := r.Run(":8182"); err != nil {
			log.Fatal("启动HTTP服务器失败:", err)
		}
	}()

	// 等待中断信号
	sig := make(chan os.Signal, 1)
	signal.Notify(sig, syscall.SIGINT, syscall.SIGTERM)
	<-sig

	// 优雅关闭
	c.Stop()
}

func startDeploy(c *gin.Context) {
	var req DeployRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{
			"code":    400,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}
	if req.Token != "as3g54fdyu3s452a3gyf354dusa53522gbf3k4jb" {
		c.JSON(500, gin.H{
			"code":    500,
			"message": "Failed",
			"data":    nil,
		})
		return
	}
	deploy()
	c.JSON(200, gin.H{
		"code":    200,
		"message": "deploy successfully",
		"data": gin.H{
			"deploy": "",
		},
	})
}

func deploy() error {
	fmt.Println("执行")
	// 1. 执行npm构建
	//if err := runBuild(); err != nil {
	//	return fmt.Errorf("构建失败: %v", err)
	//}

	// 创建临时目录用于事务性上传
	tempRemotePath := filepath.Join(filepath.Dir(viper.GetString("ssh.remote_path")), ".tmp_upload_"+time.Now().Format("20060102_150405"))

	// 在函数结束时清理临时目录
	defer func() {
		// 连接SSH服务器
		sshClient, err := connectSSH()
		if err == nil {
			defer sshClient.Close()
			// 创建SFTP客户端
			sftpClient, err := sftp.NewClient(sshClient)
			if err == nil {
				defer sftpClient.Close()
				// 清理临时目录
				if err := sftpClient.RemoveDirectory(tempRemotePath); err != nil {
					log.WithFields(logrus.Fields{
						"path":  tempRemotePath,
						"error": err,
					}).Warning("清理临时目录失败")
				}
			}
		}
	}()

	// 2. 连接SSH服务器
	fmt.Println("连接SSH服务器")
	sshClient, err := connectSSH()
	if err != nil {
		return fmt.Errorf("SSH连接失败: %v", err)
	}
	defer sshClient.Close()

	// 创建SFTP客户端
	sftpClient, err := sftp.NewClient(sshClient)
	if err != nil {
		return fmt.Errorf("SFTP客户端创建失败: %v", err)
	}
	defer sftpClient.Close()

	fmt.Println("连接SSH服务器 - 结束")
	// 3. 创建备份
	fmt.Println("创建备份 - 开始")
	if err := createBackup(sftpClient); err != nil {
		return fmt.Errorf("备份失败: %v", err)
	}
	fmt.Println("创建备份 - 结束")
	// 4. 上传新文件
	fmt.Println("上传新文件 - 开始")
	if err := uploadFiles(sftpClient, tempRemotePath); err != nil {
		return fmt.Errorf("上传失败: %v", err)
	}
	fmt.Println("上传新文件 - 结束")

	// 如果上传成功，将临时目录中的文件移动到目标目录
	remotePath := viper.GetString("ssh.remote_path")
	backupPath := remotePath + ".bak_" + time.Now().Format("20060102_150405")

	// 1. 将当前目录重命名为备份
	if err := sftpClient.Rename(remotePath, backupPath); err != nil {
		return fmt.Errorf("创建备份失败: %v", err)
	}

	// 2. 将临时目录重命名为目标目录
	if err := sftpClient.Rename(tempRemotePath, remotePath); err != nil {
		// 如果移动失败，尝试恢复备份
		if restoreErr := sftpClient.Rename(backupPath, remotePath); restoreErr != nil {
			log.WithFields(logrus.Fields{
				"error": restoreErr,
			}).Error("恢复备份失败")
			return fmt.Errorf("部署失败且无法恢复: %v, 恢复错误: %v", err, restoreErr)
		}
		return fmt.Errorf("部署失败，已恢复备份: %v", err)
	}

	// 3. 删除备份
	if err := sftpClient.RemoveDirectory(backupPath); err != nil {
		log.WithFields(logrus.Fields{
			"path":  backupPath,
			"error": err,
		}).Warning("删除备份失败")
	}

	return nil
}

func runBuild() error {
	if nodejsInstallPath == "" {
		return fmt.Errorf("Node.js环境未正确安装，请先运行checkAndInstallNode()")
	}

	// 获取Node.js安装目录
	archSuffix := "x64"
	if runtime.GOARCH == "arm64" {
		archSuffix = "arm64"
	}
	nodeBinDir := filepath.Join(nodejsInstallPath, fmt.Sprintf("node-v%s-%s-%s", requiredNodeVersion, runtime.GOOS, archSuffix), "bin")

	// 设置npm路径
	npmPath := filepath.Join(nodeBinDir, "npm")
	if runtime.GOOS == "windows" {
		npmPath += ".cmd"
	}

	// 验证npm可执行文件
	if _, err := os.Stat(npmPath); os.IsNotExist(err) {
		return fmt.Errorf("npm可执行文件不存在: %s，请确保Node.js环境已正确安装", npmPath)
	}

	// 设置环境变量
	cmd := exec.Command(npmPath, "run", "build")
	cmd.Env = append(os.Environ(),
		"PATH="+nodeBinDir+string(os.PathListSeparator)+os.Getenv("PATH"),
	)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd.Run()
}

func connectSSH() (*ssh.Client, error) {
	var client *ssh.Client
	var err error

	maxAttempts := viper.GetInt("retry.max_attempts")
	delay := viper.GetDuration("retry.delay")

	// 配置SSH客户端
	config := &ssh.ClientConfig{
		User: viper.GetString("ssh.user"),
		Auth: []ssh.AuthMethod{
			ssh.Password(viper.GetString("ssh.password")),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         5 * time.Second,
		BannerCallback:  ssh.BannerCallback(func(message string) error { return nil }),
	}

	host := fmt.Sprintf("%s:%d", viper.GetString("ssh.host"), viper.GetInt("ssh.port"))

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		client, err = ssh.Dial("tcp", host, config)
		if err == nil {
			return client, nil
		}

		log.WithFields(logrus.Fields{
			"attempt": attempt,
			"error":   err,
		}).Warning("SSH连接失败，准备重试")

		if attempt < maxAttempts {
			time.Sleep(delay)
		}
	}

	return nil, fmt.Errorf("在%d次尝试后仍无法连接SSH: %v", maxAttempts, err)
}

func createBackup(client *sftp.Client) error {
	// 创建备份目录
	backupPath := filepath.Join(viper.GetString("local.backup_dir"), time.Now().Format("20060102_150405"))
	if err := os.MkdirAll(backupPath, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %v", err)
	}

	// 创建新的SSH连接
	sshClient, err := connectSSH()
	if err != nil {
		return fmt.Errorf("创建SSH连接失败: %v", err)
	}
	defer sshClient.Close()

	// 创建SSH会话
	session, err := sshClient.NewSession()
	if err != nil {
		return fmt.Errorf("创建SSH会话失败: %v", err)
	}
	defer session.Close()

	// 构建tar命令
	remotePath := viper.GetString("ssh.remote_path")
	backupFile := filepath.Join(backupPath, "backup.tar")
	cmd := fmt.Sprintf("cd %s && tar -czf - .", filepath.Dir(remotePath))

	// 执行命令并获取输出
	output, err := session.Output(cmd)
	if err != nil {
		return fmt.Errorf("执行tar命令失败: %v", err)
	}

	// 创建本地备份文件
	f, err := os.Create(backupFile)
	if err != nil {
		return fmt.Errorf("创建备份文件失败: %v", err)
	}
	defer f.Close()

	// 写入备份文件
	if _, err := f.Write(output); err != nil {
		return fmt.Errorf("写入备份文件失败: %v", err)
	}

	log.Printf("备份完成: %s", backupFile)
	return nil
}

func downloadFile(client *sftp.Client, remoteFile, localFile string) error {
	r, err := client.Open(remoteFile)
	if err != nil {
		return err
	}
	defer r.Close()

	f, err := os.Create(localFile)
	if err != nil {
		return err
	}
	defer f.Close()

	_, err = io.Copy(f, r)
	return err
}

type FileInfo struct {
	Path     string
	Info     os.FileInfo
	RelPath  string
	ModTime  time.Time
	Checksum string
}

func calculateChecksum(filePath string) (string, error) {
	f, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer f.Close()

	h := sha256.New()
	if _, err := io.Copy(h, f); err != nil {
		return "", err
	}
	return fmt.Sprintf("%x", h.Sum(nil)), nil
}

func uploadFiles(client *sftp.Client, tempRemotePath string) error {
	// 收集所有文件信息并计算总大小
	var files []FileInfo
	totalBytes = 0
	uploadedBytes = 0

	err := filepath.Walk(viper.GetString("local.dist"), func(localPath string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			relPath, err := filepath.Rel(viper.GetString("local.dist"), localPath)
			if err != nil {
				return err
			}

			checksum, err := calculateChecksum(localPath)
			if err != nil {
				return err
			}

			files = append(files, FileInfo{
				Path:     localPath,
				Info:     info,
				RelPath:  relPath,
				ModTime:  info.ModTime(),
				Checksum: checksum,
			})
			totalBytes += info.Size()
		}
		return nil
	})

	if err != nil {
		return err
	}

	// 创建进度条
	bar := pb.New64(totalBytes)
	bar.Set(pb.Bytes, true)
	bar.Set(pb.Terminal, true)
	bar.Set(pb.Default, false)
	bar.Set(pb.Simple, false)
	bar.Set(pb.Full, false)
	bar.SetTemplate(`{{string . "prefix"}}{{bar . }} {{percent . }} {{speed . }} {{counters . }}`)
	bar.Set("prefix", "上传进度: ")
	bar.Start()
	defer bar.Finish()

	// 创建工作池
	workers := viper.GetInt("upload.workers")
	jobs := make(chan FileInfo, len(files))
	errors := make(chan error, len(files))
	wg := sync.WaitGroup{}

	// 启动工作协程
	for i := 0; i < workers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for file := range jobs {
				if err := uploadFileWithRetry(client, file, bar, tempRemotePath); err != nil {
					errors <- err
				}
			}
		}()
	}

	// 分发任务
	for _, file := range files {
		jobs <- file
	}
	close(jobs)

	// 等待所有工作完成
	wg.Wait()
	close(errors)

	// 检查是否有错误
	for err := range errors {
		if err != nil {
			return err
		}
	}

	return nil
}

func uploadFileWithRetry(client *sftp.Client, file FileInfo, bar *pb.ProgressBar, tempRemotePath string) error {
	maxAttempts := viper.GetInt("retry.max_attempts")
	delay := viper.GetDuration("retry.delay")

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		if err := uploadFileWithProgress(client, file, bar, tempRemotePath); err != nil {
			if attempt == maxAttempts {
				return fmt.Errorf("上传文件失败 %s (尝试 %d/%d): %v", file.Path, attempt, maxAttempts, err)
			}
			log.WithFields(logrus.Fields{
				"file":    file.Path,
				"attempt": attempt,
				"error":   err,
			}).Warning("上传失败，准备重试")
			time.Sleep(delay)
			continue
		}
		return nil
	}
	return nil
}

func uploadFileWithProgress(client *sftp.Client, file FileInfo, bar *pb.ProgressBar, tempRemotePath string) error {
	// 使用临时目录路径
	remotePath := filepath.Join(tempRemotePath, file.RelPath)

	// 确保父目录存在
	if err := client.MkdirAll(filepath.Dir(remotePath)); err != nil {
		return err
	}

	// 打开本地文件
	f, err := os.Open(file.Path)
	if err != nil {
		return err
	}
	defer f.Close()

	// 创建远程文件
	rf, err := client.Create(remotePath)
	if err != nil {
		return err
	}
	defer rf.Close()

	// 从内存池获取缓冲区
	buf := bufPool.Get().([]byte)
	defer bufPool.Put(buf)

	// 使用io.CopyBuffer进行文件传输
	proxyReader := bar.NewProxyReader(f)
	_, err = io.CopyBuffer(rf, proxyReader, buf)
	return err
}
