package errors

import (
	"fmt"
	"net/http"
)

// Error 自定义错误类型
type Error struct {
	Code    int    // HTTP状态码
	Message string // 错误信息
	Err     error  // 原始错误
}

func (e *Error) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Err)
	}
	return e.Message
}

// Unwrap 实现errors.Unwrap接口
func (e *Error) Unwrap() error {
	return e.Err
}

// New 创建新的错误
func New(code int, message string) *Error {
	return &Error{
		Code:    code,
		Message: message,
	}
}

// Wrap 包装已有错误
func Wrap(err error, code int, message string) *Error {
	return &Error{
		Code:    code,
		Message: message,
		Err:     err,
	}
}

// 通用错误定义
var (
	// 参数错误 (400)
	ErrInvalidParameter = New(http.StatusBadRequest, "invalid parameter")
	ErrInvalidID       = New(http.StatusBadRequest, "invalid id")
	ErrInvalidQuery    = New(http.StatusBadRequest, "invalid query")
	ErrInvalidPage     = New(http.StatusBadRequest, "invalid page")
	ErrInvalidSize     = New(http.StatusBadRequest, "invalid size")

	// 认证错误 (401)
	ErrUnauthorized = New(http.StatusUnauthorized, "unauthorized")
	ErrInvalidToken = New(http.StatusUnauthorized, "invalid token")
	ErrTokenExpired = New(http.StatusUnauthorized, "token expired")

	// 权限错误 (403)
	ErrForbidden = New(http.StatusForbidden, "forbidden")
	ErrNoPermission = New(http.StatusForbidden, "no permission")

	// 资源不存在 (404)
	ErrNotFound = New(http.StatusNotFound, "resource not found")

	// 业务错误 (422)
	ErrBusinessValidation = New(http.StatusUnprocessableEntity, "business validation failed")

	// 服务器错误 (500)
	ErrInternalServer = New(http.StatusInternalServerError, "internal server error")
	ErrDatabase      = New(http.StatusInternalServerError, "database error")
	ErrCache        = New(http.StatusInternalServerError, "cache error")
)

// 优惠券相关错误
var (
	ErrCouponNotFound = New(http.StatusNotFound, "coupon not found")
	ErrCouponExpired  = New(http.StatusUnprocessableEntity, "coupon expired")
	ErrCouponInactive = New(http.StatusUnprocessableEntity, "coupon not active")
)

// 商家相关错误
var (
	ErrMerchantNotFound = New(http.StatusNotFound, "merchant not found")
	ErrMerchantInactive = New(http.StatusUnprocessableEntity, "merchant not active")
)

// 用户相关错误
var (
	ErrUserNotFound = New(http.StatusNotFound, "user not found")
	ErrUserInactive = New(http.StatusUnprocessableEntity, "user not active")
)
