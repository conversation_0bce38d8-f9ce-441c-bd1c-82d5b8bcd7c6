version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: cashbackany-mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: cashbackany
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - cashbackany-network
    restart: unless-stopped

  redis:
    image: redis:7.0-alpine
    container_name: cashbackany-redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - cashbackany-network
    restart: unless-stopped

  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: cashbackany-api
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: cashbackany
      DB_USER: ${MYSQL_USER}
      DB_PASSWORD: ${MYSQL_PASSWORD}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
    depends_on:
      - mysql
      - redis
    networks:
      - cashbackany-network
    restart: unless-stopped

  track:
    build:
      context: .
      dockerfile: Dockerfile.track
    container_name: cashbackany-track
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: cashbackany
      DB_USER: ${MYSQL_USER}
      DB_PASSWORD: ${MYSQL_PASSWORD}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    depends_on:
      - mysql
      - redis
    networks:
      - cashbackany-network
    restart: unless-stopped

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: cashbackany-frontend
    environment:
      NEXT_PUBLIC_API_URL: ${API_URL}
      NEXT_PUBLIC_TRACK_URL: ${TRACK_URL}
    networks:
      - cashbackany-network
    restart: unless-stopped

networks:
  cashbackany-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
