package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

type Config struct {
	API          APIConfig          `mapstructure:"api"`
	Track        TrackConfig        `mapstructure:"track"`
	Task         TaskConfig         `mapstructure:"task"`
	Postgres     PostgresConfig     `mapstructure:"postgres"`
	Redis        RedisConfig        `mapstructure:"redis"`
	Logger       LoggerConfig       `mapstructure:"logger"`
	Security     SecurityConfig     `mapstructure:"security"`
	Frontend     FrontendConfig     `mapstructure:"frontend"`
	CloudflareR2 CloudflareR2Config `mapstructure:"cloudflare_r2"`
	ImgBox       ImgBoxConfig       `mapstructure:"imgbox"`
}

type FrontendConfig struct {
	FrontendPath string `mapstructure:"frontend_path"`
}

type APIConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	ReadTimeout  string `mapstructure:"read_timeout"`
	WriteTimeout string `mapstructure:"write_timeout"`
}

type TrackConfig struct {
	Host         string        `mapstructure:"host"`
	Port         int           `mapstructure:"port"`
	ReadTimeout  string        `mapstructure:"read_timeout"`
	WriteTimeout string        `mapstructure:"write_timeout"`
	Service      ServiceConfig `mapstructure:"service"`
}

type TaskConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	ReadTimeout  string `mapstructure:"read_timeout"`
	WriteTimeout string `mapstructure:"write_timeout"`
}

type ServiceConfig struct {
	TrackHost string `mapstructure:"track_host"`
}

type PostgresConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	DBName   string `mapstructure:"dbname"`
	SSLMode  string `mapstructure:"sslmode"`
	TimeZone string `mapstructure:"timezone"`
}

type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

type LoggerConfig struct {
	Level            string   `mapstructure:"level"`
	Encoding         string   `mapstructure:"encoding"`
	OutputPaths      []string `mapstructure:"output_paths"`
	ErrorOutputPaths []string `mapstructure:"error_output_paths"`
}

type SecurityConfig struct {
	JWTSecret   string `mapstructure:"jwt_secret"`
	TokenExpiry string `mapstructure:"token_expiry"`
}

type CloudflareR2Config struct {
	AccountID       string `mapstructure:"account_id"`
	AccessKeyID     string `mapstructure:"access_key_id"`
	SecretAccessKey string `mapstructure:"secret_access_key"`
	BucketName      string `mapstructure:"bucket_name"`
	PublicURL       string `mapstructure:"public_url"`
}

type ImgBoxConfig struct {
	UploadToken string `mapstructure:"upload_token"`
	PublicURL   string `mapstructure:"public_url"`
}

// LoadConfig 加载配置文件
// env 参数指定环境：local, test, live
func LoadConfig(env string) (*Config, error) {
	// 设置配置文件路径
	configPath := filepath.Join("configs", env)
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("config directory not found: %s", configPath)
	}

	// 初始化 viper
	v := viper.New()
	v.SetConfigType("yaml")
	v.AddConfigPath(configPath)

	// 加载 common 配置
	v.SetConfigName("common")
	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("error reading common config: %w", err)
	}

	// 加载 track 配置（先加载 track 配置，防止被 api 配置覆盖）
	v.SetConfigName("track")
	if err := v.MergeInConfig(); err != nil {
		return nil, fmt.Errorf("error reading track config: %w", err)
	}

	// 加载 api 配置
	v.SetConfigName("api")
	if err := v.MergeInConfig(); err != nil {
		return nil, fmt.Errorf("error reading api config: %w", err)
	}

	// 加载 task 配置
	v.SetConfigName("task")
	if err := v.MergeInConfig(); err != nil {
		return nil, fmt.Errorf("error reading task config: %w", err)
	}

	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("error unmarshaling config: %w", err)
	}

	return &config, nil
}
