# 批量创建分类脚本

这个目录包含用于批量创建商品分类的脚本。根据您的需求和环境，可以选择不同的方式执行。

## 脚本文件说明

- `create_categories.go` - Go语言实现的批量创建分类脚本
- `create_categories.sh` - 执行Go脚本的Shell包装器
- `create_categories.sql` - 纯SQL实现的分类创建脚本

## 使用方法

### 方法1: 使用Shell脚本（推荐）

这是最简单的使用方式，只需执行:

```bash
# 设置数据库连接环境变量（可选，默认值为db:5432/postgres/postgres/cashback）
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=postgres
export DB_PASSWORD=your_password
export DB_NAME=cashback

# 执行脚本
./create_categories.sh
```

### 方法2: 直接执行Go脚本

如果您想直接运行Go脚本，需要先修改数据库连接字符串：

```bash
# 编辑create_categories.go文件，修改数据库连接字符串
# 然后执行
go run create_categories.go
```

### 方法3: 执行SQL脚本

如果您更喜欢直接在数据库中执行SQL：

```bash
# 使用psql执行
psql -U postgres -d cashback -f create_categories.sql

# 或者在数据库管理工具中执行SQL文件内容
```

## 分类数据

脚本将创建以下42个分类：

1. Arts & Entertainment
2. Autos & Vehicles
3. Beauty & Fitness
...等等

每个分类都有相应的图标（使用React Icons中的Font Awesome图标名称），适合在前端展示使用。

## 注意事项

- 所有脚本都支持幂等操作，可以重复执行而不会产生重复数据
- 如果分类已存在，将更新名称和图标字段
- 执行脚本需要对数据库有写入权限 