#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

echo -e "${BLUE}=== 批量创建分类脚本 ===${NC}"
echo -e "${BLUE}准备执行批量分类创建...${NC}"

# 检查依赖
if ! command -v go &> /dev/null; then
    echo -e "${RED}错误: 未安装Go语言环境.${NC}"
    exit 1
fi

# 设置工作目录为脚本所在目录
cd "$(dirname "$0")"

# 检查Go脚本是否存在
if [ ! -f "./create_categories.go" ]; then
    echo -e "${RED}错误: 找不到create_categories.go文件${NC}"
    exit 1
fi

# 获取环境变量或使用默认值
DB_HOST="${DB_HOST:-db}"
DB_PORT="${DB_PORT:-5432}"
DB_USER="${DB_USER:-postgres}"
DB_PASSWORD="${DB_PASSWORD:-postgres}"
DB_NAME="${DB_NAME:-cashback}"

# 动态修改数据库连接字符串
sed -i.bak "s|************************************/cashback|postgres://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}|g" create_categories.go
rm -f create_categories.go.bak

echo -e "${BLUE}使用数据库连接: postgres://${DB_USER}:***@${DB_HOST}:${DB_PORT}/${DB_NAME}${NC}"
echo -e "${BLUE}开始执行Go脚本...${NC}"

# 执行Go脚本
go run create_categories.go

# 检查执行结果
if [ $? -eq 0 ]; then
    echo -e "${GREEN}脚本执行成功!${NC}"
else
    echo -e "${RED}脚本执行失败!${NC}"
    exit 1
fi

echo -e "${BLUE}=== 批量创建分类完成 ===${NC}" 