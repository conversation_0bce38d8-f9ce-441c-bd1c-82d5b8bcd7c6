-- 批量创建分类的SQL脚本
-- 可以直接在数据库中执行此脚本

-- 创建临时表存储分类数据
CREATE TEMPORARY TABLE temp_categories (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    icon VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 插入分类数据
INSERT INTO temp_categories (id, name, icon) VALUES
(1, 'Arts & Entertainment', 'FaTheaterMasks'),
(2, 'Autos & Vehicles', 'FaCar'),
(3, 'Beauty & Fitness', 'FaSpa'),
(4, 'Books & Literature', 'FaBook'),
(5, 'Business & Industrial', 'FaBriefcase'),
(6, 'Computers & Electronics', 'FaLaptop'),
(7, 'Finance', 'FaMoneyBillWave'),
(8, 'Food & Drink', 'FaUtensils'),
(9, 'Games', 'FaGamepad'),
(10, 'Health', 'FaHeartbeat'),
(11, 'Hobbies & Leisure', 'FaUmbrellaBeach'),
(12, 'Home & Garden', 'FaHome'),
(13, 'Internet & Telecom', 'FaGlobe'),
(14, 'Jobs & Education', 'FaGraduationCap'),
(15, 'Law & Government', 'FaBalanceScale'),
(16, 'News', 'FaNewspaper'),
(17, 'Online Communities', 'FaUsers'),
(18, 'People & Society', 'FaUserFriends'),
(19, 'Pets & Animals', 'FaPaw'),
(20, 'Real Estate', 'FaBuilding'),
(21, 'Reference', 'FaBookOpen'),
(22, 'Science', 'FaFlask'),
(23, 'Sports', 'FaFootballBall'),
(24, 'Travel & Transportation', 'FaPlane'),
(25, 'World Localities', 'FaMapMarkedAlt'),
(26, 'Antiques & Collectibles', 'FaGem'),
(27, 'Apparel', 'FaTshirt'),
(28, 'Auctions', 'FaGavel'),
(29, 'Classifieds', 'FaListAlt'),
(30, 'Consumer Resources', 'FaShieldAlt'),
(31, 'Discount & Outlet Stores', 'FaPercent'),
(32, 'Entertainment Media', 'FaFilm'),
(33, 'Gifts & Special Event Items', 'FaGift'),
(34, 'Green & Eco-Friendly Shopping', 'FaLeaf'),
(35, 'Luxury Goods', 'FaCrown'),
(36, 'Mass Merchants & Department Stores', 'FaStore'),
(37, 'Photo & Video Services', 'FaCamera'),
(38, 'Shopping Portals', 'FaShoppingCart'),
(39, 'Swap Meets & Outdoor Markets', 'FaExchangeAlt'),
(40, 'Toys', 'FaPuzzlePiece'),
(41, 'Wholesalers & Liquidators', 'FaBoxes'),
(42, 'Other', 'FaEllipsisH');

-- 检查categories表是否存在，如果不存在则创建
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'categories') THEN
        CREATE TABLE categories (
            id BIGINT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            icon VARCHAR(255),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;
END
$$;

-- 将临时表数据插入或更新到正式表
INSERT INTO categories (id, name, icon, created_at, updated_at)
SELECT id, name, icon, created_at, updated_at
FROM temp_categories
ON CONFLICT (id) DO UPDATE
SET name = EXCLUDED.name,
    icon = EXCLUDED.icon,
    updated_at = NOW();

-- 输出结果统计
DO $$
DECLARE
    inserted_count INT;
    total_count INT;
BEGIN
    SELECT COUNT(*) INTO total_count FROM categories;
    SELECT COUNT(*) INTO inserted_count FROM temp_categories;
    
    RAISE NOTICE '分类数据处理完成！总分类数: %, 本次处理: %', total_count, inserted_count;
END
$$; 