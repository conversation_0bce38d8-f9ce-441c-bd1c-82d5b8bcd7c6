package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/jackc/pgx/v5"
)

// 分类映射，包含名称和ID
var CategoryNameMap = map[string]uint64{
	"Arts & Entertainment":               1,
	"Autos & Vehicles":                   2,
	"Beauty & Fitness":                   3,
	"Books & Literature":                 4,
	"Business & Industrial":              5,
	"Computers & Electronics":            6,
	"Finance":                            7,
	"Food & Drink":                       8,
	"Games":                              9,
	"Health":                             10,
	"Hobbies & Leisure":                  11,
	"Home & Garden":                      12,
	"Internet & Telecom":                 13,
	"Jobs & Education":                   14,
	"Law & Government":                   15,
	"News":                               16,
	"Online Communities":                 17,
	"People & Society":                   18,
	"Pets & Animals":                     19,
	"Real Estate":                        20,
	"Reference":                          21,
	"Science":                            22,
	"Sports":                             23,
	"Travel & Transportation":            24,
	"World Localities":                   25,
	"Antiques & Collectibles":            26,
	"Apparel":                            27,
	"Auctions":                           28,
	"Classifieds":                        29,
	"Consumer Resources":                 30,
	"Discount & Outlet Stores":           31,
	"Entertainment Media":                32,
	"Gifts & Special Event Items":        33,
	"Green & Eco-Friendly Shopping":      34,
	"Luxury Goods":                       35,
	"Mass Merchants & Department Stores": 36,
	"Photo & Video Services":             37,
	"Shopping Portals":                   38,
	"Swap Meets & Outdoor Markets":       39,
	"Toys":                               40,
	"Wholesalers & Liquidators":          41,
	"Others":                             42,
}

// 分类图标映射，为每个分类分配一个相关的图标
var CategoryIconMap = map[string]string{
	"Arts & Entertainment":               "FaTheaterMasks",
	"Autos & Vehicles":                   "FaCar",
	"Beauty & Fitness":                   "FaSpa",
	"Books & Literature":                 "FaBook",
	"Business & Industrial":              "FaBriefcase",
	"Computers & Electronics":            "FaLaptop",
	"Finance":                            "FaMoneyBillWave",
	"Food & Drink":                       "FaUtensils",
	"Games":                              "FaGamepad",
	"Health":                             "FaHeartbeat",
	"Hobbies & Leisure":                  "FaUmbrellaBeach",
	"Home & Garden":                      "FaHome",
	"Internet & Telecom":                 "FaGlobe",
	"Jobs & Education":                   "FaGraduationCap",
	"Law & Government":                   "FaBalanceScale",
	"News":                               "FaNewspaper",
	"Online Communities":                 "FaUsers",
	"People & Society":                   "FaUserFriends",
	"Pets & Animals":                     "FaPaw",
	"Real Estate":                        "FaBuilding",
	"Reference":                          "FaBookOpen",
	"Science":                            "FaFlask",
	"Sports":                             "FaFootballBall",
	"Travel & Transportation":            "FaPlane",
	"World Localities":                   "FaMapMarkedAlt",
	"Antiques & Collectibles":            "FaGem",
	"Apparel":                            "FaTshirt",
	"Auctions":                           "FaGavel",
	"Classifieds":                        "FaListAlt",
	"Consumer Resources":                 "FaShieldAlt",
	"Discount & Outlet Stores":           "FaPercent",
	"Entertainment Media":                "FaFilm",
	"Gifts & Special Event Items":        "FaGift",
	"Green & Eco-Friendly Shopping":      "FaLeaf",
	"Luxury Goods":                       "FaCrown",
	"Mass Merchants & Department Stores": "FaStore",
	"Photo & Video Services":             "FaCamera",
	"Shopping Portals":                   "FaShoppingCart",
	"Swap Meets & Outdoor Markets":       "FaExchangeAlt",
	"Toys":                               "FaPuzzlePiece",
	"Wholesalers & Liquidators":          "FaBoxes",
	"Others":                             "FaEllipsisH",
}

func main() {
	// 数据库连接信息 - 请根据实际环境修改
	connString := "***************************************************************************************/bonusearned"

	// 连接数据库
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	conn, err := pgx.Connect(ctx, connString)
	if err != nil {
		log.Fatalf("无法连接到数据库: %v", err)
	}
	defer conn.Close(ctx)

	// 开始事务
	tx, err := conn.Begin(ctx)
	if err != nil {
		log.Fatalf("无法开始事务: %v", err)
	}

	// 准备批量插入语句
	sql := `
		INSERT INTO categories (id, name, icon, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $4)
		ON CONFLICT (id) DO UPDATE 
		SET name = EXCLUDED.name, 
		    icon = EXCLUDED.icon,
		    updated_at = EXCLUDED.updated_at
	`

	// 获取当前时间
	now := time.Now()

	// 执行批量插入
	var inserted, updated int

	for name, id := range CategoryNameMap {
		icon := CategoryIconMap[name]

		// 检查分类是否已存在
		var exists bool
		err := conn.QueryRow(ctx, "SELECT EXISTS(SELECT 1 FROM categories WHERE id = $1)", id).Scan(&exists)
		if err != nil {
			tx.Rollback(ctx)
			log.Fatalf("检查分类是否存在时出错: %v", err)
		}

		// 执行插入或更新
		_, err = tx.Exec(ctx, sql, id, name, icon, now)
		if err != nil {
			tx.Rollback(ctx)
			log.Fatalf("插入分类时出错: %v", err)
		}

		// 统计插入和更新的数量
		if exists {
			updated++
			fmt.Printf("更新分类: %s (ID: %d)\n", name, id)
		} else {
			inserted++
			fmt.Printf("新增分类: %s (ID: %d)\n", name, id)
		}
	}

	// 提交事务
	err = tx.Commit(ctx)
	if err != nil {
		log.Fatalf("无法提交事务: %v", err)
	}

	fmt.Printf("\n批量处理完成！新增: %d, 更新: %d\n", inserted, updated)
}
