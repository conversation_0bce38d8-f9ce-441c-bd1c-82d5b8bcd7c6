#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

# Base URL
BASE_URL="http://localhost:8080/api/v1"

# Test user credentials
USER_EMAIL="<EMAIL>"
USER_PASSWORD="test123"

# Global variables
USER_TOKEN=""
WITHDRAWAL_ID=""

# Function to print colored output
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}[SUCCESS]${NC} $2"
    else
        echo -e "${RED}[FAILED]${NC} $2"
        exit 1
    fi
}

# Function to check if response contains error
check_error() {
    local response=$1
    local error=$(echo $response | jq -r '.error // empty')
    if [ ! -z "$error" ]; then
        echo -e "${RED}Error: $error${NC}"
        return 1
    fi
    return 0
}

# Login as user to get token
echo "Logging in as test user..."
response=$(curl -s -X POST "${BASE_URL}/users/login" \
    -H "Content-Type: application/json" \
    -d "{\"email\":\"${USER_EMAIL}\",\"password\":\"${USER_PASSWORD}\"}")
echo "Login response: $response"  # Debug output
check_error "$response" || exit 1
USER_TOKEN=$(echo $response | jq -r '.token')
[ -z "$USER_TOKEN" ] && { echo "Failed to get token"; exit 1; }
echo "Got token: ${USER_TOKEN:0:20}..."  # Show first 20 chars of token
print_result $? "User login"

# Test 1: Get initial balance
echo "Getting initial balance..."
response=$(curl -s -X GET "${BASE_URL}/users/me/balance" \
    -H "Authorization: Bearer ${USER_TOKEN}")
echo "Initial balance response: $response"  # Debug output
check_error "$response" || exit 1
initial_balance=$(echo $response | jq '.balance.available_amount')
print_result $? "Get initial balance"
echo "Initial balance: $initial_balance"

# Test 2: Try to withdraw more than balance
echo "Testing withdrawal amount validation (more than balance)..."
response=$(curl -s -X POST "${BASE_URL}/withdrawals" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${USER_TOKEN}" \
    -d '{
        "amount": 99999,
        "method": "bank",
        "payment_account": "**********",
        "bank_name": "Test Bank",
        "bank_branch": "Test Branch"
    }')
echo "Withdrawal amount validation response: $response"  # Debug output
if echo "$response" | jq -r '.error' | grep -q "Insufficient balance"; then
    print_result 0 "Withdrawal amount validation"
else
    print_result 1 "Withdrawal amount validation"
fi

# Test 3: Try to withdraw negative amount
echo "Testing negative amount validation..."
response=$(curl -s -X POST "${BASE_URL}/withdrawals" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${USER_TOKEN}" \
    -d '{
        "amount": -50,
        "method": "bank",
        "payment_account": "**********",
        "bank_name": "Test Bank",
        "bank_branch": "Test Branch"
    }')
echo "Negative amount validation response: $response"  # Debug output
if echo "$response" | jq -r '.error' | grep -q "Key: 'CreateWithdrawalRequest.Amount' Error:Field validation for 'Amount' failed on the 'gt' tag"; then
    print_result 0 "Negative amount validation"
else
    print_result 1 "Negative amount validation"
fi

# Test 4: Create valid withdrawal request
echo "Creating valid withdrawal request..."
valid_amount=10
response=$(curl -s -X POST "${BASE_URL}/withdrawals" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${USER_TOKEN}" \
    -d "{
        \"amount\": $valid_amount,
        \"method\": \"bank\",
        \"payment_account\": \"**********\",
        \"bank_name\": \"Test Bank\",
        \"bank_branch\": \"Test Branch\"
    }")
echo "Create withdrawal response: $response"  # Debug output
check_error "$response" || exit 1
WITHDRAWAL_ID=$(echo $response | jq -r '.id')
[ -z "$WITHDRAWAL_ID" ] && { echo "Failed to get withdrawal ID"; exit 1; }
print_result $? "Create withdrawal"

# Test 5: Verify balance is reduced
echo "Verifying balance reduction..."
response=$(curl -s -X GET "${BASE_URL}/users/me/balance" \
    -H "Authorization: Bearer ${USER_TOKEN}")
echo "Balance reduction response: $response"  # Debug output
check_error "$response" || exit 1
new_balance=$(echo $response | jq '.balance.available_amount')
expected_balance=$(echo "$initial_balance - $valid_amount" | bc)
if [ $(echo "$new_balance == $expected_balance" | bc) -eq 1 ]; then
    print_result 0 "Balance reduction verification"
else
    echo "Expected balance: $expected_balance, Actual balance: $new_balance"
    print_result 1 "Balance reduction verification"
fi

# Test 6: Get withdrawal details
echo "Getting withdrawal details..."
response=$(curl -s -X GET "${BASE_URL}/withdrawals/${WITHDRAWAL_ID}" \
    -H "Authorization: Bearer ${USER_TOKEN}")
echo "Get withdrawal details response: $response"  # Debug output
check_error "$response" || exit 1
status=$(echo $response | jq -r '.status')
if [ "$status" = "1" ]; then
    print_result 0 "Get withdrawal details"
else
    print_result 1 "Get withdrawal details"
fi

# Test 7: List all withdrawals
echo "Listing all withdrawals..."
response=$(curl -s -X GET "${BASE_URL}/withdrawals" \
    -H "Authorization: Bearer ${USER_TOKEN}")
echo "List withdrawals response: $response"  # Debug output
check_error "$response" || exit 1
print_result $? "List withdrawals"

echo -e "\n${GREEN}All withdrawal tests completed successfully!${NC}"
