#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Base URL
#BASE_URL="http://localhost:8080/api/v1"
BASE_URL="https://api.cashbackany.com/api/v1"
# Store token
TOKEN_FILE=".token"

# Test counter
TOTAL_TESTS=0
PASSED_TESTS=0

# Helper function to print test results
print_result() {
    local test_name=$1
    local status=$2
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if [ $status -eq 0 ]; then
        echo -e "${GREEN}✓ $test_name passed${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ $test_name failed${NC}"
    fi
}

# Helper function to check response
check_response() {
    local response=$1
    local expected_status=$2
    local actual_status=$(echo "$response" | tail -n1)
    if [ "$actual_status" = "$expected_status" ]; then
        return 0
    else
        echo "Expected status $expected_status but got $actual_status"
        echo "Response: $(echo "$response" | head -n1)"
        return 1
    fi
}

# Helper function to get admin token
get_admin_token() {
    response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/users/login" \
        -H "Content-Type: application/json" \
        -d '{
            "email": "<EMAIL>",
            "password": "admin123"
        }')
    TOKEN=$(echo "$response" | grep -o '"token":"[^"]*' | cut -d'"' -f4)
    echo $TOKEN > $TOKEN_FILE
}

echo "Starting Merchant API tests..."

# First, login as admin to get token
get_admin_token

# Test 1: Create a new category
echo "Testing create category..."
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/categories" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $(cat $TOKEN_FILE)" \
    -d '{
        "name": "Test Category3"
    }')
check_response "$response" "201"
print_result "Create Category" $?

# Extract category ID
CATEGORY_ID=$(echo "$response" | grep -o '"id":[0-9]*' | cut -d':' -f2)

# Test 2: Create a new merchant
echo "Testing create merchant..."
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/merchants" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $(cat $TOKEN_FILE)" \
    -d '{"name":"Test Merchant","unique_name":"test-merchant","merchant_code":"test123","website":"https://test-merchant.com","track_url":"https://test-merchant.com/track","alliance_link":"https://test-merchant.com/alliance","need_click_id":false,"commission_type":1,"commission_value":10.00,"commission_min":0.00,"commission_max":100.00,"description":"Test merchant description","cashback_rate":80,"commission_rate":100,"platform_type":"cj","platform_merchant_id":"test123","category_id":'$CATEGORY_ID'}')
check_response "$response" "201"
print_result "Create Merchant" $?

# Extract merchant ID
MERCHANT_ID=$(echo "$response" | grep -o '"id":[0-9]*' | cut -d':' -f2)

# Test 3: Get merchant list
echo "Testing get merchant list..."
response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/merchants?page=1&page_size=10")
check_response "$response" "200"
print_result "Get Merchant List" $?

# Test 4: Get merchant detail
echo "Testing get merchant detail..."
response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/merchants/$MERCHANT_ID")
check_response "$response" "200"
print_result "Get Merchant Detail" $?

# Test 5: Update merchant
echo "Testing update merchant..."
response=$(curl -s -w "\n%{http_code}" -X PUT "$BASE_URL/merchants/$MERCHANT_ID" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $(cat $TOKEN_FILE)" \
    -d '{
        "name": "Updated Test Merchant",
        "description": "Updated test merchant description",
        "cashback_rate": 85
    }')
check_response "$response" "200"
print_result "Update Merchant" $?

# Test 6: Get merchant categories
echo "Testing get merchant categories..."
response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/merchants/$MERCHANT_ID/categories")
check_response "$response" "200"
print_result "Get Merchant Categories" $?

# Test 7: Update merchant categories
echo "Testing update merchant categories..."
response=$(curl -s -w "\n%{http_code}" -X PUT "$BASE_URL/merchants/$MERCHANT_ID/categories" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $(cat $TOKEN_FILE)" \
    -d '{
        "category_ids": ['$CATEGORY_ID']
    }')
check_response "$response" "200"
print_result "Update Merchant Categories" $?

# Test 8: Get merchant by invalid ID
echo "Testing get merchant with invalid ID..."
response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/merchants/999999")
check_response "$response" "404"
print_result "Get Invalid Merchant" $?

# Test 9: Update merchant without token
echo "Testing update merchant without token..."
response=$(curl -s -w "\n%{http_code}" -X PUT "$BASE_URL/merchants/$MERCHANT_ID" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Updated Test Merchant"
    }')
check_response "$response" "401"
print_result "Update Merchant Without Token" $?

# Test 10: Create a merchant with duplicate unique name
echo "Testing create merchant with duplicate unique name..."
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/merchants" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $(cat $TOKEN_FILE)" \
    -d '{"name":"Test Merchant 2","unique_name":"test-merchant","merchant_code":"test456","website":"https://test-merchant2.com","track_url":"https://test-merchant2.com/track","alliance_link":"https://test-merchant2.com/alliance","need_click_id":false,"commission_type":1,"commission_value":10.00,"commission_min":0.00,"commission_max":100.00,"description":"Test merchant 2 description","cashback_rate":70,"commission_rate":90,"platform_type":"cj","platform_merchant_id":"test456","category_id":'$CATEGORY_ID'}')
check_response "$response" "409"
print_result "Create Duplicate Merchant" $?

# Print test summary
echo "----------------------------------------"
echo "Test Summary:"
echo "Total tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $((TOTAL_TESTS - PASSED_TESTS))"
