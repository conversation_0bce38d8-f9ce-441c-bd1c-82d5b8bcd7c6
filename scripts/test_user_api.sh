#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Base URL
#BASE_URL="http://localhost:8080/api/v1"
BASE_URL="https://api.cashbackany.com/api/v1"

# Store token
TOKEN_FILE=".token"

# Test counter
TOTAL_TESTS=0
PASSED_TESTS=0

# Helper function to print test results
print_result() {
    local test_name=$1
    local status=$2
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if [ $status -eq 0 ]; then
        echo -e "${GREEN}✓ $test_name passed${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ $test_name failed${NC}"
    fi
}

# Helper function to check response
check_response() {
    local response=$1
    local expected_status=$2
    local actual_status=$(echo "$response" | tail -n1)
    if [ "$actual_status" = "$expected_status" ]; then
        return 0
    else
        echo "Expected status $expected_status but got $actual_status"
        echo "Response: $(echo "$response" | head -n1)"
        return 1
    fi
}

echo "Starting User API tests..."

# Test 1: Register a new user
echo "Testing user registration..."
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/users/register" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "test123",
        "nickname": "Test User 2"
    }')
check_response "$response" "201"
print_result "User Registration" $?

# Test 2: Register with existing email
echo "Testing duplicate email registration..."
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/users/register" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "test123",
        "nickname": "Test User 2"
    }')
check_response "$response" "409"
print_result "Duplicate Email Registration" $?

# Test 3: Login with correct credentials
echo "Testing user login..."
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/users/login" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "test123"
    }')
echo "Login response: $response"
check_response "$response" "200"
print_result "User Login" $?

# Store token for subsequent tests
TOKEN=$(echo "$response" | grep -o '"token":"[^"]*' | cut -d'"' -f4)
echo $TOKEN > $TOKEN_FILE

# Test 4: Login with incorrect password
echo "Testing login with wrong password..."
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/users/login" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "wrongpassword"
    }')
check_response "$response" "401"
print_result "Login with Wrong Password" $?

# Test 5: Get user profile
echo "Testing get user profile..."
response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/users/me" \
    -H "Authorization: Bearer $(cat $TOKEN_FILE)")
check_response "$response" "200"
print_result "Get User Profile" $?

# Test 6: Get user profile without token
echo "Testing get user profile without token..."
response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/users/me")
check_response "$response" "401"
print_result "Get Profile Without Token" $?

# Test 7: Update user profile
echo "Testing update user profile..."
response=$(curl -s -w "\n%{http_code}" -X PUT "$BASE_URL/users/me" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $(cat $TOKEN_FILE)" \
    -d '{
        "payment_info": {
            "paypal_email": "<EMAIL>",
            "bank_account": "**********",
            "bank_name": "Test Bank",
            "bank_branch": "Test Branch"
        }
    }')
check_response "$response" "200"
print_result "Update User Profile" $?

# Test 8: Get user balance
echo "Testing get user balance..."
response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/users/me/balance" \
    -H "Authorization: Bearer $(cat $TOKEN_FILE)")
check_response "$response" "200"
print_result "Get User Balance" $?

# Print test summary
echo "----------------------------------------"
echo "Test Summary:"
echo "Total tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $((TOTAL_TESTS - PASSED_TESTS))"
