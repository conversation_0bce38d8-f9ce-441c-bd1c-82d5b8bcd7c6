#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

# Base URL
BASE_URL="http://localhost:8080/api/v1"

# Test user credentials
USER_EMAIL="<EMAIL>"
USER_PASSWORD="test123"

# Global variables to store tokens and IDs
USER_TOKEN=""
ORDER_ID=""
WITHDRAWAL_ID=""

# Function to print colored output
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}[SUCCESS]${NC} $2"
    else
        echo -e "${RED}[FAILED]${NC} $2"
        exit 1
    fi
}

# Function to check if response contains error
check_error() {
    local response=$1
    local error=$(echo $response | jq -r '.error // empty')
    if [ ! -z "$error" ]; then
        echo -e "${RED}Error: $error${NC}"
        return 1
    fi
    return 0
}

# Login as user to get token
echo "Logging in as test user..."
response=$(curl -s -X POST "${BASE_URL}/users/login" \
    -H "Content-Type: application/json" \
    -d "{\"email\":\"${USER_EMAIL}\",\"password\":\"${USER_PASSWORD}\"}")
check_error "$response" || exit 1
USER_TOKEN=$(echo $response | jq -r '.token')
[ -z "$USER_TOKEN" ] && { echo "Failed to get token"; exit 1; }
print_result $? "User login"

# Test 1: Create a new order
echo "Creating new order..."
response=$(curl -s -X POST "${BASE_URL}/orders" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${USER_TOKEN}" \
    -d '{
        "merchant_id": 1,
        "amount": 100.00,
        "order_number": "TEST123456",
        "description": "Test order"
    }')
check_error "$response" || exit 1
ORDER_ID=$(echo $response | jq -r '.data.id')
[ -z "$ORDER_ID" ] && { echo "Failed to get order ID"; exit 1; }
print_result $? "Create order"

# Test 2: Get order details
echo "Getting order details..."
response=$(curl -s -X GET "${BASE_URL}/orders/${ORDER_ID}" \
    -H "Authorization: Bearer ${USER_TOKEN}")
check_error "$response" || exit 1
print_result $? "Get order details"

# Test 3: List all orders
echo "Listing all orders..."
response=$(curl -s -X GET "${BASE_URL}/orders" \
    -H "Authorization: Bearer ${USER_TOKEN}")
check_error "$response" || exit 1
print_result $? "List orders"

# Test 4: Get user balance
echo "Getting user balance..."
response=$(curl -s -X GET "${BASE_URL}/users/me/balance" \
    -H "Authorization: Bearer ${USER_TOKEN}")
check_error "$response" || exit 1
print_result $? "Get user balance"

# Test 5: Create withdrawal request
echo "Creating withdrawal request..."
response=$(curl -s -X POST "${BASE_URL}/withdrawals" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${USER_TOKEN}" \
    -d '{
        "amount": 50.00,
        "payment_info": {
            "payment_method": "bank_transfer",
            "bank_name": "Test Bank",
            "account_number": "**********",
            "account_name": "Test User"
        }
    }')
check_error "$response" || exit 1
WITHDRAWAL_ID=$(echo $response | jq -r '.data.id')
[ -z "$WITHDRAWAL_ID" ] && { echo "Failed to get withdrawal ID"; exit 1; }
print_result $? "Create withdrawal"

# Test 6: Get withdrawal details
echo "Getting withdrawal details..."
response=$(curl -s -X GET "${BASE_URL}/withdrawals/${WITHDRAWAL_ID}" \
    -H "Authorization: Bearer ${USER_TOKEN}")
check_error "$response" || exit 1
print_result $? "Get withdrawal details"

# Test 7: List all withdrawals
echo "Listing all withdrawals..."
response=$(curl -s -X GET "${BASE_URL}/withdrawals" \
    -H "Authorization: Bearer ${USER_TOKEN}")
check_error "$response" || exit 1
print_result $? "List withdrawals"

# Test 8: Check balance after withdrawal
echo "Checking final balance..."
response=$(curl -s -X GET "${BASE_URL}/users/me/balance" \
    -H "Authorization: Bearer ${USER_TOKEN}")
check_error "$response" || exit 1
print_result $? "Get final balance"

echo -e "\n${GREEN}All tests completed successfully!${NC}"
