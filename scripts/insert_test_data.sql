-- 连接到数据库
\c cashbackany;

-- 插入用户数据
INSERT INTO users (email, password, user_code, nickname, status, created_at, updated_at)
VALUES
    ('<EMAIL>', '$2a$10$********************12', 'UC001', 'User One', 1, NOW(), NOW()),
    ('<EMAIL>', '$2a$10$********************12', 'UC002', 'User Two', 1, NOW(), NOW()),
    ('<EMAIL>', '$2a$10$********************12', 'UC003', 'User Three', 1, NOW(), NOW()),
    ('<EMAIL>', '$2a$10$********************12', 'UC004', 'User Four', 1, NOW(), NOW()),
    ('<EMAIL>', '$2a$10$********************12', 'UC005', 'User Five', 1, NOW(), NOW()),
    ('<EMAIL>', '$2a$10$********************12', 'UC006', 'User Six', 1, NOW(), NOW()),
    ('<EMAIL>', '$2a$10$********************12', 'UC007', 'User Seven', 1, NOW(), NOW()),
    ('<EMAIL>', '$2a$10$********************12', 'UC008', 'User Eight', 1, NOW(), NOW()),
    ('<EMAIL>', '$2a$10$********************12', 'UC009', 'User Nine', 1, NOW(), NOW()),
    ('<EMAIL>', '$2a$10$********************12', 'UC010', 'User Ten', 1, NOW(), NOW());

-- 插入用户余额数据
INSERT INTO user_balances (user_id, total_amount, pending_amount, available_amount, withdrawn_amount, created_at, updated_at)
SELECT 
    id as user_id,
    ROUND(CAST(random() * 1000 AS numeric), 2) as total_amount,
    ROUND(CAST(random() * 200 AS numeric), 2) as pending_amount,
    ROUND(CAST(random() * 500 AS numeric), 2) as available_amount,
    ROUND(CAST(random() * 300 AS numeric), 2) as withdrawn_amount,
    NOW() as created_at,
    NOW() as updated_at
FROM users;

-- 插入商家数据
INSERT INTO merchants (
    name, unique_name, merchant_code, logo, website, track_url, affiliate_link,
    need_click_id, commission_type, commission_value, commission_min, commission_max,
    custom_params, param_mappings, description, status, display_status,
    cashback_rate, commission_rate, platform_type, platform_merchant_id,
    featured, regions, created_at, updated_at
)
VALUES
    ('Amazon', 'amazon', 'MC001', 'https://logo.clearbit.com/amazon.com', 'https://amazon.com', 'https://track.amazon.com?u={click_id}', 'https://amazon.com/affiliate', 
    true, 1, 5.00, 0.00, 100.00, 'u={click_id}', '{"click_id":"u"}', 'Amazon description', 1, 1, 80.00, 100.00, 'cj', 'CJ001', true, '["US","UK"]', NOW(), NOW()),
    
    ('eBay', 'ebay', 'MC002', 'https://logo.clearbit.com/ebay.com', 'https://ebay.com', 'https://track.ebay.com?u={click_id}', 'https://ebay.com/affiliate',
    true, 1, 4.00, 0.00, 50.00, 'u={click_id}', '{"click_id":"u"}', 'eBay description', 1, 1, 75.00, 100.00, 'rakuten', 'RAK001', true, '["US","CA"]', NOW(), NOW()),
    
    ('Walmart', 'walmart', 'MC003', 'https://logo.clearbit.com/walmart.com', 'https://walmart.com', 'https://track.walmart.com?u={click_id}', 'https://walmart.com/affiliate',
    true, 2, 3.50, 0.00, 75.00, 'u={click_id}', '{"click_id":"u"}', 'Walmart description', 1, 1, 70.00, 100.00, 'cj', 'CJ002', false, '["US"]', NOW(), NOW()),
    
    ('Target', 'target', 'MC004', 'https://logo.clearbit.com/target.com', 'https://target.com', 'https://track.target.com?u={click_id}', 'https://target.com/affiliate',
    true, 1, 4.50, 0.00, 80.00, 'u={click_id}', '{"click_id":"u"}', 'Target description', 1, 1, 85.00, 100.00, 'rakuten', 'RAK002', true, '["US"]', NOW(), NOW()),
    
    ('Best Buy', 'bestbuy', 'MC005', 'https://logo.clearbit.com/bestbuy.com', 'https://bestbuy.com', 'https://track.bestbuy.com?u={click_id}', 'https://bestbuy.com/affiliate',
    true, 2, 3.00, 0.00, 60.00, 'u={click_id}', '{"click_id":"u"}', 'Best Buy description', 1, 1, 72.00, 100.00, 'cj', 'CJ003', false, '["US","CA"]', NOW(), NOW()),
    
    ('Nike', 'nike', 'MC006', 'https://logo.clearbit.com/nike.com', 'https://nike.com', 'https://track.nike.com?u={click_id}', 'https://nike.com/affiliate',
    true, 1, 6.00, 0.00, 120.00, 'u={click_id}', '{"click_id":"u"}', 'Nike description', 1, 1, 88.00, 100.00, 'rakuten', 'RAK003', true, '["US","UK","CA"]', NOW(), NOW()),
    
    ('Adidas', 'adidas', 'MC007', 'https://logo.clearbit.com/adidas.com', 'https://adidas.com', 'https://track.adidas.com?u={click_id}', 'https://adidas.com/affiliate',
    true, 1, 5.50, 0.00, 100.00, 'u={click_id}', '{"click_id":"u"}', 'Adidas description', 1, 1, 82.00, 100.00, 'cj', 'CJ004', true, '["US","UK","CA"]', NOW(), NOW()),
    
    ('Apple', 'apple', 'MC008', 'https://logo.clearbit.com/apple.com', 'https://apple.com', 'https://track.apple.com?u={click_id}', 'https://apple.com/affiliate',
    true, 2, 4.00, 0.00, 200.00, 'u={click_id}', '{"click_id":"u"}', 'Apple description', 1, 1, 70.00, 100.00, 'rakuten', 'RAK004', true, '["US","UK","CA"]', NOW(), NOW()),
    
    ('Microsoft', 'microsoft', 'MC009', 'https://logo.clearbit.com/microsoft.com', 'https://microsoft.com', 'https://track.microsoft.com?u={click_id}', 'https://microsoft.com/affiliate',
    true, 1, 5.00, 0.00, 150.00, 'u={click_id}', '{"click_id":"u"}', 'Microsoft description', 1, 1, 75.00, 100.00, 'cj', 'CJ005', false, '["US","UK"]', NOW(), NOW()),
    
    ('Samsung', 'samsung', 'MC010', 'https://logo.clearbit.com/samsung.com', 'https://samsung.com', 'https://track.samsung.com?u={click_id}', 'https://samsung.com/affiliate',
    true, 1, 4.50, 0.00, 180.00, 'u={click_id}', '{"click_id":"u"}', 'Samsung description', 1, 1, 78.00, 100.00, 'rakuten', 'RAK005', true, '["US","UK","CA"]', NOW(), NOW());

-- 插入点击记录
INSERT INTO clicks (user_id, merchant_id, user_code, merchant_code, click_id, ip_address, user_agent, created_at, updated_at)
SELECT 
    u.id as user_id,
    m.id as merchant_id,
    u.user_code,
    m.merchant_code,
    'CLK' || TO_CHAR(NOW(), 'YYYYMMDDHH24MISS') || LPAD(FLOOR(random() * 1000)::text, 3, '0') as click_id,
    '192.168.' || FLOOR(random() * 255)::text || '.' || FLOOR(random() * 255)::text as ip_address,
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36' as user_agent,
    NOW() - (random() * interval '30 days') as created_at,
    NOW() - (random() * interval '30 days') as updated_at
FROM 
    users u
    CROSS JOIN merchants m
LIMIT 100;

-- 插入订单数据
INSERT INTO orders (
    user_id, user_code, merchant_id, merchant_code, platform_order_id,
    order_amount, commission_amount, cashback_amount, settlement_amount,
    order_date, confirmation_date, status, platform_status, created_at, updated_at
)
SELECT 
    u.id as user_id,
    u.user_code,
    m.id as merchant_id,
    m.merchant_code,
    'ORD' || TO_CHAR(NOW(), 'YYYYMMDDHH24MISS') || LPAD(FLOOR(random() * 1000)::text, 3, '0') as platform_order_id,
    ROUND(CAST(random() * 1000 AS numeric), 2) as order_amount,
    ROUND(CAST(random() * 50 AS numeric), 2) as commission_amount,
    ROUND(CAST(random() * 40 AS numeric), 2) as cashback_amount,
    ROUND(CAST(random() * 45 AS numeric), 2) as settlement_amount,
    NOW() - (random() * interval '60 days') as order_date,
    CASE 
        WHEN random() > 0.3 THEN NOW() - (random() * interval '30 days')
        ELSE NULL 
    END as confirmation_date,
    CASE 
        WHEN random() > 0.7 THEN 2  -- Confirmed
        WHEN random() > 0.4 THEN 1  -- Pending
        ELSE 3                      -- Cancelled
    END as status,
    CASE 
        WHEN random() > 0.7 THEN 'confirmed'
        WHEN random() > 0.4 THEN 'pending'
        ELSE 'cancelled'
    END as platform_status,
    NOW() - (random() * interval '60 days') as created_at,
    NOW() as updated_at
FROM 
    users u
    CROSS JOIN merchants m
WHERE 
    random() < 0.3
LIMIT 100;

-- 插入提现记录
INSERT INTO withdrawals (
    user_id, amount, status, payment_method, payment_account,
    transaction_id, processed_at, created_at, updated_at
)
SELECT 
    u.id as user_id,
    ROUND(CAST(random() * 500 AS numeric), 2) as amount,
    CASE 
        WHEN random() > 0.7 THEN 2  -- Completed
        WHEN random() > 0.4 THEN 1  -- Pending
        ELSE 3                      -- Failed
    END as status,
    CASE 
        WHEN random() > 0.5 THEN 'paypal'
        ELSE 'bank_transfer'
    END as payment_method,
    CASE 
        WHEN random() > 0.5 THEN 'paypal_' || u.email
        ELSE '**********'
    END as payment_account,
    'TXN' || TO_CHAR(NOW(), 'YYYYMMDDHH24MISS') || LPAD(FLOOR(random() * 1000)::text, 3, '0') as transaction_id,
    CASE 
        WHEN random() > 0.3 THEN NOW() - (random() * interval '30 days')
        ELSE NULL 
    END as processed_at,
    NOW() - (random() * interval '60 days') as created_at,
    NOW() as updated_at
FROM 
    users u
WHERE 
    random() < 0.5
LIMIT 50;
