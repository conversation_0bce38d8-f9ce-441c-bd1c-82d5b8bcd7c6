#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Base URL
BASE_URL="http://localhost:8080/api/v1"

# Store token
TOKEN_FILE=".token"

# Test counter
TOTAL_TESTS=0
PASSED_TESTS=0

# Helper function to print test results
print_result() {
    local test_name=$1
    local status=$2
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if [ $status -eq 0 ]; then
        echo -e "${GREEN}✓ $test_name passed${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ $test_name failed${NC}"
    fi
}

# Helper function to check response
check_response() {
    local response=$1
    local expected_status=$2
    local actual_status=$(echo "$response" | tail -n1)
    if [ "$actual_status" = "$expected_status" ]; then
        return 0
    else
        echo "Expected status $expected_status but got $actual_status"
        echo "Response: $(echo "$response" | head -n1)"
        return 1
    fi
}

echo "Starting API tests..."

# Test 1: Register new user
echo "Testing user registration..."
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/users/register" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "password123",
        "nickname": "Test User"
    }')
check_response "$response" "201"
print_result "User Registration" $?

# Extract token from registration response
TOKEN=$(echo "$response" | grep -o '"token":"[^"]*' | cut -d'"' -f4)
echo $TOKEN > $TOKEN_FILE

# Test 2: Register with existing email
echo "Testing duplicate registration..."
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/users/register" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "password123",
        "nickname": "Test User"
    }')
check_response "$response" "409"
print_result "Duplicate Registration" $?

# Test 3: Register with invalid email
echo "Testing invalid email registration..."
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/users/register" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "invalid-email",
        "password": "password123",
        "nickname": "Test User"
    }')
check_response "$response" "400"
print_result "Invalid Email Registration" $?

# Test 4: Login
echo "Testing user login..."
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/auth/login" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "password123"
    }')
check_response "$response" "200"
print_result "User Login" $?

# Test 5: Get user profile
echo "Testing get user profile..."
response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/users/me" \
    -H "Authorization: Bearer $TOKEN")
check_response "$response" "200"
print_result "Get User Profile" $?

# Test 6: Update user profile
echo "Testing update user profile..."
response=$(curl -s -w "\n%{http_code}" -X PUT "$BASE_URL/users/me" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
        "nickname": "Updated User",
        "payment_info": {
            "paypal_email": "<EMAIL>",
            "bank_account": "**********",
            "bank_name": "Test Bank",
            "bank_branch": "Test Branch"
        }
    }')
check_response "$response" "200"
print_result "Update User Profile" $?

# Test 7: Get user balance
echo "Testing get user balance..."
response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/users/me/balance" \
    -H "Authorization: Bearer $TOKEN")
check_response "$response" "200"
print_result "Get User Balance" $?

# Test 8: Invalid login
echo "Testing invalid login..."
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/users/login" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "wrongpassword"
    }')
check_response "$response" "401"
print_result "Invalid Login" $?

# Test 9: Protected endpoint without token
echo "Testing protected endpoint without token..."
response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/users/me")
check_response "$response" "401"
print_result "Protected Endpoint Without Token" $?

# Test 10: Invalid token
echo "Testing invalid token..."
response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/users/me" \
    -H "Authorization: Bearer invalid_token")
check_response "$response" "401"
print_result "Invalid Token" $?

# Print test summary
echo "----------------------------------------"
echo "Test Summary:"
echo "Total tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $((TOTAL_TESTS - PASSED_TESTS))"

# Clean up
rm -f $TOKEN_FILE

# Exit with status code based on test results
[ $PASSED_TESTS -eq $TOTAL_TESTS ]
