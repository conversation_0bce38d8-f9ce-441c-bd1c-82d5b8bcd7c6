Help Tips Dictionary History Forums Contact
Curly bracket
Alternatively known as an open brace , close brace , squiggly brackets , and flower brackets , curly brackets are found on the same keys as the open bracket [ and close bracket ] on U.S. keyboards.
Where are the curly bracket keys on the keyboard?
How to create an open and close curly bracket.
How are curly brackets used?
What are the other symbols on the curly bracket keys?
Related information.
Keyboard help and support.
Below is an overview of a computer keyboard with the open and close curly bracket keys highlighted in blue.
How to create an open and close curly bracket
Creating the "{" and "}" symbols on a U.S. keyboard
On English keyboards, the open bracket and close bracket are on the same key as [ and ] ( square bracket ) keys, located near Enter . To get a curly bracket, press and hold Shift , then press { or } keyboard key.
Doing the Alt code Alt +123 creates an open curly bracket, and Alt +125 creates a closed curly bracket.
Creating the "{" and "}" symbol on a smartphone or tablet
To create a curly bracket on a smartphone or tablet , open the keyboard, go to the numbers (123) menu, then the (#+=) or symbols (sym) menu, and tap the "{" or "}" symbol.
Programming
Curly brackets are commonly used in programming languages such as C , Java , Perl , and PHP (PHP: Hypertext Preprocessor) to enclose groups of statements or blocks of code . Below is an example of what an enclosed statement looks like in Perl.
In the example above are two enclosed statements using curly brackets. The first group is the do loop that continues to increase the value of the $value variable up to 100. In that group is the if statement to check if $value is greater than or equal to 10. Once $value is greater than or equal to 10, it prints a message and exits.
Field codes
In word processors , like Microsoft Word , curly brackets help identify field codes .
CSS
With CSS (Cascading Style Sheets), curly brackets enclose the values of a style. For example, in the following CSS code, the hyperlinks are changed to bold red text.
Regular expressions
With a regular expression , the curly brackets specify the number of occurrences of a pattern. For example, "a{3}" would look for matches of three a's (aaa). To have a more greedy match, "a{3,}" would match anything with three or more a's. Finally, if you wanted to be a little less greedy, "a{3,5}" would match three, four, or five a's.
Mathematics
With mathematics, the curly brackets represent sets (e.g., {1,2,3}) and functions (e.g., f(x)={x+1,x-1}).
On U.S. keyboards, { and } (curly bracket) keys are shared with [ or ] ( square bracket ) keys. To create the curly bracket, press and hold Shift , and press { or } keyboard key.
Related information
Computer keyboard key explanations.
Bracket , Delimiter , Keyboard terms , Parenthesis , Programming terms , Punctuation , Square bracket , Typography terms
Was this page useful? Yes No Feedback E-mail Share Print
Feedback E-mail Share Print
Recently added pages View all recent updates Useful links About Computer Hope Site Map Forum Contact Us How to Help Top 10 pages Follow us Facebook YouTube RSS
