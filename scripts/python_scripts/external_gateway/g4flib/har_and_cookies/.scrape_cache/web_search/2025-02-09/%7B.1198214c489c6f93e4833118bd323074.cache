Title: Curly Brackets (Curly Braces) { } A Super Simple Guide - 7ESL

Home » Knowledge Base » Writing » Punctuation » Curly Brackets (Curly Braces) { } A S...
Curly Brackets (Curly Braces) { } A Super Simple Guide
Contents
Types of Brackets Curly Brackets or Curly Braces
What are curly brackets or curly braces? When it comes to writing, using the correct punctuation throughout is very important. It’s a way you can indicate pauses, as well as, the importance in specific ideas and thoughts that you are presenting in your writing.
By doing this, you are ensuring that your writing is clear and understandable to the reader. Using the correct punctuation in your writing is especially important when it comes to writing for academic and professional purposes, as you are conveying confidence through your writing to the reader.
Types of Brackets
Brackets are a very popular form of punctuation in writing. It’s a way the writer can add some extra information without having to change the overall meaning of the sentence. There are four types of brackets in the world of punctuation, these are:
Round Brackets or Parentheses , which look like this: ( … )
Curly Brackets or Braces, which look like this: { … }
Square Brackets or Brackets , which look like this: [ … ]
Angle Brackets or Chevrons, which look like this: < … >
Today, we will be specifically focusing on curly brackets or braces. Let’s learn more about them…
Curly Brackets or Curly Braces
What Is A Curly Bracket?
As we have mentioned above, a curly bracket/brace or a flower bracket in India looks like this: { }. If you find that you’ve never encountered this form of bracket, don’t worry because you most likely never will as they are very rarely used. However, if you do happen to stumble upon this rare type of bracket, then count yourself lucky because you are about to find out what it means!
Although curly brackets are most commonly used in the field of physics and high-level mathematics, they are also used in writing and music. In writing, curly brackets or braces are used to indicate that certain words and/or sentences should be looked at as a group. Here is an example:
Hello, please pick your pizza toppings {chicken, tomatoes, bacon, sausage, onion, pepper, olives} and then follow me.
In music this type of bracket is referred to as ‘braces’ or ‘accolades’ and they are used to connect two or more lines of music together, to show that they should be played at the same time. In mathematics , curly brackets are used to delimit sets, as well as, indicate the Poisson bracket between two quantities.
When Should You Use Curly Brackets?
Curly brackets/braces were invented in 1967 by Martin Richards . Although this bracket hasn’t been around for as long as the others, it quickly lost its popularity and therefore should be avoided in formal writing, as it is no longer considered appropriate for modern writing.
However, if you plan on using it. Make sure it’s for the right occasion and used correctly, such as for the topics mentioned above. Remember that in writing, curly brackets are used to group statements or ideas together and create a loop. This type of bracket is all about control structure!
Something we did not mention, is that you can also use curly brackets while texting to create a virtual hug, for example: ( {) I love curly brackets (})
About Latest Posts
Judgement vs. Judgment: A Look at Spelling Variations - January 9, 2024 Crochet vs. Knit: Understanding the Differences - January 5, 2024 Metric vs. Imperial: What’s the Difference? - December 28, 2023
Improve your writing with the help of AI writing assistants!
Grammar Checker


Source: [[0]](https://7esl.com/curly-brackets-curly-braces/)


Title: Bracket - Wikipedia

Bracket
Afrikaans العربية Azərbaycanca 閩南語 / Bân-lâm-gú Башҡортса Беларуская Беларуская (тарашкевіца) Български Català Чӑвашла Čeština Dansk Deutsch Esperanto فارسی Frysk Galego ગુજરાતી Хальмг 한국어 Հայերեն हिन्दी Hrvatski Ido Bahasa Indonesia Italiano עברית ქართული Қазақша Kiswahili Latina Latviešu Lëtzebuergesch Lietuvių Magyar Македонски Malagasy മലയാളം Nederlands नेपाली 日本語 Norsk bokmål Norsk nynorsk Oʻzbekcha / ўзбекча ਪੰਜਾਬੀ Polski Português Română Русский سرائیکی Shqip Simple English سنڌي Slovenčina Slovenščina Српски / srpski Srpskohrvatski / српскохрватски Suomi Svenska Tagalog Татарча / tatarça ไทย Тоҷикӣ Türkçe Українська اردو Tiếng Việt Walon Winaray 吴语 粵語 中文
Article Talk
Read Edit View history
What links here Related changes Upload file Special pages Permanent link Page information Cite this page Get shortened URL Download QR code
Download as PDF Printable version
Wikimedia Commons Wikidata item
This article needs additional citations for verification . Please help improve this article by adding citations to reliable sources . Unsourced material may be challenged and removed. Find sources: "Bracket" – news · newspapers · books · scholar · JSTOR ( March 2022 ) ( Learn how and when to remove this message )
Brackets
( )
[ ]
{ }
⟨ ⟩
Brackets ( BE ) or parentheses ( AE ) or round brackets (BE) [ 1 ]
Square brackets (BE) or brackets (AE) [ 1 ]
Braces (BE&AE) or curly brackets (BE) [ 1 ]
Angle brackets (BE&AE) [ 1 ] or chevrons [ 2 ]
A bracket is either of two tall fore- or back-facing punctuation marks commonly used to isolate a segment of text or data from its surroundings. [ 3 ] They come in four main pairs of shapes, as given in the box to the right, which also gives their names, that vary between British and American English . [ 1 ] "Brackets", without further qualification, are in British English the ( ... ) marks and in American English the [ ... ] marks. [ 1 ] [ 3 ]
Other symbols are repurposed as brackets in specialist contexts, such as those used by linguists . [ 4 ]
Brackets are typically deployed in symmetric pairs, and an individual bracket may be identified as a 'left' or 'right' bracket or, alternatively, an "opening bracket" or "closing bracket", [ 5 ] respectively, depending on the directionality of the context.
In casual writing and in technical fields such as computing or linguistic analysis of grammar , brackets nest , with segments of bracketed material containing embedded within them other further bracketed sub-segments. [ 3 ] The number of opening brackets matches the number of closing brackets in such cases. [ 3 ]
Various forms of brackets are used in mathematics , with specific mathematical meanings, often for denoting specific mathematical functions and subformulas .
History
Angle brackets or chevrons ⟨ ⟩ were the earliest type of bracket to appear in written English . Erasmus coined the term lunula to refer to the round brackets or parentheses ( ) recalling the shape of the crescent moon ( Latin : luna ). [ 6 ]
Most typewriters only had the left and right parentheses. Square brackets appeared with some teleprinters.
Braces (curly brackets) first became part of a character set with the 8-bit code of the IBM 7030 Stretch . [ 7 ]
In 1961, ASCII contained parentheses, square, and curly brackets, and also less-than and greater-than signs that could be used as angle brackets.
Typography
In English, typographers mostly prefer not to set brackets in italics , even when the enclosed text is italic. [ 8 ] However, in other languages like German , if brackets enclose text in italics, they are usually also set in italics. [ 9 ]
Parentheses or round brackets
![The use of braces in chemistry is an old notation that has long since been superseded by subscripted numbers.31 The chemical formula for water, H2O, was represented as H H } O {\displaystyle \left.{{H} \atop {H}}\right\}O} .31](https://wikimedia.org/api/rest_v1/media/math/render/svg/52f3a4aa7be00231e8e8deecdfa9f570d1936cca)


Source: [[1]](https://en.wikipedia.org/wiki/Bracket)


Title: What is a Curly Bracket? - Computer Hope

Help Tips Dictionary History Forums Contact
Curly bracket
Alternatively known as an open brace , close brace , squiggly brackets , and flower brackets , curly brackets are found on the same keys as the open bracket [ and close bracket ] on U.S. keyboards.
Where are the curly bracket keys on the keyboard?
How to create an open and close curly bracket.
How are curly brackets used?
What are the other symbols on the curly bracket keys?
Related information.
Keyboard help and support.
Below is an overview of a computer keyboard with the open and close curly bracket keys highlighted in blue.
How to create an open and close curly bracket
Creating the "{" and "}" symbols on a U.S. keyboard
On English keyboards, the open bracket and close bracket are on the same key as [ and ] ( square bracket ) keys, located near Enter . To get a curly bracket, press and hold Shift , then press { or } keyboard key.
Doing the Alt code Alt +123 creates an open curly bracket, and Alt +125 creates a closed curly bracket.
Creating the "{" and "}" symbol on a smartphone or tablet
To create a curly bracket on a smartphone or tablet , open the keyboard, go to the numbers (123) menu, then the (#+=) or symbols (sym) menu, and tap the "{" or "}" symbol.
Programming
Curly brackets are commonly used in programming languages such as C , Java , Perl , and PHP (PHP: Hypertext Preprocessor) to enclose groups of statements or blocks of code . Below is an example of what an enclosed statement looks like in Perl.
In the example above are two enclosed statements using curly brackets. The first group is the do loop that continues to increase the value of the $value variable up to 100. In that group is the if statement to check if $value is greater than or equal to 10. Once $value is greater than or equal to 10, it prints a message and exits.
Field codes
In word processors , like Microsoft Word , curly brackets help identify field codes .
CSS
With CSS (Cascading Style Sheets), curly brackets enclose the values of a style. For example, in the following CSS code, the hyperlinks are changed to bold red text.
Regular expressions
With a regular expression , the curly brackets specify the number of occurrences of a pattern. For example, "a{3}" would look for matches of three a's (aaa). To have a more greedy match, "a{3,}" would match anything with three or more a's. Finally, if you wanted to be a little less greedy, "a{3,5}" would match three, four, or five a's.
Mathematics
With mathematics, the curly brackets represent sets (e.g., {1,2,3}) and functions (e.g., f(x)={x+1,x-1}).
On U.S. keyboards, { and } (curly bracket) keys are shared with [ or ] ( square bracket ) keys. To create the curly bracket, press and hold Shift , and press { or } keyboard key.
Related information
Computer keyboard key explanations.
Bracket , Delimiter , Keyboard terms , Parenthesis , Programming terms , Punctuation , Square bracket , Typography terms
Was this page useful? Yes No Feedback E-mail Share Print
Feedback E-mail Share Print
Recently added pages View all recent updates Useful links About Computer Hope Site Map Forum Contact Us How to Help Top 10 pages Follow us Facebook YouTube RSS


Source: [[2]](https://www.computerhope.com/jargon/c/curlybra.htm)


Title: Brackets vs braces vs parentheses: What's the difference? - TheServerSide

In languages such as Java and C, braces or curly brackets define the start and end of language constructs such as classes, methods and loops. Generally speaking, braces also define the scope of a variable. For example, a variable defined within brackets that delineate the start and end of a class will have scope inside all of the class's ...

Source: [[3]](https://www.theserverside.com/blog/Coffee-Talk-Java-News-Stories-and-Opinions/Whats-the-difference-between-brackets-braces-and-parentheses)


Title: Curly Brackets - The Art of Grammar

The Art of Grammar Docs
Home Docs Grammar Terminology Curly Brackets
Grammar Terminology
Active Voice Adaptability Adjectives Adjunct Adverbs Adverbial Adverbial Phrase Alliteration Affirmative Ambiguity Analogy Anaphora Anecdotal Antecedent Antithesis Appositive Apostrophe Articulate Aspect Assonance Authenticity Auxiliary Verb Base Form Brackets Case Causative Verb Chiasmus Clarity Clause Cleft Sentences Cohesion Colloquial Language Comma Communication Style Complement Complexity Complex Sentence Compound-Complex Sentence Compound Predicates Components of a Sentence Compound Sentence Conditional Conditional Clauses Conduplicatio Conjugate Conjunctions Constructive Criticism Context Continuous Contraction Contrasting Ideas Convey Coordinate Adjectives Coordinating Conjunctions Curly Brackets Dangling Modifiers Dangling Participle Declarative Sentence Dependent Clause Descriptive Phrase Directives Direct Object Double Negatives Determiner Diction Direct Speech Ellipsis Em Dash Emphasis En Dash Epistrophe Exclamation Point Exclamatory Sentence Expletives Finite Verb First Conditional Fragment Gender Bias Genitive Case Genre Gerund Grammar Grammatical Conventions Hanging Participle Helping Verb Homophones Hyperbole Hyphen Idioms Imperative Independent Clause Indirect Object Infinitive Inflection Interjection Interplay Interrogative Sentence Intervening Dependent Clause Introductory Dependent Clause Inversion Language Mechanics Language Structure Linguistic Palette Literary Device Metaphor Mimicry Misplaced Modifiers Mood Morphology Motivational Negation Negative Noun Nuances Object Objective Orthography Parallel Structure Parenthesis Participial Phrase Parts of Speech Passive Voice Past Participle Period Perspective Persuade Persuasive Language Phrase Plural Precision Predicate Prefix Prepositions Prepositional Phrases Pronoun Prose Punctuation Question Mark Redundant Register Relative Clauses Resonance Rhetorical Devices Rhetorical Questions Simile Simple Sentence Sentence Sentence Structure Serial or Oxford Comma Series Singular and Plural Subjects Slang Slash Sophisticated Split Infinitive Subject Subjective Subjunctive Subordinate Clause Subordinating Conjunctions Subtle Superlative Syntactic Order Syntax Temporal Tone Quantifier Verbs Verb Tenses Versatility Voice Word Choice Zero Conditional
Support
Reporting an Error or Mistake Requests for Additional Content Contact Us
Commonly Confused Words


Source: [[4]](https://artofgrammar.com/docs/curly-brackets/)