import json
import re
import g4f


def send_message(prompt_info, gpt_model_name, proxy_info):
    g4f.debug.version_check = False
    # Automatic selection of provider
    gpt_model_name = gpt_model_name.strip()
    function_gpt_model = gpt_model_name
    # 构造语句
    statement = f"gpt_model = g4f.models.{function_gpt_model}"
    # 执行语句
    gpt_model = g4f.models.default
    try:
        exec(statement)
    except AttributeError:
        print("无效的 gpt model，改为使用 default", gpt_model_name)
        gpt_model = g4f.models.default
    ret = g4f.ChatCompletion.create(
        model=gpt_model,
        messages=[{"role": "user", "content": prompt_info}],
        proxy=proxy_info,
    )  # alternative model setting
    match = re.search(r'\{[^{}]*\}', ret)
    if match:
        json_str = match.group()
        try:
            data = json.loads(json_str)
        except:
            # 可能没有冒号
            json_str = json_str.replace('"description"', '"description":')
            data = json.loads(json_str)
        print("解析成功！")
        return data
    else:
        # 解析失败，标题和描述留空
        print("解析失败！")
        return ""


if __name__ == "__main__":
    prompt_info = '''Generate a concise, professional introduction for greekgear.com, crafted in a commercial tone to highlight its status as the ultimate online destination for authentic products. Seamlessly weave in the appeal of shopping directly at greekgear.com, emphasizing trust, quality, and exclusivity to subtly inspire purchases. Naturally integrate SEO-friendly phrases that reflect its purpose—showcasing it as the official source for exploring and buying—while keeping the language fluid and engaging. Avoid any mention of cashback or rewards, and focus on credibility and customer appeal within a minimal word count.
Deliver the output in the following JSON format:
{
 "description": "..."
}
'''

    gpt_list = ["grok_3", "deepseek_r1", "gemini_2_0_flash_thinking", "grok_3_r1", "claude_3_5_sonnet",
                "claude_3_7_sonnet_thinking", "grok_3", "deepseek_v3"]
    for gpt_model_name in gpt_list:
        data = send_message(prompt_info, gpt_model_name, "socks5://Idv8YNmuqPmkqWx:DhSD35p2yyms6Cz@*************:41551")
        print(data)
        print(gpt_model_name)

        # file_name = './resp/' + gpt_model_name + '.md'
        # with open(file_name, 'w', encoding='utf-8') as file:
        #     file.write(data)
