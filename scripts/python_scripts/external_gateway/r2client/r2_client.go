package r2client

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"mime"
	"path/filepath"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
)

const (
	// DefaultRegion is the default region for Cloudflare R2
	DefaultRegion = "auto"
	// DefaultEndpoint is the default endpoint format for Cloudflare R2
	DefaultEndpointFormat = "https://%s.r2.cloudflarestorage.com"
	// DefaultTimeout is the default timeout for S3 operations
	DefaultTimeout = 30 * time.Second
	// DefaultACL is the default ACL for uploaded objects
	DefaultACL = "public-read"
)

// R2Client is a client for Cloudflare R2 storage
type R2Client struct {
	client     *s3.Client
	bucketName string
	publicURL  string
}

// NewR2Client creates a new R2Client
func NewR2Client(accountID, accessKeyID, secretAccessKey, bucketName, publicURL string) (*R2Client, error) {
	// Create AWS config
	cfg, err := config.LoadDefaultConfig(context.Background(),
		config.WithRegion(DefaultRegion),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			accessKeyID,
			secretAccessKey,
			"",
		)),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	// Create S3 client with custom endpoint
	endpointURL := fmt.Sprintf(DefaultEndpointFormat, accountID)
	client := s3.NewFromConfig(cfg, func(o *s3.Options) {
		o.BaseEndpoint = aws.String(endpointURL)
	})

	return &R2Client{
		client:     client,
		bucketName: bucketName,
		publicURL:  publicURL,
	}, nil
}

// UploadFile uploads a file to R2 storage
func (c *R2Client) UploadFile(ctx context.Context, key string, data []byte) (string, error) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, DefaultTimeout)
	defer cancel()

	// Determine content type
	contentType := mime.TypeByExtension(filepath.Ext(key))
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	// Upload file
	_, err := c.client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:      aws.String(c.bucketName),
		Key:         aws.String(key),
		Body:        bytes.NewReader(data),
		ContentType: aws.String(contentType),
		ACL:         types.ObjectCannedACLPublicRead,
	})
	if err != nil {
		return "", fmt.Errorf("failed to upload file: %w", err)
	}

	// Return public URL
	return c.GetPublicURL(key), nil
}

// BatchUploadFiles uploads multiple files to R2 storage
func (c *R2Client) BatchUploadFiles(ctx context.Context, files map[string][]byte) (map[string]string, error) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, DefaultTimeout*2) // Longer timeout for batch
	defer cancel()

	// Map to store results
	results := make(map[string]string)

	// Upload each file
	for key, data := range files {
		url, err := c.UploadFile(ctx, key, data)
		if err != nil {
			return results, fmt.Errorf("failed to upload file %s: %w", key, err)
		}
		results[key] = url
	}

	return results, nil
}

// GetPublicURL returns the public URL for an object
func (c *R2Client) GetPublicURL(key string) string {
	return fmt.Sprintf("%s/%s", c.publicURL, key)
}

// GetObject retrieves an object from R2 storage
func (c *R2Client) GetObject(ctx context.Context, key string) ([]byte, error) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, DefaultTimeout)
	defer cancel()

	// Get object
	resp, err := c.client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(c.bucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get object: %w", err)
	}
	defer resp.Body.Close()

	// Read object data
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read object data: %w", err)
	}

	return data, nil
}

// DeleteObject deletes an object from R2 storage
func (c *R2Client) DeleteObject(ctx context.Context, key string) error {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, DefaultTimeout)
	defer cancel()

	// Delete object
	_, err := c.client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(c.bucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		return fmt.Errorf("failed to delete object: %w", err)
	}

	return nil
}

// ListObjects lists objects in R2 storage
func (c *R2Client) ListObjects(ctx context.Context, prefix string) ([]string, error) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, DefaultTimeout)
	defer cancel()

	// List objects
	resp, err := c.client.ListObjectsV2(ctx, &s3.ListObjectsV2Input{
		Bucket: aws.String(c.bucketName),
		Prefix: aws.String(prefix),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list objects: %w", err)
	}

	// Extract keys
	var keys []string
	for _, obj := range resp.Contents {
		keys = append(keys, *obj.Key)
	}

	return keys, nil
}
