import psycopg2
import json
import os

# 数据库配置
# DB_CONFIG = {
#     'host': '127.0.0.1',
#     'port': 5432,
#     'database': 'bonusearned',
#     'user': 'postgres',
#     'password': 'postgres'
# }
DB_CONFIG = {
    'host': '**************',
    'port': 14614,
    'database': 'bonusearned',
    'user': 'user_SgXD8fYdcn7mPs6kttjk',
    'password': 'password_BkGnEmjRrBEJYcv8xHsU'
}
# 记录文件路径
RECORD_FILE = 'updated_merchant_categories.json'

# 旧分类ID到新分类ID的映射
CATEGORY_ID_MAPPING = {
    1: 66,   # Arts & Entertainment -> Arts & Entertainment
    2: 53,   # Autos & Vehicles -> Travel & Transportation
    3: 51,   # Beauty & Fitness -> Health & Beauty
    4: 63,   # Books & Literature -> Science & Reference
    5: 57,   # Business & Industrial -> Business & Industrial
    6: 70,   # Computers & Electronics -> Home & Electronics
    7: 55,   # Finance -> Consumer Goods & Shopping
    8: 67,   # Food & Drink -> Food & Drink
    9: 56,   # Games -> Toys & Games
    10: 51,  # Health -> Health & Beauty
    11: 59,  # Hobbies & Leisure -> Hobbies & Leisure
    12: 70,  # Home & Garden -> Home & Electronics
    13: 70,  # Internet & Telecom -> Home & Electronics
    14: 68,  # Jobs & Education -> Jobs & Education
    15: 71,  # Law & Government -> Others
    16: 71,  # News -> Science & Reference
    17: 60,  # Online Communities -> People & Society
    18: 60,  # People & Society -> People & Society
    19: 52,  # Pets & Animals -> Pets & Animals
    20: 71,  # Real Estate -> Real Estate
    21: 71,  # Reference -> Science & Reference
    22: 71,  # Science -> Science & Reference
    23: 58,  # Sports -> Sports & Fitness
    24: 53,  # Travel & Transportation -> Travel & Transportation
    25: 53,  # World Localities -> Travel & Transportation
    26: 55,  # Antiques & Collectibles -> Consumer Goods & Shopping
    27: 69,  # Apparel -> Fashion & Apparel
    28: 54,  # Auctions -> Auctions & Classifieds
    29: 54,  # Classifieds -> Auctions & Classifieds
    30: 55,  # Consumer Resources -> Consumer Goods & Shopping
    31: 55,  # Discount & Outlet Stores -> Consumer Goods & Shopping
    32: 66,  # Entertainment Media -> Arts & Entertainment
    33: 64,  # Gifts & Special Event Items -> Gifts & Events
    34: 65,  # Green & Eco-Friendly Shopping -> Eco-Friendly Products
    35: 69,  # Luxury Goods -> Fashion & Apparel
    36: 55,  # Mass Merchants & Department Stores -> Consumer Goods & Shopping
    37: 70,  # Photo & Video Services -> Home & Electronics
    38: 55,  # Shopping Portals -> Consumer Goods & Shopping
    39: 59,  # Swap Meets & Outdoor Markets -> Hobbies & Leisure
    40: 56,  # Toys -> Toys & Games
    41: 55,  # Wholesalers & Liquidators -> Consumer Goods & Shopping
    42: 71   # Others -> Others
}

def get_db_connection():
    """创建数据库连接"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def load_updated_merchants():
    """加载已更新的商家记录"""
    if not os.path.exists(RECORD_FILE):
        return set()
    try:
        with open(RECORD_FILE, 'r', encoding='utf-8') as f:
            return set(json.load(f))
    except Exception as e:
        print(f"加载更新记录失败: {e}")
        return set()

def save_updated_merchant(merchant_id):
    """保存已更新的商家ID"""
    updated_merchants = load_updated_merchants()
    updated_merchants.add(merchant_id)
    try:
        with open(RECORD_FILE, 'w', encoding='utf-8') as f:
            json.dump(list(updated_merchants), f)
    except Exception as e:
        print(f"保存更新记录失败: {e}")

def update_merchant_categories():
    """更新商家分类信息"""
    conn = get_db_connection()
    if not conn:
        return

    try:
        cur = conn.cursor()
        # 获取所有商家信息，包括当前的category_id
        cur.execute("SELECT id, name, category_id FROM merchants")
        merchants = cur.fetchall()

        # 加载已更新的商家记录
        updated_merchants = load_updated_merchants()

        # 更新商家分类
        for merchant_id, name, current_category_id in merchants:
            # 跳过已更新的商家
            if str(merchant_id) in updated_merchants:
                print(f"商家 {name}(ID: {merchant_id}) 已更新分类，跳过")
                continue

            # 获取新的分类ID
            new_category_id = CATEGORY_ID_MAPPING.get(current_category_id)
            if new_category_id is None:
                print(f"商家 {name}(ID: {merchant_id}) 的旧分类ID {current_category_id} 未找到映射，跳过")
                continue

            # 更新数据库
            cur.execute(
                "UPDATE merchants SET category_id = %s WHERE id = %s",
                (new_category_id, merchant_id)
            )
            conn.commit()

            # 记录更新
            save_updated_merchant(str(merchant_id))
            print(f"已更新商家 {name}(ID: {merchant_id}) 的分类从 {current_category_id} 到 {new_category_id}")

    except Exception as e:
        print(f"更新过程中出错: {e}")
        conn.rollback()
    finally:
        cur.close()
        conn.close()

def main():
    print("开始更新商家分类信息...")
    update_merchant_categories()
    print("更新完成")

if __name__ == '__main__':
    main()