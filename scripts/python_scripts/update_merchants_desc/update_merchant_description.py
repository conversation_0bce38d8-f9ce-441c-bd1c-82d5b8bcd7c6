import psycopg2
import json
import os
import random
from datetime import datetime
from external_gateway.g4flib import g4fclient

# 数据库配置
# DB_CONFIG = {
#     'host': 'localhost',
#     'port': 5432,
#     'database': 'bonusearned',
#     'user': 'postgres',
#     'password': 'postgres'
# }

DB_CONFIG = {
    'host': '**************',
    'port': 14614,
    'database': 'bonusearned',
    'user': 'user_SgXD8fYdcn7mPs6kttjk',
    'password': 'password_BkGnEmjRrBEJYcv8xHsU'
}

# 记录文件路径
RECORD_FILE = 'updated_merchants.json'
gpt_list = ["gemini_2_5_pro", "claude_3_7_sonnet", "grok_3","gpt_4_1"]
proxy_list = ["socks5://1DObNT8oat6sDS6:3n0djRB75uI7vAO@**************:46202", "socks5://gXo22lxFGFSaIWU:z59zbpZAt3VDonD@*************:48484", "socks5://ZkITwpaKOOjsHHR:ertDgc7YwcF6gPI@************:45268", "socks5://pmxxXM4iiAmB3xY:tW7B6ylkNzeUiOj@***********:41886", "socks5://vf8w2gVu7QeipGS:5MPPlMmlaub9jSZ@*************:42593", "socks5://EJKKc8HxEP7XTcD:62RLfha6BuSkGSo@*************:43766"]

prompt_list = ['''Generate a concise, professional introduction for {site}, crafted in a commercial tone to highlight its status as the ultimate online destination for authentic products. Seamlessly weave in the appeal of shopping directly at {site}, emphasizing trust, quality, and exclusivity to subtly inspire purchases. Naturally integrate SEO-friendly phrases that reflect its purpose—showcasing it as the official source for exploring and buying—while keeping the language fluid and engaging. Avoid any mention of cashback or rewards, and focus on credibility and customer appeal within a minimal word count.
Language: English
Deliver the output in the following JSON format:
{
 "description": "..."
}''',
'''Generate a succinct, professional introduction for {site}, designed in a commercial tone to position it as the premier online destination for authentic, high-quality products. Highlight the ease and trust of shopping at {site}, emphasizing its credibility and exclusive offerings to inspire conversions. Seamlessly incorporate SEO-optimized phrases tailored to its purpose—presenting {site} as the official source for discovering and purchasing—while ensuring the language remains engaging and natural. Avoid references to cashback or rewards, focusing solely on quality, trust, and customer appeal with minimal word count.
Language: English
Deliver the output in the following JSON format:
{
 "description": "..."
}''',
'''Create a concise, professional introduction for {site}, crafted in a commercial tone to establish it as the ultimate online hub for genuine products. Showcase the seamless shopping experience at {site}, underscoring trust, exclusivity, and superior quality to drive purchase intent. Naturally weave in SEO-friendly terms that highlight its role as the go-to platform for exploring and buying, keeping the tone compelling and approachable. Exclude any mention of cashback or rewards, prioritizing credibility and customer connection within a tight word count.
Language: English
Deliver the output in the following JSON format:
{
 "description": "..."
}''',
'''Craft a concise, professional introduction for {site}, using a commercial tone to showcase it as the ultimate online destination for premium, authentic products. Highlight the joy and trust of shopping at {site}, emphasizing its exclusive offerings and exceptional quality to spark purchase desire. Naturally blend SEO-friendly phrases that underscore its role as the official platform for discovering and acquiring, keeping the tone warm yet persuasive. Exclude cashback or rewards, focusing on emotional appeal and credibility with a minimal word count.
Language: English
Deliver the output in the following JSON format:
{
 "description": "..."
}'''
               ]


def get_db_connection():
    """创建数据库连接"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None


def get_merchant_description(merchant_id, merchant_name, website):
    """获取商家的新描述信息
    """
    prompt_info = random.choice(prompt_list)
    prompt_info = prompt_info.replace("{site}", website)
    for gpt_model_name in gpt_list:
        try:
            gpt_model_name = random.choice(gpt_list)
            proxy_info = random.choice(proxy_list)
            ret = g4fclient.send_message(prompt_info, gpt_model_name, proxy_info)
            description = ret["description"]
            return description
        except:
            continue


def load_updated_merchants():
    """加载已更新的商家记录"""
    if not os.path.exists(RECORD_FILE):
        return set()
    try:
        with open(RECORD_FILE, 'r', encoding='utf-8') as f:
            return set(json.load(f))
    except Exception as e:
        print(f"加载更新记录失败: {e}")
        return set()


def save_updated_merchant(merchant_id):
    """保存已更新的商家ID"""
    updated_merchants = load_updated_merchants()
    updated_merchants.add(merchant_id)
    try:
        with open(RECORD_FILE, 'w', encoding='utf-8') as f:
            json.dump(list(updated_merchants), f)
    except Exception as e:
        print(f"保存更新记录失败: {e}")


def update_merchant_descriptions():
    """更新商家描述信息"""
    conn = get_db_connection()
    if not conn:
        return

    try:
        cur = conn.cursor()
        # 获取所有商家信息
        cur.execute("SELECT id, name, description, website FROM merchants")
        merchants = cur.fetchall()

        # 加载已更新的商家记录
        updated_merchants = load_updated_merchants()

        # 更新商家描述
        for merchant_id, name, current_desc, website in merchants:
            # 跳过website为空的商家
            if len(website) <= 0:
                print(f"商家 {name}(ID: {merchant_id}) 官网为空，跳过")
                continue
            # 跳过已更新的商家
            if str(merchant_id) in updated_merchants:
                print(f"商家 {name}(ID: {merchant_id}) 已更新，跳过")
                continue

            # 获取新的描述信息
            new_description = get_merchant_description(merchant_id, name, website)
            print("new_description: ", new_description)
            # 更新数据库
            cur.execute(
                "UPDATE merchants SET description = %s WHERE id = %s",
                (new_description, merchant_id)
            )
            conn.commit()

            # 记录更新
            save_updated_merchant(str(merchant_id))
            print(f"已更新商家 {name}(ID: {merchant_id}) 的描述信息")

    except Exception as e:
        print(f"更新过程中出错: {e}")
        conn.rollback()
    finally:
        cur.close()
        conn.close()


def main():
    print("开始更新商家描述信息...")
    update_merchant_descriptions()
    print("更新完成")


if __name__ == '__main__':
    main()
