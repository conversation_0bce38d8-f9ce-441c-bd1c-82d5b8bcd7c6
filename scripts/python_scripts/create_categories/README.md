# 分类管理脚本

这个Python脚本用于批量创建商家分类，支持从CSV文件导入分类数据或使用默认分类数据。

## 功能特点

- 支持批量创建多个商家分类
- 支持从CSV文件导入分类数据
- 支持创建分类数据CSV模板
- 自动检查分类是否已存在，避免重复创建
- 支持更新已存在分类的名称和图标
- 支持批量更新分类状态（启用或禁用）
- 支持批量删除指定ID的分类（软删除）
- 记录已创建的分类，支持断点续传
- 提供详细的执行日志和统计信息

## 使用方法

### 1. 准备环境

确保已安装Python 3.x和psycopg2库：

```bash
pip install psycopg2
```

### 2. 运行脚本

```bash
python create_categories.py
```

### 3. 选择操作

脚本运行后会显示以下选项：

```
=== 分类管理脚本 ===
1. 创建分类数据CSV模板
2. 执行批量创建分类
3. 批量更新分类状态
4. 批量删除分类
5. 退出
请选择操作 [1/2/3/4/5]: 
```

- 选择`1`：创建一个CSV模板文件，用于填写分类数据
- 选择`2`：执行批量创建分类操作
- 选择`3`：批量更新分类状态（启用或禁用）
- 选择`4`：批量删除指定ID的分类（软删除）
- 选择`5`：退出程序

### 4. 准备分类数据

分类数据可以通过以下两种方式提供：

#### 方式一：使用CSV文件

创建一个名为`categories.csv`的文件，格式如下：

```csv
id,name,icon
1,服装鞋包,fas fa-tshirt
2,美妆个护,fas fa-spa
3,数码电器,fas fa-laptop
```

- `id`: 分类ID（必填，唯一标识）
- `name`: 分类名称（必填，不能重复）
- `icon`: 分类图标（可选，支持Font Awesome图标名称）

#### 方式二：使用默认分类数据

如果不提供CSV文件，脚本将使用内置的默认分类数据。

## 配置说明

脚本中的以下参数可以根据需要进行修改：

```python
# 数据库配置
DB_CONFIG = {
    'host': '数据库主机地址',
    'port': 数据库端口,
    'database': '数据库名称',
    'user': '用户名',
    'password': '密码'
}

# 记录文件路径
RECORD_FILE = 'created_categories.json'

# 分类数据文件路径
CATEGORIES_FILE = 'categories.csv'
```

# 分类管理脚本
## 功能说明

### 批量创建分类
执行流程：
1. 连接数据库
2. 检查categories表是否存在，不存在则创建
3. 加载分类数据（从CSV文件或使用默认数据）
4. 加载已创建的分类记录
5. 遍历分类数据，对每个分类：
   - 检查分类是否已存在
   - 如果存在且需要更新，则更新分类信息
   - 如果不存在，则创建新分类
6. 记录创建成功的分类
7. 输出统计信息

### 批量更新分类状态
执行流程：
1. 连接数据库
2. 输入要设置的状态值（0-禁用，1-启用）
3. 选择更新方式：
   - 更新所有分类
   - 按ID列表更新指定分类
4. 如选择按ID列表更新，需输入分类ID列表（逗号分隔）
5. 执行更新操作
6. 输出更新结果统计

### 批量删除分类
执行流程：
1. 连接数据库
2. 输入要删除的分类ID列表（逗号分隔）
3. 确认删除操作
4. 显示将要删除的分类信息
5. 执行软删除操作（设置deleted_at字段）
6. 输出删除结果统计

## 注意事项

- 所有脚本操作都支持幂等性，可以重复执行而不会产生重复数据
- 如果分类已存在，将根据需要更新名称和图标字段
- 执行脚本需要对数据库有写入权限
- 分类ID和名称必须唯一，否则会创建失败