#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 批量创建分类脚本 ===${NC}"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}错误: 未找到Python3，请先安装Python3${NC}"
    exit 1
fi

# 检查psycopg2是否安装
python3 -c "import psycopg2" 2>/dev/null
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}警告: 未安装psycopg2库，尝试安装...${NC}"
    pip3 install psycopg2 || pip3 install psycopg2-binary
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误: 安装psycopg2失败，请手动安装: pip install psycopg2 或 pip install psycopg2-binary${NC}"
        exit 1
    fi
fi

# 检查脚本文件是否存在
if [ ! -f "./create_categories.py" ]; then
    echo -e "${RED}错误: 找不到create_categories.py文件${NC}"
    exit 1
fi

# 执行Python脚本
echo -e "${GREEN}正在启动批量创建分类脚本...${NC}"
python3 create_categories.py

echo -e "${GREEN}脚本执行完成${NC}"