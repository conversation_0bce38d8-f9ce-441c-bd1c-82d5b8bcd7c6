import psycopg2
import json
import os
import csv
import time
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 14614,
    'database': 'bonusearned',
    'user': 'user_SgXD8fYdcn7mPs6kttjk',
    'password': 'password_BkGnEmjRrBEJYcv8xHsU'
}
# DB_CONFIG = {
#     'host': '127.0.0.1',
#     'port': 5432,
#     'database': 'bonusearned',
#     'user': 'postgres',
#     'password': 'postgres'
# }

# 记录文件路径
RECORD_FILE = 'created_categories.json'
# 分类数据文件路径（CSV格式）
CATEGORIES_FILE = 'categories.csv'


def get_db_connection():
    """创建数据库连接"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None


def load_created_categories():
    """加载已创建的分类记录"""
    if not os.path.exists(RECORD_FILE):
        return {}
    try:
        with open(RECORD_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载创建记录失败: {e}")
        return {}


def save_created_category(category_id, category_name):
    """保存已创建的分类ID和名称"""
    created_categories = load_created_categories()
    created_categories[str(category_id)] = category_name
    try:
        with open(RECORD_FILE, 'w', encoding='utf-8') as f:
            json.dump(created_categories, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存创建记录失败: {e}")


def load_categories_from_csv():
    """从CSV文件加载分类数据"""
    categories = []
    if not os.path.exists(CATEGORIES_FILE):
        print(f"分类数据文件 {CATEGORIES_FILE} 不存在，将使用默认分类数据")
        # 默认分类数据
        return [
            {"id": 1, "name": "服装鞋包", "icon": "fas fa-tshirt"},
            {"id": 2, "name": "美妆个护", "icon": "fas fa-spa"},
            {"id": 3, "name": "数码电器", "icon": "fas fa-laptop"},
            {"id": 4, "name": "家居家装", "icon": "fas fa-couch"},
            {"id": 5, "name": "食品饮料", "icon": "fas fa-utensils"},
            {"id": 6, "name": "母婴玩具", "icon": "fas fa-baby"},
            {"id": 7, "name": "运动户外", "icon": "fas fa-running"},
            {"id": 8, "name": "图书音像", "icon": "fas fa-book"},
            {"id": 9, "name": "旅游出行", "icon": "fas fa-plane"},
            {"id": 10, "name": "其他", "icon": "fas fa-ellipsis-h"}
        ]
    
    try:
        with open(CATEGORIES_FILE, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                categories.append({
                    "id": int(row.get("id", 0)),
                    "name": row.get("name", ""),
                    "icon": row.get("icon", "")
                })
        return categories
    except Exception as e:
        print(f"加载分类数据文件失败: {e}")
        return []


def create_categories():
    """批量创建分类"""
    conn = get_db_connection()
    if not conn:
        return

    # 加载分类数据
    categories = load_categories_from_csv()
    if not categories:
        print("没有分类数据可创建")
        return

    # 加载已创建的分类记录
    created_categories = load_created_categories()

    try:
        cur = conn.cursor()
        
        # 检查categories表是否存在
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'categories'
            )
        """)
        table_exists = cur.fetchone()[0]
        
        if not table_exists:
            print("categories表不存在，创建表...")
            cur.execute("""
                CREATE TABLE categories (
                    id BIGINT PRIMARY KEY,
                    name VARCHAR(50) UNIQUE NOT NULL,
                    icon VARCHAR(255),
                    status SMALLINT DEFAULT 1 NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    updated_at TIMESTAMP NOT NULL,
                    deleted_at TIMESTAMP
                )
            """)
            conn.commit()
            print("categories表创建成功")

        # 创建或更新分类
        success_count = 0
        skip_count = 0
        update_count = 0
        
        for category in categories:
            category_id = category["id"]
            category_name = category["name"]
            category_icon = category["icon"]
            
            # 跳过ID为0或名称为空的分类
            if category_id == 0 or not category_name:
                print(f"跳过无效分类: ID={category_id}, 名称={category_name}")
                skip_count += 1
                continue
                
            # 检查分类是否已存在
            cur.execute("SELECT id, name, icon FROM categories WHERE id = %s", (category_id,))
            existing_category = cur.fetchone()
            
            current_time = datetime.now()
            
            if existing_category:
                # 分类已存在，更新名称和图标
                if existing_category[1] != category_name or existing_category[2] != category_icon:
                    cur.execute("""
                        UPDATE categories 
                        SET name = %s, icon = %s, updated_at = %s 
                        WHERE id = %s
                    """, (category_name, category_icon, current_time, category_id))
                    conn.commit()
                    print(f"已更新分类: ID={category_id}, 名称={category_name}, 图标={category_icon}")
                    update_count += 1
                else:
                    print(f"分类已存在且无需更新: ID={category_id}, 名称={category_name}")
                    skip_count += 1
            else:
                # 创建新分类
                try:
                    cur.execute("""
                        INSERT INTO categories (id, name, icon, status, created_at, updated_at) 
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """, (category_id, category_name, category_icon, 1, current_time, current_time))
                    conn.commit()
                    
                    # 记录创建成功的分类
                    save_created_category(category_id, category_name)
                    
                    print(f"已创建分类: ID={category_id}, 名称={category_name}, 图标={category_icon}")
                    success_count += 1
                except psycopg2.errors.UniqueViolation:
                    # 处理唯一约束冲突
                    conn.rollback()
                    print(f"创建分类失败，名称已存在: ID={category_id}, 名称={category_name}")
                    skip_count += 1
                except Exception as e:
                    conn.rollback()
                    print(f"创建分类失败: ID={category_id}, 名称={category_name}, 错误: {e}")
                    skip_count += 1
        
        print(f"\n批量创建分类完成:")
        print(f"成功创建: {success_count} 个")
        print(f"成功更新: {update_count} 个")
        print(f"跳过处理: {skip_count} 个")

    except Exception as e:
        print(f"批量创建分类过程中出错: {e}")
        conn.rollback()
    finally:
        cur.close()
        conn.close()


def create_categories_csv_template():
    """创建分类数据CSV模板文件"""
    if os.path.exists(CATEGORIES_FILE):
        print(f"分类数据文件 {CATEGORIES_FILE} 已存在，跳过创建模板")
        return
        
    try:
        with open(CATEGORIES_FILE, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(["id", "name", "icon"])
            writer.writerow(["1", "服装鞋包", "fas fa-tshirt"])
            writer.writerow(["2", "美妆个护", "fas fa-spa"])
            writer.writerow(["3", "数码电器", "fas fa-laptop"])
        print(f"已创建分类数据CSV模板文件: {CATEGORIES_FILE}")
        print("请编辑此文件添加更多分类数据，然后重新运行脚本")
    except Exception as e:
        print(f"创建分类数据CSV模板文件失败: {e}")


def update_categories_status():
    """批量更新分类状态"""
    conn = get_db_connection()
    if not conn:
        return

    print("\n请输入要更新的分类状态 (0-禁用, 1-启用): ")
    try:
        status = int(input("状态值 [0/1]: "))
        if status not in [0, 1]:
            print("无效的状态值，必须是0或1")
            return
    except ValueError:
        print("无效的输入，必须是数字")
        return

    print("\n请选择更新方式:")
    print("1. 更新所有分类")
    print("2. 按ID列表更新指定分类")
    update_choice = input("请选择 [1/2]: ")

    category_ids = []
    if update_choice == "2":
        id_input = input("\n请输入要更新的分类ID列表，用逗号分隔 (例如: 1,2,3): ")
        try:
            category_ids = [int(id.strip()) for id in id_input.split(",") if id.strip()]
            if not category_ids:
                print("未提供有效的分类ID")
                return
        except ValueError:
            print("无效的ID输入，必须是数字")
            return

    try:
        cur = conn.cursor()
        current_time = datetime.now()
        
        # 检查categories表是否存在
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'categories'
            )
        """)
        table_exists = cur.fetchone()[0]
        
        if not table_exists:
            print("categories表不存在，无法更新分类状态")
            return

        # 构建SQL查询
        if update_choice == "1":
            # 更新所有分类
            cur.execute("""
                UPDATE categories 
                SET status = %s, updated_at = %s 
                WHERE deleted_at IS NULL
            """, (status, current_time))
            affected_rows = cur.rowcount
        else:
            # 按ID列表更新
            placeholders = ",".join("%s" for _ in category_ids)
            query = f"""
                UPDATE categories 
                SET status = %s, updated_at = %s 
                WHERE id IN ({placeholders}) AND deleted_at IS NULL
            """
            params = [status, current_time] + category_ids
            cur.execute(query, params)
            affected_rows = cur.rowcount

        conn.commit()
        print(f"\n批量更新分类状态完成: 已更新 {affected_rows} 个分类的状态为 {status}")

    except Exception as e:
        print(f"批量更新分类状态过程中出错: {e}")
        conn.rollback()
    finally:
        cur.close()
        conn.close()


def delete_categories():
    """批量删除分类"""
    conn = get_db_connection()
    if not conn:
        return

    print("\n请输入要删除的分类ID列表，用逗号分隔 (例如: 1,2,3): ")
    id_input = input("分类ID列表: ")
    
    try:
        category_ids = [int(id.strip()) for id in id_input.split(",") if id.strip()]
        if not category_ids:
            print("未提供有效的分类ID")
            return
    except ValueError:
        print("无效的ID输入，必须是数字")
        return

    # 确认删除
    confirm = input(f"\n确定要删除 {len(category_ids)} 个分类吗? [y/N]: ")
    if confirm.lower() != 'y':
        print("已取消删除操作")
        return

    try:
        cur = conn.cursor()
        current_time = datetime.now()
        
        # 检查categories表是否存在
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'categories'
            )
        """)
        table_exists = cur.fetchone()[0]
        
        if not table_exists:
            print("categories表不存在，无法删除分类")
            return

        # 获取要删除的分类信息，用于显示
        placeholders = ",".join("%s" for _ in category_ids)
        cur.execute(f"SELECT id, name FROM categories WHERE id IN ({placeholders})", category_ids)
        categories_to_delete = cur.fetchall()
        
        if not categories_to_delete:
            print("未找到指定的分类ID")
            return
            
        print("\n将删除以下分类:")
        for cat_id, cat_name in categories_to_delete:
            print(f"ID: {cat_id}, 名称: {cat_name}")
            
        # 执行软删除（设置deleted_at字段）
        query = f"""
            UPDATE categories 
            SET deleted_at = %s 
            WHERE id IN ({placeholders}) AND deleted_at IS NULL
        """
        params = [current_time] + category_ids
        cur.execute(query, params)
        affected_rows = cur.rowcount

        conn.commit()
        print(f"\n批量删除分类完成: 已删除 {affected_rows} 个分类")

    except Exception as e:
        print(f"批量删除分类过程中出错: {e}")
        conn.rollback()
    finally:
        cur.close()
        conn.close()


def main():
    print("=== 分类管理脚本 ===")
    print("1. 创建分类数据CSV模板")
    print("2. 执行批量创建分类")
    print("3. 批量更新分类状态")
    print("4. 批量删除分类")
    print("5. 退出")
    
    choice = input("请选择操作 [1/2/3/4/5]: ")
    
    if choice == "1":
        create_categories_csv_template()
    elif choice == "2":
        print("\n开始批量创建分类...")
        create_categories()
        print("批量创建分类完成")
    elif choice == "3":
        print("\n开始批量更新分类状态...")
        update_categories_status()
        print("批量更新分类状态完成")
    elif choice == "4":
        print("\n开始批量删除分类...")
        delete_categories()
        print("批量删除分类完成")
    elif choice == "5":
        print("退出程序")
    else:
        print("无效选择，请重新运行脚本")


if __name__ == '__main__':
    main()