import psycopg2
import json
import os

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 14614,
    'database': 'bonusearned',
    'user': 'user_SgXD8fYdcn7mPs6kttjk',
    'password': 'password_BkGnEmjRrBEJYcv8xHsU'
}

# 记录文件路径
RECORD_FILE = 'updated_merchants.json'


def get_db_connection():
    """创建数据库连接"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None


def load_updated_merchants():
    """加载已更新的商家记录"""
    if not os.path.exists(RECORD_FILE):
        return set()
    try:
        with open(RECORD_FILE, 'r', encoding='utf-8') as f:
            return set(json.load(f))
    except Exception as e:
        print(f"加载更新记录失败: {e}")
        return set()


def save_updated_merchant(merchant_id):
    """保存已更新的商家ID"""
    updated_merchants = load_updated_merchants()
    updated_merchants.add(merchant_id)
    try:
        with open(RECORD_FILE, 'w', encoding='utf-8') as f:
            json.dump(list(updated_merchants), f)
    except Exception as e:
        print(f"保存更新记录失败: {e}")


def update_merchant_track_urls():
    """更新商家track url"""
    conn = get_db_connection()
    if not conn:
        return

    try:
        cur = conn.cursor()
        # 获取所有商家信息
        cur.execute("SELECT id, name, track_url FROM merchants")
        merchants = cur.fetchall()

        # 加载已更新的商家记录
        updated_merchants = load_updated_merchants()

        # 更新商家track url
        for merchant_id, name, track_url in merchants:
            # 跳过track_url为空的商家
            if not track_url:
                print(f"商家 {name}(ID: {merchant_id}) track_url为空，跳过")
                continue
            # 跳过已更新的商家
            if str(merchant_id) in updated_merchants:
                print(f"商家 {name}(ID: {merchant_id}) 已更新，跳过")
                continue

            # 更新track url
            if 'https://t.bonusearned.com' in track_url:
                new_track_url = track_url.replace('https://t.bonusearned.com', 'https://bonusearned.com')
                # 更新数据库
                cur.execute(
                    "UPDATE merchants SET track_url = %s WHERE id = %s",
                    (new_track_url, merchant_id)
                )
                conn.commit()

                # 记录更新
                save_updated_merchant(str(merchant_id))
                print(f"已更新商家 {name}(ID: {merchant_id}) 的track url")
                print(f"原track url: {track_url}")
                print(f"新track url: {new_track_url}")

    except Exception as e:
        print(f"更新过程中出错: {e}")
        conn.rollback()
    finally:
        cur.close()
        conn.close()


def main():
    print("开始更新商家track url...")
    update_merchant_track_urls()
    print("更新完成")


if __name__ == '__main__':
    main()
