# Update Merchant Logos Script

This script updates merchant logos from the default logo to Clearbit logos based on the merchant's website domain.

## Purpose

The script identifies merchants using the default logo (`https://image.bonusearned.com/image/default_logo.webp`) and replaces it with a Clearbit logo URL (`https://logo.clearbit.com/{domain}`), where `{domain}` is extracted from the merchant's website.

## Requirements

- Python 3.6+
- psycopg2 package (`pip install psycopg2-binary`)

## Usage

1. Make sure the database configuration in the script is correct
2. Run the script:

```bash
python update_default_logos.py
```

## How It Works

1. The script connects to the database and retrieves all merchants
2. For each merchant, it checks if the logo is the default logo
3. If it is, it extracts the domain from the website field
4. It then updates the logo to use the Clearbit logo service
5. The script keeps track of updated merchants in `updated_logos.json`

## Notes

- The script skips merchants that have already been updated (based on the `updated_logos.json` file)
- If a merchant has no website or an invalid website, it will be skipped
- The script logs all actions to the console
