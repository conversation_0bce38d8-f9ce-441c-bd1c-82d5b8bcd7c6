import psycopg2
import json
import os
import urllib.parse

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 14614,
    'database': 'bonusearned',
    'user': 'user_SgXD8fYdcn7mPs6kttjk',
    'password': 'password_BkGnEmjRrBEJYcv8xHsU'
}

# 记录文件路径
RECORD_FILE = 'updated_logos.json'
# 默认logo URL
DEFAULT_LOGO_URL = 'https://image.bonusearned.com/image/default_logo.webp'
# Clearbit logo URL前缀
CLEARBIT_LOGO_PREFIX = 'https://logo.clearbit.com/'


def get_db_connection():
    """创建数据库连接"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None


def load_updated_merchants():
    """加载已更新的商家记录"""
    if not os.path.exists(RECORD_FILE):
        return set()
    try:
        with open(RECORD_FILE, 'r', encoding='utf-8') as f:
            return set(json.load(f))
    except Exception as e:
        print(f"加载更新记录失败: {e}")
        return set()


def save_updated_merchant(merchant_id):
    """保存已更新的商家ID"""
    updated_merchants = load_updated_merchants()
    updated_merchants.add(merchant_id)
    try:
        with open(RECORD_FILE, 'w', encoding='utf-8') as f:
            json.dump(list(updated_merchants), f)
    except Exception as e:
        print(f"保存更新记录失败: {e}")


def extract_domain_from_website(website):
    """从网站URL中提取域名"""
    if not website:
        return None
    
    # 确保URL有协议前缀
    if not website.startswith(('http://', 'https://')):
        website = 'https://' + website
    
    try:
        # 解析URL并提取域名
        parsed_url = urllib.parse.urlparse(website)
        domain = parsed_url.netloc
        
        # 移除www.前缀
        if domain.startswith('www.'):
            domain = domain[4:]
            
        return domain if domain else None
    except Exception as e:
        print(f"提取域名失败: {e}")
        return None


def update_merchant_logos():
    """更新商家默认logo为Clearbit logo"""
    conn = get_db_connection()
    if not conn:
        return

    try:
        cur = conn.cursor()
        # 获取所有商家信息
        cur.execute("SELECT id, name, logo, website FROM merchants")
        merchants = cur.fetchall()

        # 加载已更新的商家记录
        updated_merchants = load_updated_merchants()

        # 更新商家logo
        for merchant_id, name, logo, website in merchants:
            # 跳过已更新的商家
            if str(merchant_id) in updated_merchants:
                print(f"商家 {name}(ID: {merchant_id}) 已更新，跳过")
                continue

            # 检查是否使用默认logo
            if logo == DEFAULT_LOGO_URL:
                # 提取网站域名
                domain = extract_domain_from_website(website)
                
                if not domain:
                    print(f"商家 {name}(ID: {merchant_id}) 无有效网站域名，跳过")
                    continue
                
                # 构建新的logo URL
                new_logo_url = f"{CLEARBIT_LOGO_PREFIX}{domain}"
                
                # 更新数据库
                cur.execute(
                    "UPDATE merchants SET logo = %s WHERE id = %s",
                    (new_logo_url, merchant_id)
                )
                conn.commit()

                # 记录更新
                save_updated_merchant(str(merchant_id))
                print(f"已更新商家 {name}(ID: {merchant_id}) 的logo")
                print(f"原logo: {logo}")
                print(f"新logo: {new_logo_url}")
            else:
                print(f"商家 {name}(ID: {merchant_id}) 不使用默认logo，跳过")

    except Exception as e:
        print(f"更新过程中出错: {e}")
        conn.rollback()
    finally:
        cur.close()
        conn.close()


def main():
    print("开始更新商家默认logo为Clearbit logo...")
    update_merchant_logos()
    print("更新完成")


if __name__ == '__main__':
    main()
