package main

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"
)

type MerchantResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Total        int        `json:"total"`
		Page         int        `json:"page"`
		PageSize     int        `json:"page_size"`
		MerchantList []Merchant `json:"merchant_list"`
	} `json:"data"`
}

type Merchant struct {
	Id            int    `json:"id"`
	Name          string `json:"name"`
	UniqueName    string `json:"unique_name"`
	MerchantCode  string `json:"merchant_code"`
	Logo          string `json:"logo"`
	Website       string `json:"website"`
	TrackUrl      string `json:"track_url"`
	CashbackValue string `json:"cashback_value"`
	Description   string `json:"description"`
	Category      struct {
		Id   int    `json:"id"`
		Name string `json:"name"`
		Icon string `json:"icon"`
	} `json:"category"`
	Featured           bool      `json:"featured"`
	Country            string    `json:"country"`
	SupportedCountries []string  `json:"supported_countries"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// 主函数
func main() {
	log.Println("开始获取商家信息...")

	merchants, err := fetchAllMerchants()
	if err != nil {
		log.Fatalf("获取商家信息失败: %v", err)
	}

	log.Printf("成功获取 %d 个商家信息", len(merchants))

	err = saveToCSV(merchants)
	if err != nil {
		log.Fatalf("保存到CSV失败: %v", err)
	}

	log.Println("商家信息已成功保存到CSV文件")
}

// 从API获取所有商家信息
func fetchAllMerchants() ([]Merchant, error) {
	baseURL := "https://api.bonusearned.com/api/v1/merchants"
	pageSize := 1000
	currentPage := 1
	var allMerchants []Merchant

	for {
		log.Printf("正在获取第 %d 页数据...", currentPage)

		// 构建URL，包含分页参数
		url := fmt.Sprintf("%s?page=%d&page_size=%d", baseURL, currentPage, pageSize)

		// 发送HTTP请求
		resp, err := http.Get(url)
		if err != nil {
			return nil, fmt.Errorf("HTTP请求失败: %v", err)
		}
		defer resp.Body.Close()

		// 读取响应内容
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("读取响应内容失败: %v", err)
		}

		// 解析JSON响应
		var apiResp MerchantResp
		err = json.Unmarshal(body, &apiResp)
		if err != nil {
			return nil, fmt.Errorf("解析JSON失败: %v", err)
		}
		// 检查API返回的状态码
		if apiResp.Code != 200 {
			return nil, fmt.Errorf("API返回错误: %s", apiResp.Message)
		}

		// 将当前页的商家信息添加到结果中
		allMerchants = append(allMerchants, apiResp.Data.MerchantList...)

		// 判断是否已经获取完所有页
		if currentPage*pageSize >= apiResp.Data.Total {
			break
		}

		// 继续获取下一页
		currentPage++

		// 为避免请求过于频繁，添加短暂延迟
		time.Sleep(200 * time.Millisecond)
	}

	return allMerchants, nil
}

// 将商家信息保存到CSV文件
func saveToCSV(merchants []Merchant) error {
	// 生成文件名，包含日期时间
	timestamp := time.Now().Format("20060102-150405")
	filename := fmt.Sprintf("merchants-%s.csv", timestamp)

	// 创建CSV文件
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建CSV文件失败: %v", err)
	}
	defer file.Close()

	// 创建CSV写入器
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 设置标题行
	headers := []string{
		"ID", "名称", "唯一名称", "商家代码", "网站", "返利链接",
		"返利值", "分类ID", "分类名称", "是否特色",
		"国家", "支持的国家", "创建时间", "更新时间",
	}

	// 写入标题行
	if err := writer.Write(headers); err != nil {
		return fmt.Errorf("写入CSV标题行失败: %v", err)
	}

	// 写入数据行
	for _, merchant := range merchants {
		// 支持的国家列表转为字符串
		supportedCountries := ""
		if len(merchant.SupportedCountries) > 0 {
			bytes, _ := json.Marshal(merchant.SupportedCountries)
			supportedCountries = string(bytes)
		}

		// 将布尔值转换为字符串
		featuredStr := "否"
		if merchant.Featured {
			featuredStr = "是"
		}

		// 格式化时间
		createdAt := merchant.CreatedAt.Format("2006-01-02 15:04:05")
		updatedAt := merchant.UpdatedAt.Format("2006-01-02 15:04:05")

		// 构建数据行
		row := []string{
			strconv.Itoa(merchant.Id),          // ID
			merchant.Name,                      // 名称
			merchant.UniqueName,                // 唯一名称
			merchant.MerchantCode,              // 商家代码
			merchant.Website,                   // 网站
			merchant.TrackUrl,                  // 返利链接
			merchant.CashbackValue,             // 返利值
			strconv.Itoa(merchant.Category.Id), // 分类ID
			merchant.Category.Name,             // 分类名称
			featuredStr,                        // 是否特色
			merchant.Country,                   // 国家
			supportedCountries,                 // 支持的国家
			createdAt,                          // 创建时间
			updatedAt,                          // 更新时间
		}

		// 写入数据行
		if err := writer.Write(row); err != nil {
			return fmt.Errorf("写入CSV数据行失败: %v", err)
		}
	}

	// 确保所有数据写入文件
	writer.Flush()

	if err := writer.Error(); err != nil {
		return fmt.Errorf("CSV写入器错误: %v", err)
	}

	// 输出保存路径
	absPath, _ := os.Getwd()
	log.Printf("文件已保存到: %s/%s", absPath, filename)

	return nil
}
