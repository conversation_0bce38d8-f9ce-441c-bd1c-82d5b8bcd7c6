package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strings"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// Merchant 简化的商家结构体，仅包含我们需要的字段
type Merchant struct {
	ID       uint64 `gorm:"primarykey"`
	Name     string
	Website  string
	Featured bool
}

func main() {
	// 读取 featured.txt 文件
	featuredWebsites, err := readFeaturedWebsites("/Users/<USER>/projects/bonusearned/scripts/golang/update_merchants/featured.txt")
	if err != nil {
		log.Fatalf("读取 featured.txt 文件失败: %v", err)
	}
	log.Printf("从 featured.txt 读取到 %d 个网站", len(featuredWebsites))

	// 读取环境变量或使用默认值
	host := getEnv("DB_HOST", "**************")
	port := getEnv("DB_PORT", "14614")
	user := getEnv("DB_USER", "user_SgXD8fYdcn7mPs6kttjk")
	password := getEnv("DB_PASSWORD", "password_BkGnEmjRrBEJYcv8xHsU")
	dbname := getEnv("DB_NAME", "bonusearned")
	sslmode := getEnv("DB_SSLMODE", "disable")
	timezone := getEnv("DB_TIMEZONE", "Asia/Shanghai")

	// 构建数据库连接字符串
	dsn := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		host, port, user, password, dbname, sslmode, timezone,
	)

	// 连接数据库
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("无法连接到数据库: %v", err)
	}

	// 获取一个SQL连接
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("无法获取数据库连接: %v", err)
	}
	defer sqlDB.Close()

	// 获取所有商家
	var merchants []Merchant
	if err := db.Find(&merchants).Error; err != nil {
		log.Fatalf("获取商家列表失败: %v", err)
	}

	log.Printf("数据库中共有 %d 个商家记录", len(merchants))

	if len(merchants) == 0 {
		log.Fatalf("数据库中没有商家记录")
	}

	// 统计需要更新的商家
	var toUpdateFeatured []uint64   // 需要设置为 featured=true 的商家ID
	var toUpdateUnfeatured []uint64 // 需要设置为 featured=false 的商家ID

	for _, merchant := range merchants {
		shouldBeFeatured := isWebsiteInFeaturedList(merchant.Website, featuredWebsites)

		if shouldBeFeatured && !merchant.Featured {
			// 应该是featured但当前不是，需要更新为true
			toUpdateFeatured = append(toUpdateFeatured, merchant.ID)
		} else if !shouldBeFeatured && merchant.Featured {
			// 不应该是featured但当前是，需要更新为false
			toUpdateUnfeatured = append(toUpdateUnfeatured, merchant.ID)
		}
	}

	log.Printf("需要设置为 featured=true 的商家数量: %d", len(toUpdateFeatured))
	log.Printf("需要设置为 featured=false 的商家数量: %d", len(toUpdateUnfeatured))

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		log.Fatalf("开始事务失败: %v", tx.Error)
	}

	var totalUpdated int64 = 0

	// 更新需要设置为 featured=true 的商家
	if len(toUpdateFeatured) > 0 {
		result := tx.Model(&Merchant{}).Where("id IN ?", toUpdateFeatured).Update("featured", true)
		if result.Error != nil {
			tx.Rollback()
			log.Fatalf("更新 featured=true 失败: %v", result.Error)
		}
		totalUpdated += result.RowsAffected
		log.Printf("成功将 %d 个商家的featured字段更新为true", result.RowsAffected)
	}

	// 更新需要设置为 featured=false 的商家
	if len(toUpdateUnfeatured) > 0 {
		result := tx.Model(&Merchant{}).Where("id IN ?", toUpdateUnfeatured).Update("featured", false)
		if result.Error != nil {
			tx.Rollback()
			log.Fatalf("更新 featured=false 失败: %v", result.Error)
		}
		totalUpdated += result.RowsAffected
		log.Printf("成功将 %d 个商家的featured字段更新为false", result.RowsAffected)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Fatalf("提交事务失败: %v", err)
	}

	log.Printf("总共更新了 %d 个商家的featured字段", totalUpdated)

	// 查询更新后的统计信息
	var featuredCount int64
	db.Model(&Merchant{}).Where("featured = ?", true).Count(&featuredCount)
	log.Printf("更新后共有 %d 个商家的featured字段为true", featuredCount)

	// 打印被更新为featured=true的商家
	if len(toUpdateFeatured) > 0 {
		var updatedFeaturedMerchants []Merchant
		db.Where("id IN ?", toUpdateFeatured).Find(&updatedFeaturedMerchants)

		fmt.Println("\n已更新为 featured=true 的商家:")
		for i, merchant := range updatedFeaturedMerchants {
			fmt.Printf("%3d. ID: %d, 名称: %s, 网站: %s\n", i+1, merchant.ID, merchant.Name, merchant.Website)
		}
	}

	// 打印被更新为featured=false的商家
	if len(toUpdateUnfeatured) > 0 {
		var updatedUnfeaturedMerchants []Merchant
		db.Where("id IN ?", toUpdateUnfeatured).Find(&updatedUnfeaturedMerchants)

		fmt.Println("\n已更新为 featured=false 的商家:")
		for i, merchant := range updatedUnfeaturedMerchants {
			fmt.Printf("%3d. ID: %d, 名称: %s, 网站: %s\n", i+1, merchant.ID, merchant.Name, merchant.Website)
		}
	}
}

// readFeaturedWebsites 读取 featured.txt 文件中的网站列表
func readFeaturedWebsites(filename string) ([]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("无法打开文件 %s: %v", filename, err)
	}
	defer file.Close()

	var websites []string
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" && !strings.HasPrefix(line, "#") { // 忽略空行和注释行
			websites = append(websites, line)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取文件时出错: %v", err)
	}

	return websites, nil
}

// isWebsiteInFeaturedList 检查商家网站是否在featured列表中
func isWebsiteInFeaturedList(merchantWebsite string, featuredWebsites []string) bool {
	if merchantWebsite == "" {
		return false
	}

	// 清理商家网站URL，移除协议和www前缀
	cleanMerchantWebsite := cleanWebsiteURL(merchantWebsite)

	for _, featuredWebsite := range featuredWebsites {
		cleanFeaturedWebsite := cleanWebsiteURL(featuredWebsite)

		// 精确匹配或者商家网站包含featured网站
		if cleanMerchantWebsite == cleanFeaturedWebsite ||
			strings.Contains(cleanMerchantWebsite, cleanFeaturedWebsite) {
			return true
		}
	}

	return false
}

// cleanWebsiteURL 清理网站URL，移除协议、www前缀和尾部斜杠
func cleanWebsiteURL(url string) string {
	// 移除协议
	url = strings.TrimPrefix(url, "https://")
	url = strings.TrimPrefix(url, "http://")

	// 移除www前缀
	url = strings.TrimPrefix(url, "www.")

	// 移除尾部斜杠
	url = strings.TrimSuffix(url, "/")

	// 转换为小写进行比较
	return strings.ToLower(url)
}

// getEnv 从环境变量获取值，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}
