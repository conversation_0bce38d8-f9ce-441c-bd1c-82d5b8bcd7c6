# 随机更新商家featured字段

这个脚本用于随机选择50个商家，将它们的featured字段更新为true。

## 使用方法

1. 确保您的环境中安装了Go (1.16+)
2. 如果需要，编辑`run.sh`文件，设置正确的数据库连接参数
3. 运行脚本：

```bash
./run.sh
```

或者

```bash
bash run.sh
```

## 脚本说明

- `update_featured.go`: 主要的Go程序，负责连接数据库并更新商家记录
- `run.sh`: Shell脚本，设置环境变量并运行Go程序

## 环境变量

以下环境变量可以在`run.sh`中配置：

- `DB_HOST`: 数据库主机名，默认为"localhost"
- `DB_PORT`: 数据库端口，默认为"5432"
- `DB_USER`: 数据库用户名，默认为"postgres"
- `DB_PASSWORD`: 数据库密码，默认为"postgres"
- `DB_NAME`: 数据库名称，默认为"bonusearned"
- `DB_SSLMODE`: SSL模式，默认为"disable"
- `DB_TIMEZONE`: 时区，默认为"Asia/Shanghai"

## 脚本功能

1. 连接到指定的PostgreSQL数据库
2. 统计数据库中所有商家和已经featured=true的商家数量
3. 获取所有featured=false的商家ID
4. 随机选择50个商家（如果不足50个，则选择所有可用的商家）
5. 使用事务安全地将这些商家的featured字段更新为true
6. 显示更新结果和被更新的商家列表 