# 商家数据导出工具

这是一个使用Golang编写的工具，用于从BonusEarned API获取所有商家信息，并将数据导出为CSV表格。

## 功能特点

- 自动分页获取所有商家数据
- 支持导出到CSV格式文件
- 包含完整的商家信息，如名称、返利值、分类等
- 使用UTF-8编码，支持中文及特殊字符
- 自动为文件名添加时间戳，避免覆盖旧数据

## 环境要求

- Go 1.16+
- 网络连接（需访问BonusEarned API）

## 安装依赖

这个脚本只使用Go标准库，不需要安装额外的依赖包：

```bash
# 进入脚本所在目录
cd /path/to/bonusearned/scripts/golang

# 初始化Go模块（如果尚未初始化）
go mod init bonusearned/tools
```

## 使用方法

### 运行脚本

```bash
# 进入脚本所在目录
cd /path/to/bonusearned/scripts/golang

# 构建并运行脚本
go run fetch_merchants.go
```

或者，你可以编译成可执行文件：

```bash
# 编译
go build -o fetch_merchants fetch_merchants.go

# 运行
./fetch_merchants
```

### 输出结果

脚本运行成功后，会在当前目录生成一个CSV文件，文件名格式为：`merchants-YYYYMMDD-HHMMSS.csv`

例如：`merchants-20230523-143015.csv`

## 数据格式说明

导出的CSV文件包含以下列：

| 列名        | 描述                         |
|------------|------------------------------|
| ID         | 商家唯一标识符                 |
| 名称        | 商家名称                     |
| 唯一名称    | 商家的唯一标识名称（用于URL）   |
| 商家代码    | 商家的代码                    |
| 网站        | 商家官方网站URL               |
| 返利链接    | 用于获取返利的追踪链接          |
| 返利值      | 返利百分比或金额               |
| 分类ID      | 商家所属分类的ID               |
| 分类名称    | 商家所属分类的名称             |
| 是否特色    | 是否为特色商家                 |
| 国家        | 商家所在国家                  |
| 支持的国家  | 支持的国家列表（JSON格式）      |
| 创建时间    | 记录创建时间                  |
| 更新时间    | 记录最后更新时间               |

## CSV文件用途

生成的CSV文件可以：

1. 直接用Excel、Numbers、Google Sheets等电子表格软件打开
2. 导入到数据库系统
3. 用于数据分析和处理
4. 作为数据备份存档

## 常见问题

### 无法连接到API

如果脚本无法连接到API，请检查：
1. 网络连接是否正常
2. API地址是否正确（当前使用: https://api.bonusearned.com/api/v1/merchants）
3. API是否需要认证（当前脚本不包含认证逻辑）

### CSV文件中文显示乱码

如果在某些软件中打开CSV文件显示中文乱码，请确保：
1. 以UTF-8编码打开文件
2. 如果使用Excel，请通过"数据"->"从文本"导入，并选择UTF-8编码

### 错误处理

脚本包含基本的错误处理逻辑，如果发生错误，会在控制台输出相关信息。 