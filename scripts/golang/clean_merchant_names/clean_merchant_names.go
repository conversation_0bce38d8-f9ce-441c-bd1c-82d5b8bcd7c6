package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// Merchant 商家模型
type Merchant struct {
	ID   int    `gorm:"column:id"`
	Name string `gorm:"column:name"`
}

// TableName 设置表名
func (Merchant) TableName() string {
	return "merchants"
}

func main() {
	// 构建数据库连接字符串
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		"**************",
		14614,
		"user_SgXD8fYdcn7mPs6kttjk",
		"password_BkGnEmjRrBEJYcv8xHsU",
		"bonusearned",
		"disable",
	)

	// 连接数据库
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 获取原生SQL连接用于关闭
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("获取数据库连接失败: %v", err)
	}
	defer sqlDB.Close()

	// 记录删除的商家信息
	logFile, err := os.OpenFile("deleted_merchants.log", os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		log.Fatalf("创建日志文件失败: %v", err)
	}
	defer logFile.Close()

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		log.Fatalf("开始事务失败: %v", tx.Error)
	}

	// 查询包含'/'的商家
	var merchants []Merchant
	if err := tx.Where("name LIKE ?", "%/%").Find(&merchants).Error; err != nil {
		tx.Rollback()
		log.Fatalf("查询商家失败: %v", err)
	}

	// 删除商家
	var deletedCount int
	for _, merchant := range merchants {
		// 删除商家
		if err := tx.Delete(&merchant).Error; err != nil {
			tx.Rollback()
			log.Fatalf("删除商家失败: %v", err)
		}

		// 记录删除的商家信息
		logEntry := fmt.Sprintf("[%s] 删除商家 ID: %d, 名称: %s\n",
			time.Now().Format("2006-01-02 15:04:05"),
			merchant.ID,
			merchant.Name,
		)
		if _, err := logFile.WriteString(logEntry); err != nil {
			tx.Rollback()
			log.Fatalf("写入日志失败: %v", err)
		}

		deletedCount++
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		log.Fatalf("提交事务失败: %v", err)
	}

	log.Printf("成功删除 %d 个包含'/'的商家", deletedCount)
}
