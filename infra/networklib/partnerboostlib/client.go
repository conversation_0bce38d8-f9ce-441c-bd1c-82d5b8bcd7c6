package partnerboostlib

import (
	"bonusearned/infra/ecode"
	"bonusearned/infra/networklib/partnerboostlib/partnerboostvo"
	"bonusearned/infra/utils/domainutil"
	"context"
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
)

func GetOrderTransactions(token string, status string, page int, limit int, startDay int, endDay int) (*partnerboostvo.GetOrderTransactionsResp, *ecode.Error) {
	ctx := context.Background()
	currentDate := time.Now()
	// 计算开始和结束日期
	beginDate := currentDate.AddDate(0, 0, startDay).Format("2006-01-02")
	endDate := currentDate.AddDate(0, 0, endDay).Format("2006-01-02")

	params := map[string]interface{}{
		"mod":        "medium",
		"op":         "transaction",
		"token":      string(token),
		"status":     string(status),
		"begin_date": string(beginDate),
		"end_date":   string(endDate),
		"page":       strconv.Itoa(page),
		"limit":      strconv.Itoa(limit),
	}

	headers := map[string]string{}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetOrderTransactions, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}
	getOrderTransactionsResp := new(partnerboostvo.GetOrderTransactionsResp)
	err = json.Unmarshal(resp, getOrderTransactionsResp)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}
	return getOrderTransactionsResp, nil
}

func BatchGetOrderTransactions(accountName string, token string, status string, page int, limit int, startDay int, endDay int) ([]map[string]interface{}, *ecode.Error) {
	allOrderList := make([]map[string]interface{}, 0)

	for {
		// 调用 GetOrderTransactions 函数
		getOrderTransactionsResp, err := GetOrderTransactions(token, status, page, limit, startDay, endDay)
		if err != nil {
			zap.L().Error("partnerboostlib BatchGetOrderTransactions GetOrderTransactions failed", zap.Error(err))
			return allOrderList, err
		}

		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSlice(accountName, getOrderTransactionsResp)...)
		// 如果获取到的订单长度为0，表示没有更多订单
		if page >= getOrderTransactionsResp.Data.TotalPage {
			break
		}
		// 更新 offset，以便获取下一批订单
		page += 1
	}

	return allOrderList, nil
}

func convertOrdersToSlice(accountName string, orders *partnerboostvo.GetOrderTransactionsResp) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(orders.Data.List))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Data.List {
		// 处理时间
		timestamp, err := strconv.ParseInt(order.OrderTime, 10, 64)
		if err != nil {
			fmt.Println("Error parsing timestamp:", err)
		}
		t := time.Unix(timestamp, 0)
		formattedDateTime := t.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := t.Format("2006-1-2")

		amountStr, err := decimal.NewFromString(order.SaleAmount)
		if err != nil {
			log.Fatalf("转换错误: %v", err)
			continue
		}
		orderAmount, _ := amountStr.Float64()

		saleCommStr, err := decimal.NewFromString(order.SaleComm)
		if err != nil {
			log.Fatalf("转换错误: %v", err)
			continue
		}
		saleComm, _ := saleCommStr.Float64()

		orderMap := map[string]interface{}{
			"id":               order.PartnerboostId,
			"account":          accountName,
			"order_id":         order.OrderId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_id":      order.BrandId,
			"merchant_name":    order.Mcid,
			"amount":           orderAmount,
			"commission":       saleComm,
			"order_status":     order.Status,
			"click_id":         order.Uid,
			"tag2":             order.Uid2,
			"ip":               "",
			"referer_url":      order.ClickRef,
			"customer_country": order.CustomerCountry,
			"currency":         "USD",
			"commission_usd":   saleComm,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}
		result = append(result, orderMap)
	}

	return result
}

func GetMerchants(token string, limit int, page int, relationship string) (*partnerboostvo.GetMerchantsResp, *ecode.Error) {
	ctx := context.Background()

	params := map[string]interface{}{
		"mod":          "medium",
		"op":           "monetization_api",
		"token":        token,
		"relationship": relationship,
		"limit":        strconv.Itoa(limit),
		"page":         strconv.Itoa(page),
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetMerchants, http.MethodGet, params, nil, nil)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	getMerchantsResp := new(partnerboostvo.GetMerchantsResp)
	err = json.Unmarshal(resp, getMerchantsResp)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}
	return getMerchantsResp, nil
}

// ParseCommissionRate 解析佣金比例数据，返回佣金值和类型
// ParseCommissionRate 解析佣金率字符串，返回完整的佣金信息
// 支持的格式：
// 1. 百分比格式："0.7%", "Up to 1.4%", "Revshare 70%"
// 2. 固定金额格式："USD 21", "$ 15", "$3.5"
// 3. 组合格式："0.6%/USD 16.8", "Up to 1.3%/EUR 4.55"
type CommissionInfo struct {
	Value      float64
	Type       string // "%", "USD", "EUR" 等
	IsUpTo     bool   // 是否包含 "up to"
	IsRevShare bool   // 是否是 Revshare 模式
	// 对于组合格式，存储替代性佣金信息
	AlternativeValue      float64
	AlternativeType       string
	AlternativeIsUpTo     bool
	AlternativeIsRevShare bool
}

func ParseCommissionRate(commissionStr string) (*CommissionInfo, error) {
	// 创建返回结构
	info := &CommissionInfo{}

	// 去除字符串两端的空白字符
	commissionStr = strings.TrimSpace(commissionStr)

	// 如果字符串为空，返回错误
	if commissionStr == "" {
		return nil, fmt.Errorf("empty commission string")
	}

	// 检查是否包含 "up to"
	if strings.Contains(strings.ToLower(commissionStr), "up to") {
		info.IsUpTo = true
		commissionStr = strings.TrimPrefix(strings.ToLower(commissionStr), "up to ")
	}

	// 检查是否是 Revshare 模式
	if strings.Contains(strings.ToLower(commissionStr), "revshare") {
		info.IsRevShare = true
		commissionStr = strings.TrimPrefix(strings.ToLower(commissionStr), "revshare ")
	}

	// 处理带有货币符号的金额格式（如 "$15", "€10", "£20"）
	currencySymbols := map[string]string{
		"$": "USD",
		"€": "EUR",
		"£": "GBP",
	}

	for symbol, currencyType := range currencySymbols {
		if strings.HasPrefix(commissionStr, symbol) {
			amountStr := strings.TrimPrefix(commissionStr, symbol)
			amountStr = strings.TrimSpace(amountStr)
			value, err := strconv.ParseFloat(amountStr, 64)
			if err != nil {
				return nil, fmt.Errorf("failed to parse amount with currency symbol: %v", err)
			}
			info.Value = value
			info.Type = currencyType
			return info, nil
		}
	}

	// 处理组合格式（如 "0.6%/USD 16.8" 或 "0.7%;Up to EUR 3.45"）
	if strings.Contains(commissionStr, "/") || strings.Contains(commissionStr, ";") {
		var parts []string
		if strings.Contains(commissionStr, "/") {
			parts = strings.Split(commissionStr, "/")
		} else {
			parts = strings.Split(commissionStr, ";")
		}
		if len(parts) == 2 {
			// 解析第一部分
			firstInfo, err := ParseCommissionRate(parts[0])
			if err != nil {
				return nil, err
			}
			// 解析第二部分
			secondInfo, err := ParseCommissionRate(parts[1])
			if err != nil {
				return nil, err
			}
			// 合并信息
			info.Value = firstInfo.Value
			info.Type = firstInfo.Type
			info.IsUpTo = firstInfo.IsUpTo
			info.IsRevShare = firstInfo.IsRevShare
			info.AlternativeValue = secondInfo.Value
			info.AlternativeType = secondInfo.Type
			info.AlternativeIsUpTo = secondInfo.IsUpTo
			info.AlternativeIsRevShare = secondInfo.IsRevShare
			return info, nil
		}
	}

	// 处理百分比格式
	if strings.Contains(commissionStr, "%") {
		// 移除百分号
		commissionStr = strings.TrimSuffix(commissionStr, "%")
		// 转换为float64
		value, err := strconv.ParseFloat(commissionStr, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse percentage value: %v", err)
		}
		info.Value = value
		info.Type = "%"
		return info, nil
	}

	// 处理固定金额格式（例如 "USD 21"）
	parts := strings.Fields(commissionStr)
	if len(parts) == 2 {
		// 获取货币类型和金额
		currencyType := strings.ToUpper(parts[0])
		amountStr := parts[1]

		// 转换金额为float64
		value, err := strconv.ParseFloat(amountStr, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse fixed amount: %v", err)
		}
		info.Value = value
		info.Type = currencyType
		return info, nil
	}

	// 如果格式不匹配任何已知格式，返回错误
	return nil, fmt.Errorf("invalid commission rate format")
}

func BatchGetMerchants(token string, limit int, relationship string, accountName string) ([]map[string]interface{}, *ecode.Error) {
	var createDataRows []map[string]interface{}
	uniqueMap := make(map[string]bool)
	page := 1
	for {
		// 调用 GetMerchants 函数
		getMerchantsResp, err := GetMerchants(token, limit, page, relationship)
		if err != nil {
			return createDataRows, err
		}
		// 将获取到的商家添加到总列表中
		for _, merchant := range getMerchantsResp.Data.List {
			uniqueKey := merchant.Mid
			if _, exists := uniqueMap[uniqueKey]; !exists && !strings.Contains(domainutil.ExtractDomain(merchant.SiteUrl), "amazon") {
				// 处理特殊数据
				country := strings.TrimSpace(strings.ToLower(merchant.Country))
				if len(country) <= 0 {
					country = "other"
				}
				rowData := map[string]interface{}{}
				rowData["id"] = merchant.Mid
				rowData["unique_name"] = strings.ToLower(fmt.Sprintf("%v%v_%v", merchant.Mcid, merchant.Mid, "pb"))
				rowData["name"] = merchant.MerchantName
				rowData["category"] = merchant.Categories
				rowData["country"] = country
				rowData["supported_countries"] = merchant.SupportRegion
				rowData["domain"] = domainutil.ExtractDomain(merchant.SiteUrl)
				rowData["original_domain"] = strings.TrimSpace(merchant.SiteUrl)
				rowData["cashback_rate"] = "0.3"
				rowData["affiliate_link"] = merchant.TrackingUrl
				rowData["description"] = ""
				rowData["logo"] = "https://logo.clearbit.com/" + domainutil.ExtractDomain(domainutil.ExtractDomain(merchant.SiteUrl))
				createDataRows = append(createDataRows, rowData)
				uniqueMap[uniqueKey] = true
			}
		}

		// 如果当前页是最后一页，退出循环
		if page >= getMerchantsResp.Data.TotalPage {
			break
		}

		// 更新页码，获取下一页
		page = page + 1
	}

	return createDataRows, nil
}
