package joingekkolib

import (
	"bonusearned/infra/ecode"
	"bonusearned/infra/networklib/joingekkolib/joingekkovo"
	"bonusearned/infra/utils/cashbackutils"
	"bonusearned/infra/utils/domainutil"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

var GeoList = []string{"us"}

func GetGeoList(ctx context.Context) ([]string, *ecode.Error) {
	headers := map[string]string{
		"accept":          "application/json, text/plain, */*",
		"accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
		"origin":          "https://joingekko.com",
		"referer":         "https://joingekko.com/",
	}

	resp, err := remoteInvokeWithUrl(ctx, geoMasterHost+geoMasterPath, http.MethodGet, nil, headers, nil)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	geoResp := new(joingekkovo.GeoListResponse)
	if err := json.Unmarshal(resp, geoResp); err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	if geoResp.Error != "" {
		return nil, ecode.New(ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	var geoList []string
	for _, item := range geoResp.Data {
		if item.Value != "all" { // Skip the "all" option
			geoList = append(geoList, item.Value)
		}
	}

	return geoList, nil
}

func generateAuthToken(secretKey string) string {
	// Get current time and subtract 8 hours
	//now := time.Now().Add(-8 * time.Hour) // +8 时区
	now := time.Now()
	// Format time as required
	formattedTime := now.Format("2006-01-02 15:04")

	// Generate MD5 hash
	hash := md5.New()
	hash.Write([]byte(formattedTime + secretKey))
	return strings.ToUpper(hex.EncodeToString(hash.Sum(nil)))
}

// GetMerchant fetches merchant data for a specific geo region
func GetMerchant(geo string, secretKey string, publisherKey string, propertyID string, authKey string) (*joingekkovo.MerchantFeedResponse, *ecode.Error) {
	ctx := context.Background()
	authToken := generateAuthToken(secretKey)

	params := map[string]interface{}{
		"publisherkey": publisherKey,
		"propertyid":   propertyID,
		"authkey":      authKey,
		"authtoken":    authToken,
		"geo":          geo,
	}

	headers := map[string]string{
		"Content-Type":     "application/json",
		"content-encoding": "gzip",
		"Accept":           "application/json",
	}

	// Add small delay to prevent rate limiting
	time.Sleep(10 * time.Millisecond)

	resp, err := remoteInvokeWithUrl(ctx, host+apiMerchantFeed, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	merchantResp := new(joingekkovo.MerchantFeedResponse)
	if err := json.Unmarshal(resp, merchantResp); err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	return merchantResp, nil
}

// BatchGetMerchants fetches and processes merchant data for all supported geos
func BatchGetMerchants(secretKey string, publisherKey string, propertyID string, authKey string) ([]map[string]interface{}, *ecode.Error) {
	var createDataRows []map[string]interface{}
	uniqueMap := make(map[string]bool)
	//geoList, err := GetGeoList(context.Background())
	geoList := GeoList
	for i, geo := range geoList {
		fmt.Println(geo, i, len(geoList))
		resp, err := GetMerchant(geo, secretKey, publisherKey, propertyID, authKey)

		if err != nil {
			continue
		}
		for _, row := range resp.Feed {
			uniqueKey := strconv.Itoa(row.MerchantId) + row.MerchantName
			if _, exists := uniqueMap[uniqueKey]; !exists {
				// Handle nil pointer values
				commissionRate := ""
				if row.CommissionRate != nil {
					commissionRate = *row.CommissionRate
				}
				// 处理特殊数据
				country := strings.TrimSpace(strings.ToLower(row.Geo))
				if len(country) <= 0 {
					country = "other"
				}
				// 处理分类
				category := "Other"
				if _, ok := CategoryNameMap[row.Category]; ok {
					category = CategoryNameMap[row.Category]
				}
				rowData := map[string]interface{}{}
				rowData["id"] = strconv.Itoa(row.MerchantId)
				rowData["unique_name"] = strings.ToLower(row.MerchantName + "x7f2" + strconv.Itoa(row.MerchantId))
				rowData["name"] = row.MerchantName
				rowData["category"] = category
				rowData["country"] = country
				rowData["supported_countries"] = row.Geo
				rowData["domain"] = domainutil.ExtractDomain(row.MerchantDomain)
				rowData["original_domain"] = strings.TrimSpace(row.MerchantDomain)
				commissionInfo, err1 := cashbackutils.ParseCommissionRate(commissionRate)
				if err1 != nil {
					// 识别失败，默认值，RevShare 80%
					rowData["cashback_type"] = "%"
					rowData["cashback_value"] = 100.00
					rowData["cashback_is_upto"] = false
					rowData["cashback_is_rev_share"] = true

					rowData["alternative_cashback_type"] = ""
					rowData["alternative_cashback_value"] = 0.00
					rowData["alternative_cashback_is_upto"] = false
					rowData["alternative_cashback_is_rev_share"] = false
				} else {
					rowData["cashback_type"] = commissionInfo.Type
					rowData["cashback_value"] = commissionInfo.Value
					rowData["cashback_is_upto"] = commissionInfo.IsUpTo
					rowData["cashback_is_rev_share"] = commissionInfo.IsRevShare

					rowData["alternative_cashback_type"] = commissionInfo.AlternativeType
					rowData["alternative_cashback_value"] = commissionInfo.AlternativeValue
					rowData["alternative_cashback_is_upto"] = commissionInfo.AlternativeIsUpTo
					rowData["alternative_cashback_is_rev_share"] = commissionInfo.AlternativeIsRevShare
				}
				rowData["parent_cashback_value"] = commissionRate
				rowData["affiliate_link"] = buildAffiliateUrl(publisherKey, propertyID, row.MerchantDomain, row.Geo, strconv.Itoa(row.MerchantId))
				rowData["description"] = ""
				rowData["logo"] = "https://logo.clearbit.com/" + domainutil.ExtractDomain(row.MerchantDomain)
				createDataRows = append(createDataRows, rowData)
				uniqueMap[uniqueKey] = true
			}
		}
	}

	return createDataRows, nil
}

func buildAffiliateUrl(publisherKey string, propertyid string, domain string, geo string, merchantid string) string {
	baseURL := affiliateHost + apiAffiliateLink
	// 预分配空间
	builder := strings.Builder{}
	builder.Grow(len(baseURL) + 100)
	builder.WriteString(baseURL)

	// 确定起始字符，只需判断一次
	separator := byte('&')
	if !strings.Contains(baseURL, "?") {
		separator = '?'
	}

	// 直接使用map查找，避免多次if-else
	builder.WriteByte(separator)
	builder.WriteString("publisherkey")
	builder.WriteByte('=')
	builder.WriteString(publisherKey)
	separator = '&'

	builder.WriteByte(separator)
	builder.WriteString("propertyid")
	builder.WriteByte('=')
	builder.WriteString(propertyid)
	separator = '&'

	builder.WriteByte(separator)
	builder.WriteString("url")
	builder.WriteByte('=')
	builder.WriteString(url.QueryEscape(domain))
	separator = '&'

	builder.WriteByte(separator)
	builder.WriteString("geo")
	builder.WriteByte('=')
	builder.WriteString(geo)
	separator = '&'

	builder.WriteByte(separator)
	builder.WriteString("merchantid")
	builder.WriteByte('=')
	builder.WriteString(merchantid)

	return builder.String()
}
