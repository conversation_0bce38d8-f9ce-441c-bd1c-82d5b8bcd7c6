package joingekkolib

import (
	"context"
	"fmt"
	"io"
	"io/ioutil"
	"math/rand"
	"net/http"
	"net/url"
	"strings"
	"time"
)

const (
	requestInterval = time.Second * 3
	overtimeTime    = time.Second * 180
)

const (
	affiliateHost    = "https://link.perfectlink.ai"
	apiAffiliateLink = "/deep-link"
)

const (
	geoMasterHost = "https://datalayer.joingekko.com"
	geoMasterPath = "/v1/geo-master"
)

const (
	host            = "https://publisher.joingekko.com"
	apiMerchantFeed = "/brand-feed"
)

var (
	browserVersions = []string{
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
	}
)

func init() {
	rand.Seed(time.Now().UnixNano())
}

// getRandomUserAgent returns a random user agent from the predefined list
func getRandomUserAgent() string {
	return browserVersions[rand.Intn(len(browserVersions))]
}

func remoteInvokeWithUrl(ctx context.Context, baseUrl string, method string, params map[string]interface{}, headers map[string]string, body io.Reader) (respBody []byte, err error) {
	// space 请求，延时300ms
	time.Sleep(requestInterval)

	client := &http.Client{
		Timeout: overtimeTime,
	}

	urlValues := url.Values{}
	for key, value := range params {
		urlValues.Add(key, value.(string))
	}
	fullUrl := baseUrl
	if strings.Contains(baseUrl, "?") {
		fullUrl = fmt.Sprintf("%s&%s", baseUrl, urlValues.Encode())
	} else {
		fullUrl = fmt.Sprintf("%s?%s", baseUrl, urlValues.Encode())
	}
	req, err := http.NewRequestWithContext(ctx, method, fullUrl, body)
	if err != nil {
		return nil, err
	}
	// 默认 header
	req.Header.Set("content-type", "application/json")
	req.Header.Set("accept", "application/json")

	// Set random user agent
	if headers == nil {
		headers = map[string]string{}
		headers["user-agent"] = getRandomUserAgent()
	}
	if headers["user-agent"] == "" {
		headers["user-agent"] = getRandomUserAgent()
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return respBody, err
	}
	defer resp.Body.Close()
	respBody, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return respBody, err
	}
	return respBody, nil
}

var CategoryNameMap = map[string]string{
	"Lifestyle":                            "Lifestyle",
	"Home and Garden":                      "Home and Garden",
	"Travel and Tourism":                   "Travel and Tourism",
	"Sports":                               "Sports",
	"Computers Electronics and Technology": "Computers Electronics and Technology",
	"Holidays & Occasions":                 "Holidays & Occasions",
	"eCommerce & Shopping":                 "eCommerce & Shopping",
	"Finance":                              "Finance",
	"Games":                                "Games",
	"Business and Consumer Services":       "Business and Consumer Services",
	"Media":                                "Media",
	"Household":                            "Household",
	"Heavy Industry and Engineering":       "Heavy Industry and Engineering",
	"Food and Drink":                       "Food and Drink",
	"Health":                               "Health",
	"Local Deals":                          "Local Deals",
	"Health & Beauty":                      "Health & Beauty",
	"Vehicles & Parts":                     "Vehicles & Parts",
	"Vehicles":                             "Vehicles",
	"Pets and Animals":                     "Pets and Animals",
	"Gambling":                             "Gambling",
	"Science and Education":                "Science and Education",
	"Reference Materials":                  "Reference Materials",
	"Computers & Accessories":              "Computers & Accessories",
	"Clothing & Apparel":                   "Clothing & Apparel",
	"Arts & Entertainment":                 "Arts & Entertainment",
	"Office":                               "Office",
	"Erotic":                               "Erotic",
	"Hardware":                             "Hardware",
	"Community and Society":                "Community and Society",
	"Consumer Electronics":                 "Consumer Electronics",
	"Babies & Kids":                        "Babies & Kids",
	"Hobbies and Leisure":                  "Hobbies and Leisure",
	"Tobacco":                              "Tobacco",
	"Sporting Goods":                       "Sporting Goods",
	"Consumer Services":                    "Consumer Services",
	"Travel":                               "Travel",
	"Jobs and Career":                      "Jobs and Career",
	"Toys & Games":                         "Toys & Games",
	"Financial":                            "Financial",
	"Food & Gourmet":                       "Food & Gourmet",
	"Animals & Pet Supplies":               "Animals & Pet Supplies",
}
