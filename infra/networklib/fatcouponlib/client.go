package fatcouponlib

import (
	"bonusearned/infra/ecode"
	"bonusearned/infra/networklib/fatcouponlib/fatcouponvo"
	"bonusearned/infra/utils/domainutil"
	"context"
	"encoding/json"
	"go.uber.org/zap"
	"net/http"
	"strconv"
)

func GetCoupons(page int) (*fatcouponvo.GetCouponListResponse, *ecode.Error) {
	ctx := context.Background()

	params := map[string]interface{}{
		"page": strconv.Itoa(page),
	}

	resp, err := remoteInvokeWithUrl(ctx, fatCouponHost+apiGetCouponList, http.MethodGet, params, nil, nil)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	getCouponListResponse := new(fatcouponvo.GetCouponListResponse)
	err = json.Unmarshal(resp, getCouponListResponse)
	if err != nil {
		zap.L().Error("fatcouponlib GetCoupons json.Unmarshal failed", zap.Error(err))
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}
	return getCouponListResponse, nil
}

func BatchGetCoupons() ([]map[string]interface{}, *ecode.Error) {
	var createDataRows []map[string]interface{}
	uniqueMap := make(map[string]bool)
	//geoList := GeoList
	for page := 1; page <= 529; page++ {
		resp, err := GetCoupons(page)
		if err != nil {
			continue
		}
		for _, row := range resp.PageProps.Deals.Data {
			code := ""
			if row.Code != nil {
				code = *row.Code
			}
			if len(code) <= 0 {
				continue
			}
			commissionRate := ""
			if row.CommissionRate != nil {
				commissionRate = *row.CommissionRate
			}
			uniqueKey := row.Id
			if _, exists := uniqueMap[uniqueKey]; !exists {
				rowData := map[string]interface{}{}
				rowData["id"] = row.Id
				rowData["title"] = row.Title
				rowData["description"] = row.Description
				rowData["link"] = domainutil.ExtractDomain(row.Link)
				rowData["code"] = code
				rowData["commission_rate"] = commissionRate
				rowData["type"] = row.Type
				rowData["store_name"] = row.Store.Name
				createDataRows = append(createDataRows, rowData)
				uniqueMap[uniqueKey] = true
			}
		}

		if resp == nil || len(resp.PageProps.Deals.Data) == 0 {
			break
		}
	}

	return createDataRows, nil
}
