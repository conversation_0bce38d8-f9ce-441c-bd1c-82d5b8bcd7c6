package fatcouponvo

import "time"

// GetCouponListResponse represents the response from FatCoupon API
type GetCouponListResponse struct {
	PageProps struct {
		Categories []struct {
			Status   int           `json:"status"`
			Type     int           `json:"type"`
			Slug     string        `json:"slug"`
			Priority int           `json:"priority"`
			Image    string        `json:"image"`
			Icon     string        `json:"icon"`
			Name     string        `json:"name"`
			ImageId  string        `json:"imageId"`
			Id       string        `json:"id"`
			Children []interface{} `json:"children"`
		} `json:"categories"`
		Deals struct {
			Count int `json:"count"`
			Page  int `json:"page"`
			Size  int `json:"size"`
			Total int `json:"total"`
			Data  []struct {
				Id             string      `json:"id"`
				StoreId        string      `json:"storeId"`
				Priority       int         `json:"priority"`
				Status         int         `json:"status"`
				Title          string      `json:"title"`
				Description    string      `json:"description"`
				StartedAt      *time.Time  `json:"startedAt"`
				EndedAt        *time.Time  `json:"endedAt"`
				Link           string      `json:"link"`
				Code           *string     `json:"code"`
				CommissionRate *string     `json:"commissionRate"`
				Image          interface{} `json:"image"`
				UpTo           bool        `json:"upTo"`
				Type           string      `json:"type"`
				CreatedAt      time.Time   `json:"createdAt"`
				UpdatedAt      time.Time   `json:"updatedAt"`
				Store          struct {
					Id                       *string `json:"id"`
					Name                     *string `json:"name"`
					Slug                     *string `json:"slug"`
					Image                    *string `json:"image"`
					AppOpenMode              *string `json:"appOpenMode"`
					CouponCount              *int    `json:"couponCount"`
					CommissionRate           string  `json:"commissionRate"`
					CommissionRateWithBonus  string  `json:"commissionRateWithBonus"`
					IsDisableFirstOrderBonus bool    `json:"isDisableFirstOrderBonus"`
				} `json:"store"`
				Categories []struct {
					Id   string `json:"id"`
					Name string `json:"name"`
					Slug string `json:"slug"`
				} `json:"categories"`
				ExpiresAt string `json:"expiresAt,omitempty"`
				Rate      string `json:"rate"`
			} `json:"data"`
		} `json:"deals"`
	} `json:"pageProps"`
	NSSP bool `json:"__N_SSP"`
}
