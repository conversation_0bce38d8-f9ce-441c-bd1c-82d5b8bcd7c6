package blueafflib

import (
	"bonusearned/infra/ecode"
	"bonusearned/infra/networklib/blueafflib/blueaffvo"
	"bonusearned/infra/utils/cashbackutils"
	"bonusearned/infra/utils/domainutil"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"net/http"
	"strconv"
	"strings"
	"time"
)

func GetOrderTransactionsByPortal(token string, status string, page int, limit int, startDay int, endDay int) (*blueaffvo.GetOrderTransactionsByPortalResp, *ecode.Error) {
	ctx := context.Background()
	currentDate := time.Now()
	// 计算开始和结束日期
	beginDate := currentDate.AddDate(0, 0, startDay).Format("2006-01-02")
	endDate := currentDate.AddDate(0, 0, endDay).Format("2006-01-02")

	params := map[string]interface{}{}

	headers := map[string]string{
		"cookie":               token,
		"accept":               "application/json, text/plain, */*",
		"accept-language":      "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
		"content-type":         "application/json",
		"priority":             "u=1, i",
		"sec-ch-ua":            "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"",
		"sec-ch-ua-mobile":     "?0",
		"sec-ch-ua-platform":   "\"macOS\"",
		"sec-fetch-dest":       "empty",
		"sec-fetch-mode":       "cors",
		"sec-fetch-site":       "same-origin",
		"x-allow-visit-region": "c9",
		"Referer":              "https://publisher.blueaff.com/reports/conversions",
		"Referrer-Policy":      "strict-origin-when-cross-origin",
	}

	requestBody := map[string]interface{}{
		"order_type":   "All",
		"start_date":   beginDate,
		"end_date":     endDate,
		"current":      page,
		"current_page": page,
		"pageSize":     limit,
		"page_size":    limit,
	}
	// 将请求体编码为 JSON
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	resp, err := remoteInvokeWithUrl(ctx, hostByPortal+apiGetOrderTransactionsByPortal, http.MethodPost, params, headers, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}
	getOrderTransactionsResp := new(blueaffvo.GetOrderTransactionsByPortalResp)
	err = json.Unmarshal(resp, getOrderTransactionsResp)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}
	return getOrderTransactionsResp, nil
}

func BatchGetOrderTransactions(accountName string, token string, tokenEf string, status string, page int, limit int, startDay int, endDay int, exchangeRatesUsdMap map[string]float64) ([]map[string]interface{}, *ecode.Error) {
	allOrderList := make([]map[string]interface{}, 0)

	for {
		getOrderTransactionsByPortalResp, err := GetOrderTransactionsByPortal(token, status, page, limit, startDay, endDay)
		if err != nil {
			zap.L().Error("blueafflib GetOrderTransactionsByPortal GetOrderTransactionsByPortal failed", zap.Error(err))
			continue
		}
		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSliceByPortal(accountName, getOrderTransactionsByPortalResp)...)
		if page*getOrderTransactionsByPortalResp.Data.Paging.PageSize >= getOrderTransactionsByPortalResp.Data.Paging.Count {
			break
		}

		// 更新 offset，以便获取下一批订单
		page += 1
	}
	return allOrderList, nil
}

func convertOrdersToSliceByPortal(accountName string, orders *blueaffvo.GetOrderTransactionsByPortalResp) []map[string]interface{} {
	// publish 全部转换为了 美元，无需进行转换
	result := make([]map[string]interface{}, 0, len(orders.Data.Records))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Data.Records {
		// 处理时间
		t, err := time.Parse("2006-01-02 15:04:05", order.ConvTime)
		if err != nil {
			fmt.Println("Error parsing time:", err)
		}
		formattedDateTime := t.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := t.Format("2006-1-2")

		// Convert string to float64
		estRevenue, err := strconv.ParseFloat(order.EstRevenue, 64)
		if err != nil {
			fmt.Println("Error converting string to float:", err)
			continue
		}

		saleAmount, err := strconv.ParseFloat(order.SaleAmount, 64)
		if err != nil {
			fmt.Println("Error converting string to float:", err)
			saleAmount = 0
		}

		orderMap := map[string]interface{}{
			"id":               order.ConversionId,
			"account":          accountName,
			"order_id":         order.ConversionId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_id":      order.OfferId,
			"merchant_name":    order.OfferName,
			"amount":           saleAmount,
			"commission":       estRevenue,
			"order_status":     order.Status,
			"click_id":         order.Sub1,
			"tag2":             order.Sub2,
			"ip":               order.ConversionIp,
			"referer_url":      order.ClickId,
			"customer_country": order.Country,
			"currency":         order.Currency,
			"commission_usd":   estRevenue,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}
		result = append(result, orderMap)
	}

	return result
}

func GetMerchant(token string, limit int, page int) (*blueaffvo.GetMerchantsResp, *ecode.Error) {
	ctx := context.Background()

	params := map[string]interface{}{}

	headers := map[string]string{
		"x-allow-visit-region": "c9",
		"Content-Type":         "application/json",
		"cookie":               token,
	}

	requestBody := map[string]interface{}{
		"current":      page,
		"pageSize":     limit,
		"page_size":    limit,
		"offer_type":   "All",
		"channel":      []string{},
		"current_page": page,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	resp, err := remoteInvokeWithUrl(ctx, hostByPortal+apiGetMerchants, http.MethodPost, params, headers, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	merchantResp := new(blueaffvo.GetMerchantsResp)
	if err := json.Unmarshal(resp, merchantResp); err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	return merchantResp, nil
}

func BatchGetMerchants(token string) ([]map[string]interface{}, *ecode.Error) {
	var createDataRows []map[string]interface{}
	uniqueMap := make(map[string]bool)
	page := 1
	limit := 1000

	for {
		resp, err := GetMerchant(token, limit, page)
		if err != nil {
			fmt.Println("GetMerchants:", err)
			continue
		}

		for _, row := range resp.Data.Records {
			uniqueKey := row.Id + row.OfferName
			if _, exists := uniqueMap[uniqueKey]; !exists {
				//// 处理分类
				//if row.PrimaryRegion != "US" {
				//	continue
				//}
				if len(row.TrackingLink) <= 0 {
					continue
				}
				category := "Other"
				if _, ok := CategoryNameMap[row.Category]; ok {
					category = CategoryNameMap[row.Category]
				}
				rowData := map[string]interface{}{}
				rowData["id"] = row.Id
				rowData["unique_name"] = strings.ReplaceAll(strings.ToLower(row.OfferName+"b6a3"+row.Id), " ", "")
				rowData["name"] = row.OfferName
				rowData["category"] = category
				rowData["country"] = row.PrimaryRegion
				rowData["supported_countries"] = strings.Join(row.SupportedRegion, ",")
				rowData["domain"] = domainutil.ExtractDomain(row.Domain)
				rowData["original_domain"] = strings.TrimSpace(row.Domain)
				rowData["featured"] = false
				if len(row.Channel) > 0 {
					rowData["featured"] = true
				}
				commissionInfo, err := cashbackutils.ParseCommissionRate(row.Commission)
				if err != nil {
					commissionInfo, err = cashbackutils.ParseCommissionRate(row.AverageCommission + "%") // 备用方案
					if err != nil {
						continue
					}
				}
				rowData["cashback_type"] = commissionInfo.Type
				rowData["cashback_value"] = commissionInfo.Value
				rowData["cashback_is_upto"] = commissionInfo.IsUpTo
				rowData["cashback_is_rev_share"] = commissionInfo.IsRevShare

				rowData["alternative_cashback_type"] = commissionInfo.AlternativeType
				rowData["alternative_cashback_value"] = commissionInfo.AlternativeValue
				rowData["alternative_cashback_is_upto"] = commissionInfo.AlternativeIsUpTo
				rowData["alternative_cashback_is_rev_share"] = commissionInfo.AlternativeIsRevShare

				rowData["parent_cashback_value"] = "85%"
				rowData["affiliate_link"] = row.TrackingLink

				rowData["description"] = row.Description
				rowData["logo"] = "https://logo.clearbit.com/" + domainutil.ExtractDomain(row.Domain)
				createDataRows = append(createDataRows, rowData)
				uniqueMap[uniqueKey] = true
			}
		}

		// Check if we've reached the last page
		if page >= resp.Data.Paging.NumPages {
			break
		}
		fmt.Println(page, resp.Data.Paging.NumPages)
		page++
	}
	return createDataRows, nil
}
