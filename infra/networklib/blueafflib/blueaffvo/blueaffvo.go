package blueaffvo

//	type GetOrderTransactionsByPortalResp struct {
//		Code string `json:"code"`
//		Info string `json:"info"`
//		Data struct {
//			Summary struct {
//				TotalConversion int    `json:"total_conversion"`
//				TotalSaleAmount string `json:"total_sale_amount"`
//				TotalEstRevenue string `json:"total_est_revenue"`
//			} `json:"summary"`
//			Paging struct {
//				PageSize int `json:"page_size"`
//				Count    int `json:"count"`
//			} `json:"paging"`
//			Records []struct {
//				Id           string `json:"id"`
//				ConvTime     string `json:"conv_time"`
//				OfferId      string `json:"offer_id"`
//				OfferName    string `json:"offer_name"`
//				Status       string `json:"status"`
//				RejectedTime string `json:"rejected_time"`
//				ApprovedTime string `json:"approved_time"`
//				PaidTime     string `json:"paid_time"`
//				SaleAmount   string `json:"sale_amount"`
//				EstRevenue   string `json:"est_revenue"`
//				Currency     string `json:"currency"`
//				PartnerId    string `json:"partner_id"`
//				SessionIp    string `json:"session_ip"`
//				ClickId      string `json:"click_id"`
//				ConversionId string `json:"conversion_id"`
//				ConversionIp string `json:"conversion_ip"`
//				Country      string `json:"country"`
//				CouponCode   string `json:"coupon_code"`
//				SourceId     string `json:"source_id"`
//				Sub1         string `json:"sub1"`
//				Sub2         string `json:"sub2"`
//				Sub3         string `json:"sub3"`
//				Sub4         string `json:"sub4"`
//				Sub5         string `json:"sub5"`
//			} `json:"records"`
//		} `json:"data"`
//		RequestId string `json:"request_id"`
//	}
type GetOrderTransactionsByPortalResp struct {
	Code string `json:"code"`
	Info string `json:"info"`
	Data struct {
		Summary struct {
			TotalConversion int    `json:"total_conversion"`
			TotalSaleAmount string `json:"total_sale_amount"`
			TotalEstRevenue string `json:"total_est_revenue"`
		} `json:"summary"`
		Paging struct {
			PageSize int `json:"page_size"`
			Count    int `json:"count"`
		} `json:"paging"`
		Records []struct {
			Id           string `json:"id"`
			ConvTime     string `json:"conv_time"`
			OfferId      string `json:"offer_id"`
			OfferName    string `json:"offer_name"`
			Status       string `json:"status"`
			RejectedTime string `json:"rejected_time"`
			ApprovedTime string `json:"approved_time"`
			PaidTime     string `json:"paid_time"`
			SaleAmount   string `json:"sale_amount"`
			EstRevenue   string `json:"est_revenue"`
			Currency     string `json:"currency"`
			PartnerId    string `json:"partner_id"`
			SessionIp    string `json:"session_ip"`
			ClickId      string `json:"click_id"`
			ConversionId string `json:"conversion_id"`
			ConversionIp string `json:"conversion_ip"`
			Country      string `json:"country"`
			CouponCode   string `json:"coupon_code"`
			SourceId     string `json:"source_id"`
			Sub1         string `json:"sub1"`
			Sub2         string `json:"sub2"`
			Sub3         string `json:"sub3"`
			Sub4         string `json:"sub4"`
			Sub5         string `json:"sub5"`
		} `json:"records"`
	} `json:"data"`
	RequestId string `json:"request_id"`
}
type GetMerchantsResp struct {
	Code string `json:"code"`
	Info string `json:"info"`
	Data struct {
		Paging struct {
			PageSize int `json:"page_size"`
			Count    int `json:"count"`
			NumPages int `json:"num_pages"`
		} `json:"paging"`
		Records []struct {
			SortId                     int         `json:"sort_id"`
			Id                         string      `json:"id"`
			OfferId                    string      `json:"offer_id"`
			OfferName                  string      `json:"offer_name"`
			LogoUrl                    string      `json:"logo_url"`
			Domain                     string      `json:"domain"`
			Status                     string      `json:"status"`
			Category                   string      `json:"category"`
			PrimaryRegion              string      `json:"primary_region"`
			SupportedRegion            []string    `json:"supported_region"`
			Channel                    []string    `json:"channel"`
			Commission                 string      `json:"commission"`
			TrackingLink               string      `json:"tracking_link"`
			AttributionPeriod          interface{} `json:"attribution_period"`
			ValidationPeriod           interface{} `json:"validation_period"`
			AveragePaymentTime         interface{} `json:"average_payment_time"`
			AverageEpc                 string      `json:"average_epc"`
			AverageEpa                 string      `json:"average_epa"`
			AverageCvr                 string      `json:"average_cvr"`
			AverageCommission          string      `json:"average_commission"`
			CommissionTerms            string      `json:"commission_terms"`
			AllowedPromotionalMethods  []string    `json:"allowed_promotional_methods"`
			ForbiddenPromotionalMethod []string    `json:"forbidden_promotional_method"`
			Description                string      `json:"description"`
			PaymentMethod              string      `json:"payment_method"`
			CreateTime                 string      `json:"create_time"`
			UpdateTime                 string      `json:"update_time"`
		} `json:"records"`
	} `json:"data"`
	RequestId string `json:"request_id"`
}
