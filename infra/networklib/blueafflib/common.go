package blueafflib

import (
	"context"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"time"
)

const (
	hostByPortal                    = "https://publisher.blueaff.com"
	apiGetOrderTransactionsByPortal = "/api/publisher/report/conversions"

	apiGetMerchants = "/api/publisher/offer/list"
)

const (
	requestInterval = time.Second * 3
	overtimeTime    = time.Second * 180
)

func remoteInvokeWithUrl(ctx context.Context, baseUrl string, method string, params map[string]interface{}, headers map[string]string, body io.Reader) (respBody []byte, err error) {
	// space 请求，延时300ms
	time.Sleep(requestInterval)

	client := &http.Client{
		Timeout: overtimeTime,
	}

	urlValues := url.Values{}
	for key, value := range params {
		urlValues.Add(key, value.(string))
	}
	fullUrl := fmt.Sprintf("%s?%s", baseUrl, urlValues.Encode())
	req, err := http.NewRequestWithContext(ctx, method, fullUrl, body)
	if err != nil {
		return nil, err
	}
	// 默认 header
	req.Header.Set("content-type", "application/json")
	req.Header.Set("accept", "application/json")

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return respBody, err
	}
	defer resp.Body.Close()
	respBody, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return respBody, err
	}
	return respBody, nil
}

var CategoryNameMap = map[string]string{
	"Ecommerce":                              "Consumer Goods",
	"Consumer Electronics":                   "Home & Electronics",
	"Home Improvement":                       "Home & Electronics",
	"Pet Supplies":                           "Pets & Animals",
	"Sports":                                 "Sports & Fitness",
	"cpu,rec,tvl":                            "Home & Electronics", // cpu主导，rec和tvl次要
	"Computers/Electronics":                  "Home & Electronics",
	"Accessories":                            "Fashion & Apparel", // 假设为服饰配件
	"Home & Garden":                          "Home & Electronics",
	"Commerce/Classifieds":                   "Auctions & Classifieds",
	"Clothing":                               "Fashion & Apparel",
	"Health":                                 "Health & Beauty",
	"Apparel":                                "Fashion & Apparel",
	"cpu,car":                                "Home & Electronics", // cpu主导
	"Business":                               "Business & Industrial",
	"Food/Drink":                             "Food & Drink",
	"Games/Toys":                             "Toys & Games",
	"cpu,edu":                                "Home & Electronics", // cpu主导
	"Education":                              "Jobs & Education",
	"Automotive":                             "Travel & Transportation",
	"fud,gif":                                "Food & Drink", // fud（food）主导
	"Sports/Fitness":                         "Sports & Fitness",
	"art,edu":                                "Arts & Entertainment", // art主导
	"hom,cpu":                                "Home & Electronics",   // hom（home）主导
	"spf,rec":                                "Sports & Fitness",     // spf（sports/fitness）主导
	"rec,spf":                                "Hobbies & Leisure",    // rec（recreation）主导
	"Art/Music/Photography":                  "Arts & Entertainment",
	"green,hom":                              "Eco-Friendly Products", // green主导
	"Shopping Malls":                         "Mass Merchants & Department Stores",
	"domain,hosting":                         "Consumer Goods",
	"clo,acc":                                "Fashion & Apparel", // clo（clothing）主导
	"acc,":                                   "Fashion & Apparel", // acc作为配件归入服饰
	"spf,clo":                                "Sports & Fitness",  // spf（sports/fitness）主导
	"toy,gif":                                "Toys & Games",      // toy主导
	"Family":                                 "People & Society",
	"cpu,tvl":                                "Home & Electronics", // cpu主导
	"rec,toy":                                "Hobbies & Leisure",  // rec主导
	"Recreation":                             "Hobbies & Leisure",
	"Freebies, Free Stuff, Rewards Programs": "Consumer Goods",
	"Ebike":                                  "Sports & Fitness",
	"Sports & Outdoor":                       "Sports & Fitness",
	"Beauty":                                 "Health & Beauty",
	"Gifts":                                  "Gifts & Events",
	"Internet & Online":                      "Home & Electronics",
	"cpu,hom":                                "Home & Electronics", // cpu主导
	"Sports Equipment":                       "Sports & Fitness",
	"baby care":                              "People & Society",
	"hea,":                                   "Health & Beauty",
	"Wigs":                                   "Health & Beauty",
	"Travel":                                 "Travel & Transportation",
	"Art":                                    "Arts & Entertainment",
	"Health & Beauty":                        "Health & Beauty",
	"Utility":                                "Home & Electronics",
	"Gadgets":                                "Home & Electronics",
	"Photos & Print Services":                "Home & Electronics",
	"Nutrition":                              "Health & Beauty",
	"Erotic":                                 "Arts & Entertainment", // 归入娱乐媒体
	"Vape":                                   "Consumer Goods",
	"Baby & Toddler":                         "People & Society",
	"Software":                               "Home & Electronics",
	"FMCG":                                   "Consumer Goods",
	"Ticket":                                 "Arts & Entertainment",
	"Electronic Superstore":                  "Home & Electronics",
	"Childrenswear":                          "Fashion & Apparel",
	"Tourism & Attractions":                  "Travel & Transportation",
	"Weddings":                               "Gifts & Events",
	"Pets & Pet Care":                        "Pets & Animals",
	"Green (Eco friendly)":                   "Eco-Friendly Products",
	"Office Supplies":                        "Business & Industrial",
	"Nutra":                                  "Health & Beauty",
	"Sweepstakes":                            "Consumer Goods",
	"Department Stores":                      "Consumer Goods",
	"Weight Loss":                            "Health & Beauty",
	"Female Intimate Care":                   "Health & Beauty",
	"Personal Loan":                          "Consumer Goods",
	"Hair Loss":                              "Health & Beauty",
	"Prostate":                               "Health & Beauty",
	"Prenatal Care":                          "Health & Beauty",
	"Games":                                  "Toys & Games",
	"Electronic Accessories":                 "Home & Electronics",
	"Breast Enhancement":                     "Health & Beauty",
	"Fresh Breath":                           "Health & Beauty",
	"Joint Health":                           "Health & Beauty",
	"Fertility":                              "Health & Beauty",
	"Energy Drink":                           "Food & Drink",
	"Acne":                                   "Health & Beauty",
	"Blood Sugar":                            "Health & Beauty",
	"Male Enhancement":                       "Health & Beauty",
	"Menopause":                              "Health & Beauty",
	"Stress":                                 "Health & Beauty",
	"Immunity":                               "Health & Beauty",
	"Bodybuilding":                           "Sports & Fitness",
	"Cholesterol":                            "Health & Beauty",
	"Furniture & Soft Furnishings":           "Home & Electronics",
	"Adult":                                  "Arts & Entertainment",
	"Hotels & Accommodation":                 "Travel & Transportation",
	"Airport Parking & Transfers":            "Travel & Transportation",
	"Womenswear":                             "Fashion & Apparel",
	"Sportswear":                             "Fashion & Apparel",
	"Car":                                    "Travel & Transportation",
	"Gifts & Flowers":                        "Gifts & Events",
	"Photography":                            "Home & Electronics",
	"Books & Subscriptions":                  "Science & Reference",
	"Toys & Games":                           "Toys & Games",
	"Trip":                                   "Travel & Transportation",
	"Computers":                              "Home & Electronics",
	"Business Services (B2B)":                "Business & Industrial",
	"Audio Visual":                           "Home & Electronics",
	"Education, Training & Recruitment":      "Jobs & Education",
	"Nootropics":                             "Health & Beauty",
	"Stretch Marks":                          "Health & Beauty",
	"Colon Cleansing":                        "Health & Beauty",
	"Testosterone":                           "Health & Beauty",
	"Anti-Aging":                             "Health & Beauty",
	"Female Libido":                          "Health & Beauty",
	"Thyroid":                                "Health & Beauty",
	"Snoring":                                "Health & Beauty",
	"General":                                "Consumer Goods",
	"Varicose Veins":                         "Health & Beauty",
	"Jewellery":                              "Fashion & Apparel",
	"Web Hosting":                            "Home & Electronics",
	"Webmaster Tools":                        "Home & Electronics",
	"Software Downloads":                     "Home & Electronics",
	"Group Buying":                           "Consumer Goods",
	"Savings & Investments":                  "Consumer Goods",
	"Shoes":                                  "Fashion & Apparel",
	"DIY":                                    "Home & Electronics",
	"Clothing Accessories":                   "Fashion & Apparel",
	"Wine, Spirits & Tobacco":                "Food & Drink",
	"Utilities":                              "Home & Electronics",
	"Gambling & Competitions":                "Toys & Games",
	"Network Operators":                      "Home & Electronics",
	"Travel Agencies":                        "Travel & Transportation",
	"Insurance":                              "Consumer Goods",
	"baby care product":                      "People & Society",
	"edu,tvl":                                "Jobs & Education", // edu主导
	"Music & DVD":                            "Arts & Entertainment",
	"Mobile Broadband":                       "Home & Electronics",
	"Personal Banking":                       "Consumer Goods",
	"B2B Telecommunications Services":        "Business & Industrial",
	"Pharmaceuticals":                        "Health & Beauty",
	"Car Rental":                             "Travel & Transportation",
	"Menswear":                               "Fashion & Apparel",
	"Airlines":                               "Travel & Transportation",
	"Dating":                                 "People & Society",
	"Mobile Pay As You Go":                   "Home & Electronics",
	"Internet Service Provider":              "Home & Electronics",
	"Charities":                              "People & Society",
	"PC & Video Games":                       "Toys & Games",
	"Entertainment":                          "Arts & Entertainment",
	"Clothing & Accessories":                 "Fashion & Apparel",
	"Food & Drink":                           "Food & Drink",
	"Computer & Electronics":                 "Home & Electronics",
	"Auto":                                   "Travel & Transportation",
	"Computers Electronics and Technology":   "Home & Electronics",
	"Lifestyle":                              "People & Society",
	"Holidays & Occasions":                   "Travel & Transportation",
	"Home and Garden":                        "Home & Electronics",
	"Travel and Tourism":                     "Travel & Transportation",
	"Household":                              "Home & Electronics",
	"Gambling":                               "Toys & Games",
	"eCommerce & Shopping":                   "Consumer Goods",
	"Gift & Flowers":                         "Gifts & Events",
	"Arts & Entertainment":                   "Arts & Entertainment",
	"Tickets":                                "Arts & Entertainment",
	"Sports & Fitness":                       "Sports & Fitness",
	"Footwear":                               "Fashion & Apparel",
	"Retail - Sports & Fitness":              "Sports & Fitness",
	"Art/Photo/Music":                        "Arts & Entertainment",
	"Food & Drinks":                          "Food & Drink",
	"Games & Toys":                           "Toys & Games",
	"Services - Tickets":                     "Arts & Entertainment",
	"Clothing/Apparel":                       "Fashion & Apparel",
	"Property":                               "Real Estate",
	"Lead Gen":                               "Business & Industrial",
	"Uncategorized":                          "Others",
	"Home & Living":                          "Home & Electronics",
	"Shopping":                               "Consumer Goods",
	"Lingerie":                               "Fashion & Apparel",
	"Health & Fitness":                       "Health & Beauty",
	"Technology & Computing":                 "Home & Electronics",
	"Law, Gov't & Politics":                  "Others",
	"Hobbies & Interests":                    "Hobbies & Leisure",
	"Personal Finance":                       "Consumer Goods",
	"Style & Fashion":                        "Fashion & Apparel",
	"Family & Parenting":                     "People & Society",
	"Society":                                "People & Society",
	"Pets":                                   "Pets & Animals",
	"Careers":                                "Jobs & Education",
	"News":                                   "Science & Reference",
	"Real Estate":                            "Real Estate",
	"Entertainment Superstore":               "Arts & Entertainment",
	"Uncategorized-Sports":                   "Sports & Fitness",
	"Style & Fashion-Adult":                  "Fashion & Apparel",
	"Style & Fashion-Health & Fitness":       "Health & Beauty",
	"Religion and Spirituality":              "Science & Reference",
	"Science":                                "Science & Reference",
	"Technology & Computing-Shopping":        "Consumer Goods",
	"Financial Services":                     "Consumer Goods",
	"Loans":                                  "Consumer Goods",
	"Online Gaming":                          "Toys & Games",
	"Cruises/Ferries":                        "Travel & Transportation",
	"Digital TV & Video-on-Demand":           "Arts & Entertainment",
	"Hobbies & Collectibles":                 "Hobbies & Leisure",
	"White Goods":                            "Home & Electronics",
	"Mobile Contract":                        "Home & Electronics",
	"Mobile Downloads":                       "Home & Electronics",
	"hea,mal":                                "Health & Beauty",      // hea（health）主导
	"spf,fam":                                "Sports & Fitness",     // spf（sports/fitness）主导
	"hom,gif":                                "Home & Electronics",   // hom（home）主导
	"art,hom":                                "Arts & Entertainment", // art主导
	"Green":                                  "Eco-Friendly Products",
	"General Web Services":                   "Home & Electronics",
	"tvl,weddings":                           "Travel & Transportation", // tvl（travel）主导
	"Fashion":                                "Fashion & Apparel",
	"metaphysics":                            "Science & Reference",
	"POD Gift":                               "Gifts & Events",
	"hea,fud":                                "Health & Beauty",         // hea（health）主导
	"tvl,rec":                                "Travel & Transportation", // tvl（travel）主导
	"B2B Utility Services":                   "Business & Industrial",
	"Marketing":                              "Business & Industrial",
	"Credit Cards":                           "Consumer Goods",
	"Entertainment Downloads":                "Arts & Entertainment",
	"activewear":                             "Fashion & Apparel",
	"Miscellaneous":                          "Consumer Goods",
	"hom,fud":                                "Home & Electronics", // hom（home）主导
	"Military":                               "Others",
	"clo,hea":                                "Fashion & Apparel", // clo（clothing）主导
	"Accessories & Peripherals":              "Home & Electronics",
	"Tools":                                  "Home & Electronics",
	"Cable & Satellite Operators":            "Home & Electronics",
	"fud,gif,gourmet,hea":                    "Food & Drink", // fud（food）主导
	"Gourmet":                                "Food & Drink",
	"edu,car":                                "Jobs & Education", // edu主导
	"gif,fin":                                "Gifts & Events",   // gif（gifts）主导
	"Financial":                              "Consumer Goods",
	"cpu,fud":                                "Home & Electronics", // cpu主导
	"toy,fam":                                "Toys & Games",       // toy主导
	"cpu,art":                                "Home & Electronics", // cpu主导
	"fud,":                                   "Food & Drink",
	"Books/Media":                            "Science & Reference",
	"hom,hea":                                "Home & Electronics", // hom（home）主导
	"Career/Jobs/Employment":                 "Jobs & Education",
	"fud,hea":                                "Food & Drink", // fud（food）主导
	"Legal":                                  "Others",
	"clo,hom":                                "Fashion & Apparel", // clo（clothing）主导
	"Retail":                                 "Consumer Goods",
	"Other":                                  "Consumer Goods",
	"Retail - Fashion":                       "Fashion & Apparel",
	"car,acc":                                "Travel & Transportation", // car主导
	"Gaming and Lotto":                       "Toys & Games",
	"acc,gif":                                "Fashion & Apparel", // acc（accessories）主导
	"Office":                                 "Business & Industrial",
	"Coaches":                                "Travel & Transportation",
	"Trains":                                 "Travel & Transportation",
	"spf,hea":                                "Sports & Fitness", // spf（sports/fitness）主导
	"Web Services":                           "Home & Electronics",
	"Business & Career":                      "Business & Industrial",
	"3D Printer":                             "Home & Electronics",
	"Women's Activewear":                     "Fashion & Apparel",
	"Gaming":                                 "Toys & Games",
	"Mature/Adult":                           "Arts & Entertainment",
	"Home & Garden-Sports":                   "Home & Electronics",
	"Home & Garden-Style & Fashion":          "Home & Electronics",
	"Style & Fashion-Shopping":               "Fashion & Apparel",
	"Style & Fashion-Hobbies & Interests":    "Fashion & Apparel",
	"Travel-Business":                        "Travel & Transportation",
	"Home & Garden-Style & Fashion-Shopping": "Home & Electronics",
	"Home & Garden-Education-Food & Drink":   "Home & Electronics",
	"Style & Fashion-Health & Fitness-Shopping": "Health & Beauty",
	"Style & Fashion-Uncategorized-Society":     "Fashion & Apparel",
	"Society-Business-Sports":                   "People & Society",
	"Shopping-Food & Drink":                     "Consumer Goods",
	"Travel-Arts & Entertainment":               "Travel & Transportation",
	"Department Store":                          "Consumer Goods",
	"acc,dating":                                "Fashion & Apparel", // acc（accessories）主导
	"Recreation & Leisure":                      "Hobbies & Leisure",
}
