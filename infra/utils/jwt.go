package utils

import (
	"bonusearned/infra/ecode"
	"time"

	"github.com/golang-jwt/jwt"
)

// GenerateToken 生成JWT令牌
func GenerateToken(userID uint64, userCode string, email string, secret string, expiry time.Duration) (string, *ecode.Error) {
	claims := jwt.MapClaims{
		"user_id":   userID,
		"email":     email,
		"user_code": userCode,
		"exp":       time.Now().Add(expiry).Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, err := token.SignedString([]byte(secret))
	if err != nil {
		return "", ecode.ErrInternalServer
	}
	return signedToken, nil
}

// ParseToken 解析JWT令牌
func ParseToken(tokenString string, secret string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, jwt.ErrInvalidKey
}
