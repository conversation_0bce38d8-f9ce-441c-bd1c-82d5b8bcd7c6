package imageutil

import (
	"bytes"
	"crypto/md5"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gen2brain/webp"

	"bonusearned/infra/ecode"
)

const (
	// DefaultImageSize is the size of most default images (128x128)
	DefaultImageSize = 64
	// MaxDownloadSize is the maximum size of image to download (10MB)
	MaxDownloadSize = 10 * 1024 * 1024
	// DefaultTimeout is the default timeout for HTTP requests (increased to 30 seconds)
	DefaultTimeout = 30 * time.Second
	// DefaultQuality is the default quality for WebP conversion
	DefaultQuality = 80
	// MaxRetries is the maximum number of retries for downloading images
	MaxRetries = 3
	// RetryDelay is the delay between retries
	RetryDelay       = 2 * time.Second
	DefaultImageName = "default_logo.webp"
)

// DownloadImage downloads an image from a URL and returns the image data
func DownloadImage(url string) ([]byte, string, *ecode.Error) {
	if url == "" {
		return nil, "", ecode.New(ecode.ErrInvalidParameter.Code, "empty URL")
	}

	// Create HTTP client with increased timeout
	client := &http.Client{
		Timeout: DefaultTimeout,
	}

	// Implement retry logic
	var lastErr error
	for retry := 0; retry < MaxRetries; retry++ {
		// Add delay between retries (except for the first attempt)
		if retry > 0 {
			time.Sleep(RetryDelay * time.Duration(retry))
		}

		// Create request
		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			lastErr = err
			continue
		}

		// Add headers to mimic browser request
		req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
		req.Header.Set("accept-encoding", "gzip, deflate, br, zstd")
		req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
		req.Header.Set("Cache-Control", "no-cache")
		req.Header.Set("Pragma", "no-cache")
		req.Header.Set("sec-ch-ua", "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"")
		req.Header.Set("priority", "u=0, i")
		req.Header.Set("upgrade-insecure-requests", "1")

		// Send request
		resp, err := client.Do(req)
		if err != nil {
			lastErr = err
			continue
		}
		defer resp.Body.Close()
		// Check response status
		if resp.StatusCode != http.StatusOK {
			lastErr = fmt.Errorf("status code: %d", resp.StatusCode)
			continue
		}

		// Check content type
		contentType := resp.Header.Get("Content-Type")
		if !strings.HasPrefix(contentType, "image/") {
			lastErr = fmt.Errorf("content is not an image: %s", contentType)
			continue
		}

		// Read response body with size limit
		limitReader := io.LimitReader(resp.Body, MaxDownloadSize)
		imageData, err := io.ReadAll(limitReader)
		if err != nil {
			lastErr = err
			continue
		}

		// If we hit the limit, it's too large
		if len(imageData) >= MaxDownloadSize {
			lastErr = fmt.Errorf("image too large, exceeds %d bytes", MaxDownloadSize)
			continue
		}

		// If the image data is empty or too small, retry
		if len(imageData) < 100 {
			lastErr = fmt.Errorf("image data too small: %d bytes", len(imageData))
			continue
		}

		return imageData, contentType, nil
	}

	// All retries failed
	return nil, "", ecode.Wrap(lastErr, ecode.ErrInternalServer.Code, "failed to download image after retries")
}

// ConvertToWebP converts image data to WebP format

func ConvertToWebP(imageData []byte, contentType string, quality int) ([]byte, *ecode.Error) {
	if quality <= 0 || quality > 100 {
		quality = DefaultQuality
	}

	// Decode image based on content type
	var img image.Image
	var err error

	reader := bytes.NewReader(imageData)
	switch {
	case strings.Contains(contentType, "jpeg") || strings.Contains(contentType, "jpg"):
		img, err = jpeg.Decode(reader)
	case strings.Contains(contentType, "png"):
		img, err = png.Decode(reader)
	case strings.Contains(contentType, "webp"):
		// Already WebP, just return the data
		return imageData, nil
	default:
		// Try to decode as generic image
		img, _, err = image.Decode(reader)
	}

	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, "failed to decode image")
	}

	// Convert to WebP
	var buf bytes.Buffer
	if err := webp.Encode(&buf, img, webp.Options{
		Lossless: false,
		Quality:  quality,
	}); err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, "failed to encode WebP")
	}

	return buf.Bytes(), nil
}

// IsDefaultImage checks if the image is likely a default image (common size for placeholders)
func IsDefaultImage(imageData []byte) (bool, *ecode.Error) {
	reader := bytes.NewReader(imageData)
	img, _, err := image.Decode(reader)
	if err != nil {
		return false, ecode.Wrap(err, ecode.ErrInternalServer.Code, "failed to decode image")
	}
	bounds := img.Bounds()
	width := bounds.Max.X - bounds.Min.X
	height := bounds.Max.Y - bounds.Min.Y

	// Check if it's a common default image size
	if width == DefaultImageSize && height == DefaultImageSize {
		return true, nil
	}

	return false, nil
}

// SaveImageToFile saves image data to a file
func SaveImageToFile(imageData []byte, filePath string) *ecode.Error {
	// Create directory if it doesn't exist
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return ecode.Wrap(err, ecode.ErrInternalServer.Code, "failed to create directory")
	}

	// Write file
	if err := os.WriteFile(filePath, imageData, 0644); err != nil {
		return ecode.Wrap(err, ecode.ErrInternalServer.Code, "failed to write file")
	}

	return nil
}

// GenerateImageFileName generates a unique filename for an image based on its content
func GenerateImageFileName(imageData []byte, merchantCode string) string {
	// Generate MD5 hash of image data
	hash := md5.Sum(imageData)
	hashStr := fmt.Sprintf("%x", hash)[:8] // Use first 8 chars of hash

	// Create filename with merchant code and hash
	return fmt.Sprintf("%s_%s.webp", merchantCode, hashStr)
}

//// ProcessMerchantLogo downloads, converts, and saves a merchant logo
//// If the logo cannot be downloaded or is a default image, it uses the default logo instead
//func ProcessMerchantLogo(logoURL, merchantCode, tempDir string, merchantName ...string) (string, []byte, *ecode.Error) {
//	// Download image
//	imageData, contentType, err := DownloadImage(logoURL)
//
//	// Handle download errors (including 404) by using the default logo
//	if err != nil {
//		return DefaultImageName, []byte{}, nil
//	}
//
//	// Check if it's a default image
//	//isDefault, err := IsDefaultImage(imageData)
//	//if err == nil && isDefault {
//	//	fmt.Printf("Detected default image for %s. Using default logo instead.\n", merchantCode)
//	//	return DefaultImageName, []byte{}, nil
//	//}
//
//	// If we got here, we have a valid image, so process it normally
//	// Convert to WebP
//	webpData, err := ConvertToWebP(imageData, contentType, DefaultQuality)
//	if err != nil {
//		return "", nil, ecode.Wrap(err.Err, ecode.ErrInternalServer.Code, "failed to convert to WebP")
//	}
//
//	// Generate filename
//	filename := GenerateImageFileName(webpData, merchantCode)
//
//	// Save to temp directory
//	filePath := filepath.Join(tempDir, filename)
//	if err := SaveImageToFile(webpData, filePath); err != nil {
//		return "", nil, ecode.Wrap(err.Err, ecode.ErrInternalServer.Code, "failed to save image")
//	}
//
//	return filename, webpData, nil
//}
