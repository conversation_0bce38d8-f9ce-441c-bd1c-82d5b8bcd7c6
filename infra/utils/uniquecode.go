package utils

import (
	"fmt"
	"github.com/OneOfOne/xxhash"
	"github.com/google/uuid"
	"time"
)

// GenerateUniqueCodeWithError 生成唯一编码，针对高并发场景优化，带错误处理
func GenerateUniqueCodeWithError(uniqueCode string) (string, uuid.UUID, error) {
	// 组合多个唯一元素
	now := time.Now()
	timestamp := now.UnixNano()
	clickId := uuid.New()

	// 使用更快的哈希函数 xxHash
	hasher := xxhash.New64()

	// 拼接用户编码、时间戳和部分点击ID以确保唯一性
	baseData := fmt.Sprintf("%s%d%s", uniqueCode, timestamp, clickId.String()[:8])

	_, err := hasher.WriteString(baseData)
	if err != nil {
		return "", uuid.Nil, fmt.Errorf("failed to write to hasher: %w", err)
	}

	hash := hasher.Sum64()

	// 定义字符集用于生成字母数字UID
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
	charsetLen := len(charset)

	uid := make([]byte, 16)
	for i := 0; i < 16; i++ {
		uid[i] = charset[byte(hash%uint64(charsetLen))]
		hash >>= 6
	}

	return string(uid), clickId, nil
}

// GenerateUniqueCode 生成唯一编码，针对高并发场景优化
func GenerateUniqueCode(uniqueCode string) (string, uuid.UUID) {
	uid, clickId, _ := GenerateUniqueCodeWithError(uniqueCode)
	return uid, clickId
}

// GenerateUserCode 生成用户编码
func GenerateUserCode() string {
	// 生成一个UUID
	id := uuid.New()
	
	// 将UUID转换为字符串并取前8位
	return id.String()[:8]
}

// GenerateMerchantCode 生成商家编码
func GenerateMerchantCode() string {
	// 生成一个UUID
	id := uuid.New()
	
	// 将UUID转换为字符串并取前12位
	return "M" + id.String()[:11]
}
