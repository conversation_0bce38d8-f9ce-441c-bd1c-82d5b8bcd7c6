package trackurlutil

import (
	"strings"
)

func GenTrackURL(trackURL, userCode string) string {
	if trackURL == "" {
		return "/"
	}

	if userCode != "" {
		if strings.Contains(trackURL, "{user_code}") {
			// 正常情况下的url
			return strings.ReplaceAll(trackURL, "{user_code}", userCode)
		} else {
			// 不正常的url 特殊处理
			parts := strings.Split(trackURL, "/t/")
			if len(parts) != 2 {
				// 如果无法正确分隔，直接替换 URL 返回
				return strings.ReplaceAll(trackURL, "/{user_code}", "")
			}
			// 分隔 "/t/" 后的路径部分
			return parts[0] + "/t/" + strings.Split(parts[1], "/")[0] + "/" + userCode
		}
	}

	// user code == ""
	newTrackUrl := strings.ReplaceAll(trackURL, "/{user_code}", "")
	parts := strings.Split(newTrackUrl, "/t/")
	if len(parts) != 2 {
		// 如果无法正确分隔，直接替换 URL
		return strings.ReplaceAll(newTrackUrl, "/{user_code}", "")
	}
	// 分隔 "/t/" 后的路径部分
	trailingPath := strings.Split(parts[1], "/")[1:]
	// 如果 trailingPath 有内容但 userCode 为空，返回 baseURL
	if len(trailingPath) > 0 {
		return parts[0] + "/t/" + strings.Split(parts[1], "/")[0]
	}
	return newTrackUrl
}

func GenBaseTrackURL(trackHost string, merchantCode string) string {
	return trackHost + "/t/" + merchantCode + "/{user_code}"
}
