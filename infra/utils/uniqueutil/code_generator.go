package uniqueutil

import (
	"crypto/sha256"
	"encoding/binary"
	"github.com/speps/go-hashids/v2"
	"time"
)

var (
	// 初始化 hashids
	hd *hashids.HashIDData
)

func init() {
	hd = hashids.NewData()
	// 设置盐值，这个值应该是固定的，可以从配置中读取
	hd.Salt = "your-unique"
	// 设置固定长度为7
	hd.MinLength = 7
	// 设置字母表，去掉容易混淆的字符
	hd.Alphabet = "23456789ABCDEFGHJKMNPQRSTUVWXYZ"
}

// GenerateCode 生成唯一的7位编码
func GenerateCode(input string) string {
	// 计算输入的哈希值
	hash := sha256.Sum256([]byte(input))

	// 将哈希值分成多个部分
	// 使用前16字节，每4字节一组，得到4个数字
	nums := make([]int64, 4)
	for i := 0; i < 4; i++ {
		nums[i] = int64(binary.BigEndian.Uint32(hash[i*4 : (i+1)*4]))
	}

	// 添加时间戳信息作为第5个数字
	nums = append(nums, time.Now().UnixNano())

	// 创建新的 hashids 实例
	h, err := hashids.NewWithData(hd)
	if err != nil {
		return fallbackGenerateCode(input)
	}

	// 编码所有数字
	encoded, err := h.EncodeInt64(nums)
	if err != nil {
		return fallbackGenerateCode(input)
	}

	if len(encoded) > 7 {
		return fallbackGenerateCode(input)
	}

	// hashids 的 MinLength 设置确保了输出一定是7位
	return encoded
}

// fallbackGenerateCode 备用的生成方法，确保生成7位编码
func fallbackGenerateCode(input string) string {
	// 组合输入和时间戳
	data := input + time.Now().Format(time.RFC3339Nano)
	hash := sha256.Sum256([]byte(data))

	// 生成7位编码
	code := make([]byte, 7)
	charset := "23456789ABCDEFGHJKMNPQRSTUVWXYZ"
	charsetLen := len(charset)

	// 使用不同的哈希字节组合生成每一位
	for i := 0; i < 7; i++ {
		// 使用4个字节生成一个索引，确保分布更均匀
		index := int(binary.BigEndian.Uint32(hash[i*4:(i+1)*4])) % charsetLen
		code[i] = charset[index]
	}

	return string(code)
}
