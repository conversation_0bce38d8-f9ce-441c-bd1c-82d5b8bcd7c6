package uniqueutil

import (
	"crypto/sha256"
	"encoding/binary"
	"fmt"
	"hash/crc32"
	"os"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
)

const (
	// nodeIDBits 用于节点ID的位数
	nodeIDBits = 10
	// sequenceBits 用于序列号的位数
	sequenceBits = 12

	// maxNodeID 最大节点ID
	maxNodeID = -1 ^ (-1 << nodeIDBits)
	// maxSequence 最大序列号
	maxSequence = -1 ^ (-1 << sequenceBits)

	// timeLeft 时间戳左移位数
	timeLeft = nodeIDBits + sequenceBits
	// nodeIDLeft 节点ID左移位数
	nodeIDLeft = sequenceBits
)

var (
	// 上次生成ID的时间戳
	lastTimestamp int64 = -1
	// 当前序列号
	sequence int64 = 0
	// 节点ID
	nodeID int64
	// 互斥锁，用于保护lastTimestamp和sequence
	mu sync.Mutex
	// 用于确保Init只被调用一次的标志
	initialized uint32
)

// Init 初始化节点ID，必须在使用前调用
func Init(nID int64) error {
	if !atomic.CompareAndSwapUint32(&initialized, 0, 1) {
		return fmt.Errorf("already initialized")
	}

	if nID < 0 || nID > maxNodeID {
		return fmt.Errorf("node ID must be between 0 and %d", maxNodeID)
	}
	nodeID = nID
	return nil
}

// base62Characters 用于Base62编码
const base62Characters = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"

// toBase62 将数字转换为Base62字符串
func toBase62(num uint64) string {
	if num == 0 {
		return string(base62Characters[0])
	}

	var result []byte
	for num > 0 {
		result = append(result, base62Characters[num%62])
		num /= 62
	}

	// 反转结果
	for i, j := 0, len(result)-1; i < j; i, j = i+1, j-1 {
		result[i], result[j] = result[j], result[i]
	}

	return string(result)
}

// fromBase62 将Base62字符串转换回数字
func fromBase62(str string) (uint64, error) {
	var result uint64
	for _, char := range str {
		result *= 62
		switch {
		case char >= '0' && char <= '9':
			result += uint64(char - '0')
		case char >= 'A' && char <= 'Z':
			result += uint64(char-'A') + 10
		case char >= 'a' && char <= 'z':
			result += uint64(char-'a') + 36
		default:
			return 0, fmt.Errorf("invalid base62 character: %c", char)
		}
	}
	return result, nil
}

// calculateChecksum 计算校验和
func calculateChecksum(data []byte) uint32 {
	return crc32.ChecksumIEEE(data)
}

// GenerateConversionID 生成一个唯一的转化ID
// 格式: prefix-timestamp-nodeid-sequence-uuid-checksum
func GenerateConversionID() (string, error) {
	if atomic.LoadUint32(&initialized) == 0 {
		hostname, err := os.Hostname()
		if err != nil {
			return "", fmt.Errorf("failed to get hostname: %v", err)
		}
		// 使用主机名的哈希作为默认节点ID
		hash := sha256.Sum256([]byte(hostname))
		defaultNodeID := int64(binary.BigEndian.Uint64(hash[:8]) % uint64(maxNodeID))
		if err := Init(defaultNodeID); err != nil {
			return "", err
		}
	}

	mu.Lock()
	defer mu.Unlock()

	// 获取当前时间戳（使用纳秒）
	timestamp := time.Now().UnixNano()

	// 如果是同一纳秒内，增加序列号
	if timestamp == lastTimestamp {
		sequence = (sequence + 1) & maxSequence
		// 如果序列号超出范围，等待下一纳秒
		if sequence == 0 {
			for timestamp <= lastTimestamp {
				timestamp = time.Now().UnixNano()
			}
		}
	} else {
		sequence = 0
	}

	lastTimestamp = timestamp

	// 生成UUID
	uid := uuid.New()

	// 组合ID各个部分
	idBytes := make([]byte, 32)
	binary.BigEndian.PutUint64(idBytes[0:8], uint64(timestamp))
	binary.BigEndian.PutUint64(idBytes[8:16], uint64(nodeID))
	binary.BigEndian.PutUint64(idBytes[16:24], uint64(sequence))
	copy(idBytes[24:], uid[:])

	// 计算校验和
	checksum := calculateChecksum(idBytes)

	// 使用Base62编码组合最终的ID
	id := fmt.Sprintf("cv-%s-%s-%s-%s-%s",
		toBase62(uint64(timestamp)),
		toBase62(uint64(nodeID)),
		toBase62(uint64(sequence)),
		uid.String(),
		toBase62(uint64(checksum)))

	return id, nil
}

// ValidateConversionID 验证转化ID的有效性
func ValidateConversionID(id string) bool {
	// 检查基本格式
	var timestampStr, nodeIDStr, sequenceStr, uuidStr, checksumStr string
	_, err := fmt.Sscanf(id, "cv-%s-%s-%s-%s-%s",
		&timestampStr, &nodeIDStr, &sequenceStr, &uuidStr, &checksumStr)
	if err != nil {
		return false
	}

	// 验证并解析各个部分
	timestamp, err := fromBase62(timestampStr)
	if err != nil {
		return false
	}

	nodeIDNum, err := fromBase62(nodeIDStr)
	if err != nil || nodeIDNum > uint64(maxNodeID) {
		return false
	}

	sequence, err := fromBase62(sequenceStr)
	if err != nil || sequence > uint64(maxSequence) {
		return false
	}

	// 验证UUID部分
	uid, err := uuid.Parse(uuidStr)
	if err != nil {
		return false
	}

	providedChecksum, err := fromBase62(checksumStr)
	if err != nil {
		return false
	}

	// 重建原始数据
	idBytes := make([]byte, 32)
	binary.BigEndian.PutUint64(idBytes[0:8], timestamp)
	binary.BigEndian.PutUint64(idBytes[8:16], nodeIDNum)
	binary.BigEndian.PutUint64(idBytes[16:24], sequence)
	copy(idBytes[24:], uid[:])

	// 计算校验和并验证
	calculatedChecksum := calculateChecksum(idBytes)
	if uint64(calculatedChecksum) != providedChecksum {
		return false
	}

	// 验证时间戳的合理性
	// 确保时间戳不是未来的时间（允许1小时的时钟偏差）
	currentTime := time.Now().UnixNano()
	if int64(timestamp) > currentTime+time.Hour.Nanoseconds() {
		return false
	}

	// 确保时间戳不是太过久远的时间（例如不超过10年）
	tenYears := time.Hour * 24 * 365 * 10
	if int64(timestamp) < currentTime-tenYears.Nanoseconds() {
		return false
	}

	return true
}

// ParseConversionID 解析转化ID，返回其组成部分
// 如果ID无效，返回错误
func ParseConversionID(id string) (timestamp time.Time, nodeID int64, sequence int64, uid uuid.UUID, err error) {
	if !ValidateConversionID(id) {
		return time.Time{}, 0, 0, uuid.UUID{}, fmt.Errorf("invalid conversion ID")
	}

	var timestampStr, nodeIDStr, sequenceStr, uuidStr, checksumStr string
	_, err = fmt.Sscanf(id, "cv-%s-%s-%s-%s-%s",
		&timestampStr, &nodeIDStr, &sequenceStr, &uuidStr, &checksumStr)
	if err != nil {
		return time.Time{}, 0, 0, uuid.UUID{}, fmt.Errorf("failed to parse conversion ID format")
	}

	timestampNum, _ := fromBase62(timestampStr)
	nodeIDNum, _ := fromBase62(nodeIDStr)
	sequenceNum, _ := fromBase62(sequenceStr)
	uid, _ = uuid.Parse(uuidStr)

	timestamp = time.Unix(0, int64(timestampNum))
	return timestamp, int64(nodeIDNum), int64(sequenceNum), uid, nil
}
