package uniqueutil

import (
	"bonusearned/infra/ecode"
	"encoding/base64"
	"fmt"
	"math/rand"
	"strings"
	"time"
)

var (
	encoding = base64.RawURLEncoding // URL安全的base64编码，没有填充字符
	rnd      = rand.New(rand.NewSource(time.Now().UnixNano()))
)

// 生成1位随机数
func generateRandom() byte {
	return byte(rnd.Intn(32)) // 使用5位的随机数，可以用一个字节表示
}

// 验证输入长度
func validateInput(merchantCode, userCode, sub1 string) *ecode.Error {
	if merchantCode == "" {
		return ecode.New(ecode.ErrInvalidParameter.Code, "merchantCode is required")
	}
	if len(merchantCode) > 7 {
		return ecode.New(ecode.ErrInvalidParameter.Code, "merchantCode too long (max 7 chars)")
	}
	if len(userCode) > 7 {
		return ecode.New(ecode.ErrInvalidParameter.Code, "userCode too long (max 7 chars)")
	}
	if len(sub1) > 5 {
		return ecode.New(ecode.ErrInvalidParameter.Code, "sub1 too long (max 5 chars)")
	}
	return nil
}

// EncodeClick 编码函数：将商家码、用户码等信息编码成token
func EncodeClick(merchantCode, userCode, sub1 string, key []byte) (string, *ecode.Error) {
	// 1. 验证输入
	if err := validateInput(merchantCode, userCode, sub1); err != nil {
		return "", err
	}

	// 2. 准备数据
	ts := time.Now().UnixMicro()
	random := generateRandom()
	flags := byte(0)
	if userCode != "" {
		flags |= 1
	}
	if sub1 != "" {
		flags |= 2
	}

	// 3. 构建二进制数据
	data := make([]byte, 28) // 8(时间戳) + 1(标志位) + 7(商家码) + 7(用户码) + 5(sub1)

	// 时间戳 (8字节)
	for i := 0; i < 8; i++ {
		data[i] = byte(ts >> (i * 8))
	}

	// 标志位和随机数 (1字节)
	data[8] = (flags << 5) | random // 高3位是标志位，低5位是随机数

	// 商家码 (最多7字节)
	copy(data[9:16], []byte(fmt.Sprintf("%-7s", merchantCode)))

	// 用户码 (最多7字节)
	copy(data[16:23], []byte(fmt.Sprintf("%-7s", userCode)))

	// sub1 (最多5字节)
	copy(data[23:28], []byte(fmt.Sprintf("%-5s", sub1)))

	// 4. 加密
	for i := 0; i < len(data); i++ {
		data[i] ^= key[i%len(key)]
	}

	// 5. Base64编码（不带填充字符）
	encoded := encoding.EncodeToString(data)

	return encoded, nil
}

// DecodeClick 解码函数：从token解码出商家码、用户码等信息
func DecodeClick(token string, key []byte) (merchantCode, userCode, sub1 string, err *ecode.Error) {
	// 1. Base64解码
	data, errc := encoding.DecodeString(token)
	if errc != nil {
		return "", "", "", ecode.New(ecode.ErrInternalServer.Code, "invalid click id format")
	}

	// 2. 解密
	for i := 0; i < len(data); i++ {
		data[i] ^= key[i%len(key)]
	}
	if len(data) <= 0 {
		return "", "", "", ecode.New(ecode.ErrInternalServer.Code, "invalid click id format")
	}
	// 3. 解析时间戳
	var ts int64
	for i := 0; i < 8; i++ {
		ts |= int64(data[i]) << (i * 8)
	}

	// 4. 解析标志位和随机数
	flags := data[8] >> 5

	// 5. 解析各个字段
	if len(data) <= 28 {
		return "", "", "", ecode.New(ecode.ErrInternalServer.Code, "invalid click id format")
	}
	merchantCode = strings.TrimSpace(string(data[9:16]))
	if flags&1 != 0 {
		userCode = strings.TrimSpace(string(data[16:23]))
	}
	if flags&2 != 0 {
		sub1 = strings.TrimSpace(string(data[23:28]))
	}

	return merchantCode, userCode, sub1, nil
}
