package cashbackutils

import (
	"fmt"
	"strconv"
	"strings"
)

// ParseCommissionRate 解析佣金比例数据，返回佣金值和类型
// ParseCommissionRate 解析佣金率字符串，返回完整的佣金信息
// 支持的格式：
// 1. 百分比格式："0.7%", "Up to 1.4%", "Revshare 70%"
// 2. 固定金额格式："USD 21", "$ 15", "$3.5"
// 3. 组合格式："0.6%/USD 16.8", "Up to 1.3%/EUR 4.55"
// 4. 范围格式："0.0 ~ 10.0 (EUR)", "0.0% ~ 0.5%"
// 5. 括号格式："0.01(USD)"
type CommissionInfo struct {
	Value      float64
	Type       string // "%", "USD", "EUR" 等
	IsUpTo     bool   // 是否包含 "up to"
	IsRevShare bool   // 是否是 Revshare 模式
	// 对于组合格式，存储替代性佣金信息
	AlternativeValue      float64
	AlternativeType       string
	AlternativeIsUpTo     bool
	AlternativeIsRevShare bool
}

func ParseCommissionRate(commissionStr string) (*CommissionInfo, error) {
	// 创建返回结构
	info := &CommissionInfo{}

	// 去除字符串两端的空白字符
	commissionStr = strings.TrimSpace(commissionStr)

	// 如果字符串为空，返回错误
	if commissionStr == "" {
		return nil, fmt.Errorf("empty commission string")
	}

	// 检查是否包含 "up to"
	if strings.Contains(strings.ToLower(commissionStr), "up to") {
		info.IsUpTo = true
		commissionStr = strings.TrimPrefix(strings.ToLower(commissionStr), "up to ")
	}

	// 检查是否是 Revshare 模式
	if strings.Contains(strings.ToLower(commissionStr), "revshare") {
		info.IsRevShare = true
		commissionStr = strings.TrimPrefix(strings.ToLower(commissionStr), "revshare ")
	}

	// 处理新格式："0.0 ~ 10.0 (EUR)" 或 "0.0% ~ 0.5%"
	if strings.Contains(commissionStr, "~") {
		parts := strings.Split(commissionStr, "~")
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid range format")
		}

		// 获取右侧部分（最大值）
		rightPart := strings.TrimSpace(parts[1])

		// 检查是否是百分比格式
		if strings.Contains(rightPart, "%") {
			// 处理百分比格式 "0.5%"
			valueStr := strings.TrimSuffix(rightPart, "%")
			value, err := strconv.ParseFloat(valueStr, 64)
			if err != nil {
				return nil, fmt.Errorf("failed to parse percentage range value: %v", err)
			}
			info.Value = value
			info.Type = "%"
			return info, nil
		}

		// 处理带括号的货币格式 "10.0 (EUR)"
		if strings.Contains(rightPart, "(") && strings.Contains(rightPart, ")") {
			valuePart := rightPart[:strings.Index(rightPart, "(")]
			currencyPart := rightPart[strings.Index(rightPart, "(")+1 : strings.Index(rightPart, ")")]

			valuePart = strings.TrimSpace(valuePart)
			currencyPart = strings.TrimSpace(currencyPart)

			value, err := strconv.ParseFloat(valuePart, 64)
			if err != nil {
				return nil, fmt.Errorf("failed to parse currency range value: %v", err)
			}

			info.Value = value
			info.Type = currencyPart
			return info, nil
		}
	}

	// 处理括号格式："0.01(USD)"
	if strings.Contains(commissionStr, "(") && strings.Contains(commissionStr, ")") && !strings.Contains(commissionStr, " ") {
		valuePart := commissionStr[:strings.Index(commissionStr, "(")]
		currencyPart := commissionStr[strings.Index(commissionStr, "(")+1 : strings.Index(commissionStr, ")")]

		value, err := strconv.ParseFloat(valuePart, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse parentheses format value: %v", err)
		}

		info.Value = value
		info.Type = currencyPart
		return info, nil
	}

	// 处理带有货币符号的金额格式（如 "$15", "€10", "£20"）
	currencySymbols := map[string]string{
		"$": "USD",
		"€": "EUR",
		"£": "GBP",
	}

	for symbol, currencyType := range currencySymbols {
		if strings.HasPrefix(commissionStr, symbol) {
			amountStr := strings.TrimPrefix(commissionStr, symbol)
			amountStr = strings.TrimSpace(amountStr)
			value, err := strconv.ParseFloat(amountStr, 64)
			if err != nil {
				return nil, fmt.Errorf("failed to parse amount with currency symbol: %v", err)
			}
			info.Value = value
			info.Type = currencyType
			return info, nil
		}
	}

	// 处理组合格式（如 "0.6%/USD 16.8" 或 "0.7%;Up to EUR 3.45"）
	if strings.Contains(commissionStr, "/") || strings.Contains(commissionStr, ";") {
		var parts []string
		if strings.Contains(commissionStr, "/") {
			parts = strings.Split(commissionStr, "/")
		} else {
			parts = strings.Split(commissionStr, ";")
		}
		if len(parts) == 2 {
			// 解析第一部分
			firstInfo, err := ParseCommissionRate(parts[0])
			if err != nil {
				return nil, err
			}
			// 解析第二部分
			secondInfo, err := ParseCommissionRate(parts[1])
			if err != nil {
				return nil, err
			}
			// 合并信息
			info.Value = firstInfo.Value
			info.Type = firstInfo.Type
			info.IsUpTo = firstInfo.IsUpTo
			info.IsRevShare = firstInfo.IsRevShare
			info.AlternativeValue = secondInfo.Value
			info.AlternativeType = secondInfo.Type
			info.AlternativeIsUpTo = secondInfo.IsUpTo
			info.AlternativeIsRevShare = secondInfo.IsRevShare
			return info, nil
		}
	}

	// 处理百分比格式
	if strings.Contains(commissionStr, "%") {
		// 移除百分号
		commissionStr = strings.TrimSuffix(commissionStr, "%")
		// 转换为float64
		value, err := strconv.ParseFloat(commissionStr, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse percentage value: %v", err)
		}
		info.Value = value
		info.Type = "%"
		return info, nil
	}

	// 处理固定金额格式（例如 "USD 21"）
	parts := strings.Fields(commissionStr)
	if len(parts) == 2 {
		// 获取货币类型和金额
		currencyType := strings.ToUpper(parts[0])
		amountStr := parts[1]

		// 转换金额为float64
		value, err := strconv.ParseFloat(amountStr, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse fixed amount: %v", err)
		}
		info.Value = value
		info.Type = currencyType
		return info, nil
	}

	// 如果格式不匹配任何已知格式，返回错误
	return info, fmt.Errorf("invalid commission rate format")
}

// FormatCommissionInfo 将CommissionInfo结构体转换为字符串格式
// 支持的格式：
// 1. 百分比格式："0.7%", "Up to 1.4%", "Revshare 70%"
// 2. 固定金额格式："USD 21", "$ 15", "$3.5"
// 3. 组合格式："0.6%/USD 16.8", "Up to 1.3%/EUR 4.55"
func FormatCommissionInfo(info *CommissionInfo) string {
	if info == nil {
		return ""
	}

	// 格式化主要佣金信息
	mainPart := formatSingleCommissionInfo(info.Value, info.Type, info.IsUpTo, info.IsRevShare)

	// 如果存在替代性佣金信息，添加到结果中
	if info.AlternativeValue != 0 {
		alternativePart := formatSingleCommissionInfo(
			info.AlternativeValue,
			info.AlternativeType,
			info.AlternativeIsUpTo,
			info.AlternativeIsRevShare,
		)
		return mainPart + "/" + alternativePart
	}

	return mainPart
}

// formatSingleCommissionInfo 格式化单个佣金信息
func formatSingleCommissionInfo(value float64, commissionType string, isUpTo bool, isRevShare bool) string {
	var result string

	// 添加"Up to"前缀
	if isUpTo {
		result += "Up to "
	}

	// 添加"Revshare"前缀
	if isRevShare {
		result += "Revshare "
	}

	// 处理不同类型的佣金格式
	switch commissionType {
	case "%":
		result += fmt.Sprintf("%.1f%%", value)
	default:
		// 使用货币符号映射
		currencySymbols := map[string]string{
			"USD": "$",
			"EUR": "€",
			"GBP": "£",
			"JPY": "¥",
			"CNY": "¥",
			"KRW": "₩",
			"INR": "₹",
			"RUB": "₽",
			"BRL": "R$",
			"THB": "฿",
		}
		if symbol, ok := currencySymbols[commissionType]; ok {
			result += fmt.Sprintf("%v%.2f", symbol, value)
		} else {
			result += fmt.Sprintf("%v %.2f", commissionType, value)
		}
	}

	return result
}
