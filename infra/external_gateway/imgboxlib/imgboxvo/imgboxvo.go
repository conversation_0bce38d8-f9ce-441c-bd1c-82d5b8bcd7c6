package imgboxvo

// Response represents the standard API response from ImgBox
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// UploadResponse represents the data returned from an upload
type UploadResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		ConvertToWebp bool   `json:"convert_to_webp"`
		ImgUrl        string `json:"img_url"`
		Quality       int    `json:"quality"`
		Source        string `json:"source"`
	} `json:"data"`
}
