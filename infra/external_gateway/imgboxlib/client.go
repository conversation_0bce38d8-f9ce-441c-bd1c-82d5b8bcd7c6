package imgboxlib

import (
	"bonusearned/infra/ecode"
	"bonusearned/infra/external_gateway/imgboxlib/imgboxvo"
	"bytes"
	"context"
	"encoding/json"
	"mime/multipart"
	"net/http"
)

func UploadImageFromURL(token string, imageURL string, convertToWebp string, filename string) (*imgboxvo.UploadResponse, *ecode.Error) {
	ctx := context.Background()

	// Create a buffer to store the multipart form data
	var body bytes.Buffer
	writer := multipart.NewWriter(&body)

	// Add form fields
	_ = writer.WriteField("upload_token", token)
	_ = writer.WriteField("image_url", imageURL)
	_ = writer.WriteField("convert_to_webp", convertToWebp)
	_ = writer.WriteField("filename", filename)

	// Close the multipart writer to set the terminating boundary
	if err := writer.Close(); err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	// Set headers
	headers := map[string]string{
		"Content-Type": writer.FormDataContentType(),
	}
	// Create and send the HTTP request
	resp, err := remoteInvokeWithUrl(ctx, DefaultHost+apiUpload, http.MethodPost, nil, headers, &body)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}
	// Parse the response
	uploadImageResp := new(imgboxvo.UploadResponse)
	err = json.Unmarshal(resp, uploadImageResp)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}
	return uploadImageResp, nil
}
