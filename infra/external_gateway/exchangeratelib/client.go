package exchangeratelib

import (
	"bonusearned/infra/ecode"
	"bonusearned/infra/external_gateway/exchangeratelib/exchangeratevo"
	"context"
	"encoding/json"
	"go.uber.org/zap"
	"net/http"
)

func GetExchangeRates() (*exchangeratevo.GetExchangeRatesResp, *ecode.Error) {
	ctx := context.Background()
	params := map[string]interface{}{}
	headers := map[string]string{
		"Accept-Language": "en-US,en;q=0.9",
		"Accept":          "*/*",
		"User-Agent":      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetRates, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}
	getExchangeRatesResp := new(exchangeratevo.GetExchangeRatesResp)
	err = json.Unmarshal(resp, getExchangeRatesResp)
	if err != nil {
		zap.L().Error("exchangeratelib GetExchangeRates json.Unmarshal failed", zap.Error(err))
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}
	return getExchangeRatesResp, nil
}
