package rediscache

import (
	"bonusearned/infra/ecode"
	"context"
	"encoding/json"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// Cache Redis缓存
type Cache struct {
	client redis.Cmdable
}

// New 创建Redis缓存实例
func New(client redis.Cmdable) *Cache {
	return &Cache{
		client: client,
	}
}

// Set 设置缓存，指定过期时间
func (c *Cache) Set(ctx *gin.Context, key string, value interface{}, ttl time.Duration) *ecode.Error {
	err := c.client.Set(context.Background(), key, value, ttl).Err()
	if err != nil {
		return ecode.Wrap(err, ecode.ErrCache.Code, "set cache failed")
	}
	return nil
}

// Get 获取缓存
func (c *Cache) Get(ctx *gin.Context, key string) (interface{}, bool) {
	data, err := c.client.Get(context.Background(), key).Bytes()
	if err != nil {
		return nil, false
	}

	var value interface{}
	if err := json.Unmarshal(data, &value); err != nil {
		return nil, false
	}
	return value, true
}

// Delete 删除缓存
func (c *Cache) Delete(ctx *gin.Context, key string) *ecode.Error {
	err := c.client.Del(context.Background(), key).Err()
	if err != nil {
		return ecode.Wrap(err, ecode.ErrCache.Code, "delete cache failed")
	}
	return nil
}

// MGet 批量获取缓存，返回获取到的值和未命中的key
func (c *Cache) MGet(ctx *gin.Context, keyGenerator func(uint64) string, ids []uint64) ([]interface{}, []uint64, *ecode.Error) {
	if len(ids) == 0 {
		return nil, nil, nil
	}

	// 1. 生成所有key
	keys := make([]string, len(ids))
	idMap := make(map[string]uint64)
	for i, id := range ids {
		key := keyGenerator(id)
		keys[i] = key
		idMap[key] = id
	}

	// 2. 批量获取
	values, err := c.client.MGet(context.Background(), keys...).Result()
	if err != nil {
		return nil, ids, ecode.Wrap(err, ecode.ErrCache.Code, "mget cache failed")
	}

	// 3. 处理结果
	results := make([]interface{}, 0, len(values))
	missedIDs := make([]uint64, 0)

	for i, value := range values {
		if value == nil {
			missedIDs = append(missedIDs, idMap[keys[i]])
			continue
		}

		// 反序列化
		var data interface{}
		if err := json.Unmarshal([]byte(value.(string)), &data); err != nil {
			missedIDs = append(missedIDs, idMap[keys[i]])
			continue
		}
		results = append(results, data)
	}

	return results, missedIDs, nil
}
