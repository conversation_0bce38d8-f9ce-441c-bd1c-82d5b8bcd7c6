package localcache

import (
	"fmt"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type item struct {
	value      interface{}
	expiration int64
}

// Cache 本地缓存
type Cache struct {
	items sync.Map
}

// New 创建一个新的缓存实例
func New() *Cache {
	cache := &Cache{}
	go cache.cleanExpired()
	return cache
}

// Set 设置缓存，指定过期时间
func (c *Cache) Set(ctx *gin.Context, key string, value interface{}, ttl time.Duration) {
	var expiration int64
	if ttl > 0 {
		expiration = time.Now().Add(ttl).UnixNano()
	}
	c.items.Store(key, &item{
		value:      value,
		expiration: expiration,
	})
}

// Get 获取缓存
func (c *Cache) Get(ctx *gin.Context, key string) (interface{}, bool) {
	if val, ok := c.items.Load(key); ok {
		if item, ok := val.(*item); ok {
			if item.expiration == 0 || time.Now().UnixNano() < item.expiration {
				return item.value, true
			}
			c.items.Delete(key)
		}
	}
	return nil, false
}

// Delete 删除缓存
func (c *Cache) Delete(ctx *gin.Context, key string) {
	c.items.Delete(key)
}

// Range 遍历缓存中的所有有效项
func (c *Cache) Range(ctx *gin.Context, f func(key string, value interface{}) bool) {
	now := time.Now().UnixNano()
	c.items.Range(func(key, value interface{}) bool {
		k := key.(string)
		if item, ok := value.(*item); ok {
			if item.expiration == 0 || now < item.expiration {
				return f(k, item.value)
			}
			c.items.Delete(k)
		}
		return true
	})
}

// GenKey 生成缓存key
func GenKey(prefix string, id interface{}) string {
	return fmt.Sprintf("%s:%v", prefix, id)
}

// cleanExpired 定期清理过期的缓存项
func (c *Cache) cleanExpired() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		now := time.Now().UnixNano()
		c.items.Range(func(key, value interface{}) bool {
			if item, ok := value.(*item); ok {
				if item.expiration > 0 && now > item.expiration {
					c.items.Delete(key)
				}
			}
			return true
		})
	}
}
