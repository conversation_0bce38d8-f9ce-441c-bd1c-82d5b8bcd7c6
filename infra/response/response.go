package response

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`           // 状态码
	Message string      `json:"message"`        // 消息
	Data    interface{} `json:"data,omitempty"` // 数据
}

// ListResponse 列表响应结构
type ListResponse struct {
	Total int64       `json:"total"` // 总数
	Items interface{} `json:"items"` // 列表项
	Page  int         `json:"page"`  // 当前页
	Size  int         `json:"size"`  // 每页大小
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, &Response{
		Code:    http.StatusOK,
		Message: "success",
		Data:    data,
	})
	return
}

// SuccessList 成功列表响应
func SuccessList(c *gin.Context, items interface{}, total int64, page, size int) *Response {
	return &Response{
		Code:    http.StatusOK,
		Message: "success",
		Data: &ListResponse{
			Total: total,
			Items: items,
			Page:  page,
			Size:  size,
		},
	}
}

// Error 错误响应
func Error(c *gin.Context, code int, message string) {
	c.JSON(http.StatusOK, &Response{
		Code:    code,
		Message: message,
	})
	return
}

// ErrorWithData 带数据的错误响应
func ErrorWithData(c *gin.Context, code int, message string, data interface{}) *Response {
	return &Response{
		Code:    code,
		Message: message,
		Data:    data,
	}
}
