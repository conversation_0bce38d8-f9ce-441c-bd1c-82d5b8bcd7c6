package constant

// Redis 相关常量
const (
	// MERCHANT_INFO_SEPARATOR Unit Separator (ASCII 31, \x1F) - 专门用于分隔商家信息的控制字符
	MERCHANT_INFO_SEPARATOR = string(rune(31))

	// Redis key 前缀
	MERCHANT_INFO_KEY_PREFIX = "merchant:"
	MERCHANT_INFO_KEY_SUFFIX = ":info"

	// 缓存过期时间
	CACHE_EXPIRE_TIME_1MIN  = 60               // 1分钟
	CACHE_EXPIRE_TIME_1H    = 60 * 60          // 1小时
	CACHE_EXPIRE_TIME_24H   = 24 * 60 * 60     // 24小时
	CACHE_EXPIRE_TIME_3DAYS = 3 * 24 * 60 * 60 // 3天
)

// GetMerchantInfoKey 生成商家信息的Redis key
func GetMerchantInfoKey(merchantCode string) string {
	return MERCHANT_INFO_KEY_PREFIX + merchantCode + MERCHANT_INFO_KEY_SUFFIX
}

// GetMerchantCacheKey 生成商家缓存的Redis key
func GetMerchantCacheKey(id uint) string {
	return MERCHANT_INFO_KEY_PREFIX + string(id)
}

// GetMerchantCodeCacheKey 生成商家编码缓存的Redis key
func GetMerchantCodeCacheKey(code string) string {
	return MERCHANT_INFO_KEY_PREFIX + "code:" + code
}

// GetMerchantUniqueNameCacheKey 生成商家唯一名称缓存的Redis key
func GetMerchantUniqueNameCacheKey(uniqueName string) string {
	return MERCHANT_INFO_KEY_PREFIX + "unique_name:" + uniqueName
}
