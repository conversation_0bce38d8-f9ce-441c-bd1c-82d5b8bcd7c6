package constant

const (
	UserNameLiPeiGuan = "peiguan.li"
)

const (
	AccountTypeLinkHaiTao  = "linkhaitao"
	AccountTypePb          = "pb"
	AccountTypeDuoMai      = "duomai"
	AccountTypeFatCoupon   = "fatcoupon"
	AccountTypeLinkBux     = "linkbux"
	AccountTypeBlueAff     = "blueaff"
	AccountTypeRebatesMe   = "rebatesme"
	AccountTypeBonusArrive = "bonusarrive"
	AccountTypeJoinGekko   = "joingekko"
	AccountTypeEClickLink  = "eclicklink"
)

var OrdersAccountList = []map[string]interface{}{
	{
		"account_name": "blueaffbonusearned",
		"type":         AccountTypeBlueAff,
		//"token_ef":     "oTqoWzStQySoGUs3mBaXug",                                                                    // ef 后台
		"token":     "tgw_l7_route=8d7348a14213c84face3dc871c981e01; ba_pub_id=t20v2233k9yysreuumbee5e67uthspr6", // pub 后台
		"limit":     1000,
		"user_name": UserNameLiPeiGuan,
	},
}

var MerchantsAccountList = []map[string]interface{}{
	//{
	//	"publisherKey":        "2464bbaa-c87b-4487-9383-bc5ad17d103f",
	//	"propertyID":          "1001039",
	//	"authKey":             "y6plgxtvbskt6kvk3eoqqeat7a",
	//	"secretKey":           "wapuzhrbbgkvgo4spq7plfkdvm",
	//	"type":                AccountTypeJoinGekko,
	//	"account_name":        "joingekkolpg",
	//	"limit":               100,
	//	"user_name":           UserNameLiPeiGuan,
	//	"spread_sheet_id":     "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
	//	"sheet_name_merchant": "joingekko offer info",
	//	"click_id":            "click_id",
	//	"sub1":                "subid",
	//},
	{
		"account_name": "blueaffbonusearned",
		"type":         AccountTypeBlueAff,
		"token":        "tgw_l7_route=8d7348a14213c84face3dc871c981e01; ba_pub_id=t20v2233k9yysreuumbee5e67uthspr6",
		"limit":        1000,
		"user_name":    UserNameLiPeiGuan,
		"click_id":     "sub1",
		"sub1":         "sub2",
	},
}

var CouponsAccountList = []map[string]interface{}{
	{
		"account_name": "linkbuxconan123",
		"type":         AccountTypeLinkBux,
		"token":        "b70fba9310df5cd74fb4f18c5e51e2cfcdb14ea8",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
}
