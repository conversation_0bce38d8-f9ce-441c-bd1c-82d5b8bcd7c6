package constant

// CountryCodeToName 国家代码到国家名称的映射
var CountryCodeToName = map[string]string{
	"US": "United States",
	"CA": "Canada",
	"AT": "Austria",
	"DE": "Germany",
	"FR": "France",
	"UK": "United Kingdom",
	"ES": "Spain",
	"IT": "Italy",
	"MX": "Mexico",
	"IN": "India",
	"PT": "Portugal",
	"PL": "Poland",
	"NL": "Netherlands",
	"AU": "Australia",
	"BE": "Belgium",
	"BR": "Brazil",
	"CL": "Chile",
	"HK": "Hong Kong",
	"AR": "Argentina",
	"FI": "Finland",
	"SE": "Sweden",
	"SG": "Singapore",
	"DK": "Denmark",
	"NO": "Norway",
	"PE": "Peru",
	"CZ": "Czechia",
	"TR": "Turkiye",
	"IE": "Ireland",
	"SA": "Saudi Arabia",
	"CH": "Switzerland",
	"MY": "Malaysia",
	"RO": "Romania",
	"AE": "United Arab Emirates",
	"NZ": "New Zealand",
}

// CountryNameToCode 国家名称到国家代码的映射
var CountryNameToCode = map[string]string{
	"United States":        "US",
	"Canada":               "CA",
	"Austria":              "AT",
	"Germany":              "DE",
	"France":               "FR",
	"United Kingdom":       "UK",
	"Spain":                "ES",
	"Italy":                "IT",
	"Mexico":               "MX",
	"India":                "IN",
	"Portugal":             "PT",
	"Poland":               "PL",
	"Netherlands":          "NL",
	"Australia":            "AU",
	"Belgium":              "BE",
	"Brazil":               "BR",
	"Chile":                "CL",
	"Hong Kong":            "HK",
	"Argentina":            "AR",
	"Finland":              "FI",
	"Sweden":               "SE",
	"Singapore":            "SG",
	"Denmark":              "DK",
	"Norway":               "NO",
	"Peru":                 "PE",
	"Czechia":              "CZ",
	"Turkiye":              "TR",
	"Ireland":              "IE",
	"Saudi Arabia":         "SA",
	"Switzerland":          "CH",
	"Malaysia":             "MY",
	"Romania":              "RO",
	"United Arab Emirates": "AE",
	"New Zealand":          "NZ",
}

// GetCountryCodeByName 根据国家名称获取国家代码
func GetCountryCodeByName(name string) (string, bool) {
	code, exists := CountryNameToCode[name]
	return code, exists
}

// GetCountryNameByCode 根据国家代码获取国家名称
func GetCountryNameByCode(code string) (string, bool) {
	name, exists := CountryCodeToName[code]
	return name, exists
}

// IsValidCountryCode 检查是否为有效的国家代码
func IsValidCountryCode(code string) bool {
	_, exists := CountryCodeToName[code]
	return exists
}

// IsValidCountryName 检查是否为有效的国家名称
func IsValidCountryName(name string) bool {
	_, exists := CountryNameToCode[name]
	return exists
}
