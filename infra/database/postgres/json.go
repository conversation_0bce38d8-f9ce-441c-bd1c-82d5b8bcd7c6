package postgres

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// JSON 自定义 JSON 类型
type JSON []byte

// Value 实现 driver.Valuer 接口
func (j JSON) Value() (driver.Value, error) {
	if j.<PERSON>ull() {
		return nil, nil
	}
	return string(j), nil
}

// Scan 实现 sql.Scanner 接口
func (j *JSON) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}
	s, ok := value.([]byte)
	if !ok {
		return errors.New("Invalid scan source")
	}
	*j = append((*j)[0:0], s...)
	return nil
}

// MarshalJSON 实现 json.Marshaler 接口
func (j JSON) MarshalJSON() ([]byte, error) {
	if j.<PERSON>ull() {
		return []byte("null"), nil
	}
	return j, nil
}

// UnmarshalJSON 实现 json.Unmarshaler 接口
func (j *JSON) UnmarshalJSON(data []byte) error {
	if j == nil {
		return errors.New("JSON: UnmarshalJSON on nil pointer")
	}
	*j = append((*j)[0:0], data...)
	return nil
}

// IsNull 检查是否为空
func (j JSON) IsNull() bool {
	return len(j) == 0 || string(j) == "null"
}

// Equals 比较两个 JSON 是否相等
func (j JSON) Equals(j1 JSON) bool {
	return string(j) == string(j1)
}

// String 实现 Stringer 接口
func (j JSON) String() string {
	return string(j)
}

// FromString 从字符串创建 JSON
func FromString(s string) JSON {
	return JSON(s)
}

// FromMap 从 map 创建 JSON
func FromMap(m map[string]interface{}) (JSON, error) {
	data, err := json.Marshal(m)
	if err != nil {
		return nil, err
	}
	return JSON(data), nil
}

// ToMap 将 JSON 转换为 map
func (j JSON) ToMap() (map[string]interface{}, error) {
	if j.IsNull() {
		return nil, nil
	}
	var m map[string]interface{}
	err := json.Unmarshal(j, &m)
	return m, err
}
