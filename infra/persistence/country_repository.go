package persistence

import (
	"bonusearned/domain/merchant/entity"
	"bonusearned/domain/merchant/repository"
	"bonusearned/infra/constant"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type countryPostgresRepository struct {
	db *gorm.DB
}

// NewCountryPostgresRepository 创建国家仓储
func NewCountryPostgresRepository(db *gorm.DB) repository.CountryRepository {
	return &countryPostgresRepository{db: db}
}

// CreateCountry 创建国家
func (r *countryPostgresRepository) CreateCountry(ctx *gin.Context, country *entity.Country) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(country).Error; err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create country")
	}
	return nil
}

// UpdateCountry 更新国家
func (r *countryPostgresRepository) UpdateCountry(ctx *gin.Context, country *entity.Country) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(country).Error; err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to update country")
	}
	return nil
}

// GetCountryDetailById 根据ID获取国家详情
func (r *countryPostgresRepository) GetCountryDetailById(ctx *gin.Context, id uint64) (*entity.Country, *ecode.Error) {
	var country entity.Country
	err := r.db.WithContext(ctx).
		Where("id = ? AND status = ?", id, constant.StatusActive).
		First(&country).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrCountryNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get country")
	}
	return &country, nil
}

// GetCountryDetailByCode 根据代码获取国家详情
func (r *countryPostgresRepository) GetCountryDetailByCode(ctx *gin.Context, code string) (*entity.Country, *ecode.Error) {
	var country entity.Country
	err := r.db.WithContext(ctx).
		Where("code = ? AND status = ?", code, constant.StatusActive).
		First(&country).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrCountryNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get country")
	}
	return &country, nil
}

// GetCountryListByCondition 获取国家列表
func (r *countryPostgresRepository) GetCountryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Country, int64, *ecode.Error) {
	var countries []*entity.Country
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Country{})

	// 添加条件
	for key, value := range condition {
		switch key {
		case "status":
			query = query.Where("status = ?", value)
		case "code":
			query = query.Where("code = ?", value)
		case "name":
			query = query.Where("name ILIKE ?", "%"+value.(string)+"%")
		}
	}

	// 默认只查询启用状态的国家
	if _, exists := condition["status"]; !exists {
		query = query.Where("status = ?", constant.StatusActive)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count countries")
	}

	// 获取数据
	err := query.Order("name ASC").Find(&countries).Error
	if err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get countries")
	}

	return countries, total, nil
}

// DeleteCountry 删除国家
func (r *countryPostgresRepository) DeleteCountry(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.Country{}, id).Error; err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to delete country")
	}
	return nil
}
