package persistence

import (
	"bonusearned/domain/clickrecord/entity"
	"bonusearned/domain/clickrecord/repository"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type clickPostgresRepository struct {
	db *gorm.DB
}

// NewClickPostgresRepository 创建点击记录仓储
func NewClickPostgresRepository(db *gorm.DB) repository.ClickRepository {
	return &clickPostgresRepository{db: db}
}

// CreateClickRecord 创建点击记录
func (r *clickPostgresRepository) CreateClickRecord(ctx *gin.Context, record *entity.ClickRecord) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(record).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// GetClickRecordDetailById 根据ID获取点击记录
func (r *clickPostgresRepository) GetClickRecordDetailById(ctx *gin.Context, id uint64) (*entity.ClickRecord, *ecode.Error) {
	var record entity.ClickRecord
	if err := r.db.WithContext(ctx).First(&record, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &record, nil
}

// GetClickRecordListByCondition 获取用户的点击记录
func (r *clickPostgresRepository) GetClickRecordListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.ClickRecord, int64, *ecode.Error) {
	var records []*entity.ClickRecord
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.ClickRecord{})

	// 处理搜索条件
	if userID, ok := condition["user_id"].(uint64); ok && userID > 0 {
		query = query.Where("user_id = ?", userID)
	}
	query = query.Order("click_date DESC")
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 获取列表
	if err := query.Find(&records).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	return records, total, nil
}

// DeleteClickRecord 删除点击记录
func (r *clickPostgresRepository) DeleteClickRecord(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.ClickRecord{}, id).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}
