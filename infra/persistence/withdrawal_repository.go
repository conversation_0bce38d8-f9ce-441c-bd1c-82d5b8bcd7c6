package persistence

import (
	"bonusearned/domain/withdrawal/entity"
	"bonusearned/domain/withdrawal/repository"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type withdrawalPostgresRepository struct {
	db *gorm.DB
}

// NewWithdrawalPostgresRepository 创建提现仓储
func NewWithdrawalPostgresRepository(db *gorm.DB) repository.WithdrawalRepository {
	return &withdrawalPostgresRepository{db: db}
}

// CreateWithdrawal 创建提现记录
func (r *withdrawalPostgresRepository) CreateWithdrawal(ctx *gin.Context, withdrawal *entity.Withdrawal) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(withdrawal).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// GetWithdrawalDetailByID 根据ID获取提现记录
func (r *withdrawalPostgresRepository) GetWithdrawalDetailByID(ctx *gin.Context, id uint64) (*entity.Withdrawal, *ecode.Error) {
	var withdrawal entity.Withdrawal
	if err := r.db.WithContext(ctx).First(&withdrawal, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &withdrawal, nil
}

// GetWithdrawalListByCondition 获取用户的提现记录
func (r *withdrawalPostgresRepository) GetWithdrawalListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Withdrawal, int64, *ecode.Error) {
	var withdrawals []*entity.Withdrawal
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Withdrawal{})

	// 处理搜索条件
	if userID, ok := condition["user_id"].(uint64); ok && userID > 0 {
		query = query.Where("user_id = ?", userID)
	}

	if status, ok := condition["status"].(entity.WithdrawalStatus); ok {
		query = query.Where("status = ?", status)
	}

	query = query.Order("created_at DESC")
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 获取列表
	if err := query.Find(&withdrawals).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	return withdrawals, total, nil
}

// UpdateWithdrawalStatus 更新提现状态
func (r *withdrawalPostgresRepository) UpdateWithdrawalStatus(ctx *gin.Context, withdrawal *entity.Withdrawal) *ecode.Error {
	if err := r.db.WithContext(ctx).Model(withdrawal).Updates(map[string]interface{}{
		"status":         withdrawal.Status,
		"process_time":   withdrawal.ProcessTime,
		"complete_time":  withdrawal.CompleteTime,
		"fail_reason":    withdrawal.FailReason,
		"processor_id":   withdrawal.ProcessorID,
		"transaction_id": withdrawal.TransactionID,
	}).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}
