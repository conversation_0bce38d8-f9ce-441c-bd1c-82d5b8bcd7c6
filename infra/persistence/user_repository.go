package persistence

import (
	"bonusearned/domain/user/entity"
	"bonusearned/domain/user/repository"
	"bonusearned/infra/ecode"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type userPostgresRepository struct {
	db *gorm.DB
}

// NewUserPostgresRepository 创建用户仓储实现
func NewUserPostgresRepository(db *gorm.DB) repository.UserRepository {
	return &userPostgresRepository{db: db}
}

// GetUserDetailById 根据ID获取用户详情
func (r *userPostgresRepository) GetUserDetailById(ctx *gin.Context, id uint64) (*entity.User, *ecode.Error) {
	var user entity.User
	if err := r.db.WithContext(ctx).First(&user, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrUserNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &user, nil
}

// GetUserDetailByEmail 根据邮箱获取用户详情
func (r *userPostgresRepository) GetUserDetailByEmail(ctx *gin.Context, email string) (*entity.User, *ecode.Error) {
	var user entity.User
	if err := r.db.WithContext(ctx).Where("email = ?", email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrUserNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &user, nil
}

// GetUserDetailByUserCode 根据用户代码获取用户详情
func (r *userPostgresRepository) GetUserDetailByUserCode(ctx *gin.Context, userCode string) (*entity.User, *ecode.Error) {
	var user entity.User
	if err := r.db.WithContext(ctx).Where("user_code = ?", userCode).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrUserNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &user, nil
}

// CreateUser 创建用户
func (r *userPostgresRepository) CreateUser(ctx *gin.Context, user *entity.User) *ecode.Error {
	// 使用事务确保数据一致性
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 检查邮箱是否已存在
		var count int64
		if err := tx.Model(&entity.User{}).Where("email = ?", user.Email).Count(&count).Error; err != nil {
			return err
		}
		if count > 0 {
			return ecode.ErrEmailExists
		}

		// 创建用户
		if err := tx.Create(user).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		if e, ok := err.(*ecode.Error); ok {
			return e
		}
		return ecode.ErrDatabase
	}
	return nil
}

// UpdateProfile 更新用户基本信息（仅限：phone、nickname）
func (r *userPostgresRepository) UpdateProfile(ctx *gin.Context, user *entity.User) *ecode.Error {
	result := r.db.WithContext(ctx).Model(&entity.User{}).
		Where("id = ?", user.ID).
		Updates(map[string]interface{}{
			"phone":      user.Phone,
			"nickname":   user.Nickname,
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		return ecode.ErrDatabase
	}
	if result.RowsAffected == 0 {
		return ecode.ErrUserNotFound
	}
	return nil
}

// UpdatePaymentInfo 更新用户支付信息
func (r *userPostgresRepository) UpdatePaymentInfo(ctx *gin.Context, id uint64, paymentInfo map[string]interface{}) *ecode.Error {
	result := r.db.WithContext(ctx).Model(&entity.User{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"payment_info": paymentInfo,
			"updated_at":   time.Now(),
		})

	if result.Error != nil {
		return ecode.ErrDatabase
	}
	if result.RowsAffected == 0 {
		return ecode.ErrUserNotFound
	}
	return nil
}

// UpdateUserPassword 更新用户密码
func (r *userPostgresRepository) UpdateUserPassword(ctx *gin.Context, id uint64, hashedPassword string) *ecode.Error {
	result := r.db.WithContext(ctx).Model(&entity.User{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"password":   hashedPassword,
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		return ecode.ErrDatabase
	}
	if result.RowsAffected == 0 {
		return ecode.ErrUserNotFound
	}
	return nil
}

// UpdateUserBalance 更新用户余额（仅供定时任务调用）
func (r *userPostgresRepository) UpdateUserBalance(ctx *gin.Context, user *entity.User) *ecode.Error {
	// 使用事务确保余额更新的原子性
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 检查用户是否存在并锁定记录
		var dbUser entity.User
		if err := tx.Set("gorm:for_update", true).First(&dbUser, user.ID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return ecode.ErrUserNotFound
			}
			return err
		}

		// 只更新余额相关字段
		if err := tx.Model(&entity.User{}).Where("id = ?", user.ID).
			Updates(map[string]interface{}{
				"user_balance": user.UserBalance,
				"updated_at":   time.Now(),
			}).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		if e, ok := err.(*ecode.Error); ok {
			return e
		}
		return ecode.ErrDatabase
	}
	return nil
}

// UpdateUserLastLogin 更新用户最后登录时间
func (r *userPostgresRepository) UpdateUserLastLogin(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Model(&entity.User{}).Where("id = ?", id).Update("last_login_at", time.Now()).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// DeleteUser 删除用户
func (r *userPostgresRepository) DeleteUser(ctx *gin.Context, id uint64) *ecode.Error {
	result := r.db.WithContext(ctx).Delete(&entity.User{}, id)
	if result.Error != nil {
		return ecode.ErrDatabase
	}
	if result.RowsAffected == 0 {
		return ecode.ErrUserNotFound
	}
	return nil
}

// GetUserListByCondition 根据条件获取用户列表
func (r *userPostgresRepository) GetUserListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.User, int64, *ecode.Error) {
	var users []*entity.User
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&entity.User{})

	// 在应用所有筛选条件后计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count users")
	}
	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if pageSize, ok := condition["page_size"].(int); ok {
		query = query.Limit(pageSize)
	}

	// 只查询需要的字段
	err := query.Find(&users).Error
	if err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get users")
	}

	return users, total, nil
}

// BatchUpdateUserBalance 批量更新用户余额（仅供定时任务调用）
func (r *userPostgresRepository) BatchUpdateUserBalance(ctx *gin.Context, users []*entity.User) *ecode.Error {
	if len(users) == 0 {
		return nil
	}

	// 使用事务确保批量更新的原子性
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 分批处理，每批最多1000条记录
		batchSize := 1000
		for i := 0; i < len(users); i += batchSize {
			end := i + batchSize
			if end > len(users) {
				end = len(users)
			}

			batch := users[i:end]

			// 为每个用户更新余额
			for _, user := range batch {
				if err := tx.Model(&entity.User{}).Where("id = ?", user.ID).
					Updates(map[string]interface{}{
						"user_balance": user.UserBalance,
						"updated_at":   time.Now(),
					}).Error; err != nil {
					return err
				}
			}
		}
		return nil
	})

	if err != nil {
		if e, ok := err.(*ecode.Error); ok {
			return e
		}
		return ecode.ErrDatabase
	}
	return nil
}
