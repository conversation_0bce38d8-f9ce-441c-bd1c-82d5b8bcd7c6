package persistence

import (
	"bonusearned/domain/merchant/entity"
	"bonusearned/domain/merchant/repository"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type categoryPostgresRepository struct {
	db *gorm.DB
}

// NewCategoryPostgresRepository 创建商家分类仓储
func NewCategoryPostgresRepository(db *gorm.DB) repository.CategoryRepository {
	return &categoryPostgresRepository{db: db}
}

// CreateCategory 创建商家分类
func (r *categoryPostgresRepository) CreateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(category).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// UpdateCategory 更新商家分类
func (r *categoryPostgresRepository) UpdateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(category).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// GetCategoryDetailById 根据ID获取商家分类
func (r *categoryPostgresRepository) GetCategoryDetailById(ctx *gin.Context, id uint64) (*entity.Category, *ecode.Error) {
	var category entity.Category
	if err := r.db.WithContext(ctx).First(&category, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &category, nil
}

// GetCategoryDetailByName 根据名称获取商家分类
func (r *categoryPostgresRepository) GetCategoryDetailByName(ctx *gin.Context, name string) (*entity.Category, *ecode.Error) {
	var category entity.Category
	if err := r.db.WithContext(ctx).Where("name = ?", name).First(&category).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &category, nil
}

// GetCategoryListByCondition 获取商家分类列表
func (r *categoryPostgresRepository) GetCategoryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Category, int64, *ecode.Error) {
	var categories []*entity.Category
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Category{})

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 获取列表
	if err := query.Find(&categories).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	return categories, total, nil
}
