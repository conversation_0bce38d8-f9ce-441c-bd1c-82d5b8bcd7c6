package persistence

import (
	"bonusearned/domain/coupon/entity"
	"bonusearned/domain/coupon/repository"
	"bonusearned/infra/ecode"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"strings"
	"time"
)

type couponPostgresRepository struct {
	db *gorm.DB
}

// NewCouponPostgresRepository 创建优惠券仓储
func NewCouponPostgresRepository(db *gorm.DB) repository.CouponRepository {
	return &couponPostgresRepository{db: db}
}

// CreateCoupon 创建优惠券
func (r *couponPostgresRepository) CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(coupon).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// UpdateCoupon 更新优惠券
func (r *couponPostgresRepository) UpdateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(coupon).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// DeleteCoupon 删除优惠券
func (r *couponPostgresRepository) DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.Coupon{}, id).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// GetCouponDetailById 根据ID查找优惠券
func (r *couponPostgresRepository) GetCouponDetailById(ctx *gin.Context, id uint64) (*entity.Coupon, *ecode.Error) {
	var coupon entity.Coupon
	if err := r.db.WithContext(ctx).First(&coupon, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &coupon, nil
}

// GetCouponDetailByCode 根据优惠券码查找优惠券
func (r *couponPostgresRepository) GetCouponDetailByCode(ctx *gin.Context, code string) (*entity.Coupon, *ecode.Error) {
	var coupon entity.Coupon
	if err := r.db.WithContext(ctx).Where("code = ?", code).First(&coupon).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &coupon, nil
}

// GetCouponListByCondition 查找优惠券列表
func (r *couponPostgresRepository) GetCouponListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Coupon, int64, *ecode.Error) {
	var coupons []*entity.Coupon
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Coupon{})

	// 处理搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if merchantID, ok := condition["merchant_id"].(uint64); ok && merchantID > 0 {
		query = query.Where("merchant_id = ?", merchantID)
	}

	if featured, ok := condition["featured"].(bool); ok && featured {
		query = query.Where("featured = ?", featured)
	}

	if couponType, ok := condition["type"].(string); ok && couponType != "" {
		query = query.Where("type = ?", couponType)
	}

	query = query.Order("created_at DESC")
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 获取列表
	if err := query.Find(&coupons).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	return coupons, total, nil
}

// BatchCreateCoupon 批量创建优惠券
func (r *couponPostgresRepository) BatchCreateCoupon(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error {
	if len(coupons) == 0 {
		return nil
	}

	const (
		batchSize  = 1000 // 每批处理的记录数
		maxRetries = 3    // 最大重试次数
	)

	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 创建一个优化的 Session
		session := tx.Session(&gorm.Session{
			CreateBatchSize: batchSize,
			PrepareStmt:     true, // 使用预处理语句
			SkipHooks:       true, // 跳过钩子以提高性能
		})

		// 分批处理
		for i := 0; i < len(coupons); i += batchSize {
			end := i + batchSize
			if end > len(coupons) {
				end = len(coupons)
			}

			batch := coupons[i:end]

			// 添加重试机制
			var lastErr error
			for retry := 0; retry < maxRetries; retry++ {
				select {
				case <-ctx.Done():
					return fmt.Errorf("context cancelled while processing batch %d-%d: %w",
						i, end, ctx.Err())
				default:
				}

				if err := session.Omit("id").Create(&batch).Error; err != nil {
					lastErr = err
					// 检查是否是死锁错误，如果是则重试
					if strings.Contains(err.Error(), "deadlock") ||
						strings.Contains(err.Error(), "duplicate key") {
						// 指数退避
						backoff := time.Millisecond * time.Duration(100*(1<<retry))
						time.Sleep(backoff)
						continue
					}
					// 其他错误直接返回
					return fmt.Errorf("failed to create coupons batch %d-%d (size: %d): %w",
						i, end, len(batch), err)
				}

				// 成功则跳出重试循环
				lastErr = nil
				break
			}

			// 如果重试后仍然失败
			if lastErr != nil {
				return fmt.Errorf("failed after %d retries on batch %d-%d (size: %d): %w",
					maxRetries, i, end, len(batch), lastErr)
			}
		}
		return nil
	})

	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to batch create coupons")
	}
	return nil
}

// BatchUpdateCoupon 批量更新优惠券
func (r *couponPostgresRepository) BatchUpdateCoupon(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error {
	if err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, coupon := range coupons {
			if err := tx.Save(coupon).Error; err != nil {
				return err
			}
		}
		return nil
	}); err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// BatchDeleteCoupon 批量删除优惠券
func (r *couponPostgresRepository) BatchDeleteCoupon(ctx *gin.Context, ids []uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.Coupon{}, ids).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}
