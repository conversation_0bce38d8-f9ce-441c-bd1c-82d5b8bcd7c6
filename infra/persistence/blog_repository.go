package persistence

import (
	"bonusearned/domain/blog/entity"
	"bonusearned/domain/blog/repository"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type blogPostgresRepository struct {
	db *gorm.DB
}

// NewBlogPostgresRepository 创建博客仓储
func NewBlogPostgresRepository(db *gorm.DB) repository.BlogRepository {
	return &blogPostgresRepository{db: db}
}

// CreateBlog 创建博客
func (r *blogPostgresRepository) CreateBlog(ctx *gin.Context, blog *entity.Blog) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(blog).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// UpdateBlog 更新博客
func (r *blogPostgresRepository) UpdateBlog(ctx *gin.Context, blog *entity.Blog) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(blog).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// DeleteBlog 删除博客
func (r *blogPostgresRepository) DeleteBlog(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.Blog{}, id).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// GetBlogDetailById 根据ID查找博客
func (r *blogPostgresRepository) GetBlogDetailById(ctx *gin.Context, id uint64) (*entity.Blog, *ecode.Error) {
	var blog entity.Blog
	if err := r.db.WithContext(ctx).First(&blog, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &blog, nil
}

// GetBlogListByCondition 搜索博客
func (r *blogPostgresRepository) GetBlogListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Blog, int64, *ecode.Error) {
	var blogs []*entity.Blog
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Blog{})

	// 处理搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		query = query.Where("title ILIKE ? OR content ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if category, ok := condition["category"].(string); ok && category != "" {
		query = query.Where("category = ?", category)
	}
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 获取列表
	if err := query.Find(&blogs).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	return blogs, total, nil
}

func (r *blogPostgresRepository) GetBlogCount(ctx *gin.Context) (int64, *ecode.Error) {
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&entity.Blog{})

	// 在应用所有筛选条件后计算总数
	if err := query.Count(&total).Error; err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count blog")
	}
	return total, nil
}
