package persistence

import (
	"bonusearned/domain/merchant/entity"
	"bonusearned/domain/merchant/repository"
	"bonusearned/infra/constant"
	"bonusearned/infra/ecode"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm/clause"

	"gorm.io/gorm"
)

type merchantPostgresRepository struct {
	db *gorm.DB
}

// NewMerchantPostgresRepository 创建 PostgreSQL 商家仓储实现
func NewMerchantPostgresRepository(db *gorm.DB) repository.MerchantRepository {
	return &merchantPostgresRepository{db: db}
}

// GetMerchantDetailById 根据ID获取商家详情
func (r *merchantPostgresRepository) GetMerchantDetailById(ctx *gin.Context, id uint64) (*entity.Merchant, *ecode.Error) {
	var merchant entity.Merchant
	err := r.db.WithContext(ctx).
		Select("id, name, unique_name, merchant_code, logo, website, track_url, "+
			"cashback_type, description, category_id, featured, country_id, "+
			"supported_countries, created_at, updated_at, cashback_value, cashback_is_upto, cashback_is_rev_share, "+
			"alternative_cashback_type, alternative_cashback_value, alternative_cashback_is_upto, "+
			"alternative_cashback_is_rev_share").
		Preload("Country").
		Where("id = ?", id).
		First(&merchant).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrMerchantNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get merchant")
	}
	return &merchant, nil
}

// GetMerchantDetailByCode 根据商家代码获取商家详情
func (r *merchantPostgresRepository) GetMerchantDetailByCode(ctx *gin.Context, merchantCode string) (*entity.Merchant, *ecode.Error) {
	var merchant entity.Merchant
	err := r.db.WithContext(ctx).
		Select("id, name, unique_name, merchant_code, logo, website, track_url, "+
			"cashback_type, description, category_id, featured, country_id, "+
			"supported_countries, created_at, updated_at, cashback_value, cashback_is_upto, cashback_is_rev_share, "+
			"alternative_cashback_type, alternative_cashback_value, alternative_cashback_is_upto, "+
			"alternative_cashback_is_rev_share").
		Preload("Country").
		Where("merchant_code = ?", merchantCode).
		First(&merchant).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrMerchantNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get merchant by code")
	}
	return &merchant, nil
}

// GetMerchantAffiliateLinkByCode 根据商家代码获取商家上级联盟链接
func (r *merchantPostgresRepository) GetMerchantAffiliateLinkByCode(ctx *gin.Context, merchantCode string) (*entity.Merchant, *ecode.Error) {
	var merchant entity.Merchant
	err := r.db.WithContext(ctx).Select("id, affiliate_link, param_mappings").
		Where("merchant_code = ? AND status = ?", merchantCode, constant.StatusActive).First(&merchant).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrMerchantNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get merchant by code")
	}
	return &merchant, nil
}

// GetMerchantDetailByUniqueName 根据唯一名称获取商家详情
func (r *merchantPostgresRepository) GetMerchantDetailByUniqueName(ctx *gin.Context, uniqueName string) (*entity.Merchant, *ecode.Error) {
	var merchant entity.Merchant
	err := r.db.WithContext(ctx).
		Select("id, name, unique_name, merchant_code, logo, website, track_url, "+
			"cashback_type, description, category_id, featured, country_id, "+
			"supported_countries, created_at, updated_at, cashback_value, cashback_is_upto, cashback_is_rev_share, "+
			"alternative_cashback_type, alternative_cashback_value, alternative_cashback_is_upto, "+
			"alternative_cashback_is_rev_share").
		Preload("Country").
		Where("unique_name = ?", uniqueName).
		First(&merchant).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrMerchantNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get merchant by unique name")
	}
	return &merchant, nil
}

// GetMerchantListByCondition 根据条件获取商家列表
func (r *merchantPostgresRepository) GetMerchantListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Merchant, int64, *ecode.Error) {
	var merchants []*entity.Merchant
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&entity.Merchant{})

	// 处理条件
	if search, ok := condition["search"].(string); ok && search != "" {
		query = query.Where("(name ILIKE ? OR unique_name ILIKE ? OR merchant_code ILIKE ?)",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if categoryID, ok := condition["category_id"].(uint64); ok && categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}

	if status, ok := condition["status"].(int8); ok && status > 0 {
		query = query.Where("status = ?", status)
	}

	if featured, ok := condition["featured"].(bool); ok {
		query = query.Where("featured = ?", featured)
	}

	if country, ok := condition["country"].(string); ok && country != "" {
		query = query.Joins("LEFT JOIN countries ON merchants.country_id = countries.id").
			Where("countries.code = ?", country)
	}

	if countryID, ok := condition["country_id"].(uint64); ok && countryID > 0 {
		query = query.Where("country_id = ?", countryID)
	}

	if startsWith, ok := condition["starts_with"].(string); ok && startsWith != "" {
		if startsWith == "number" {
			query = query.Where("name ~ '^[0-9]'")
		} else {
			query = query.Where("name ILIKE ?", startsWith+"%")
		}
	}

	// 在应用所有筛选条件后计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count merchants")
	}

	// 处理排序
	if sort, ok := condition["sort"].(string); ok {
		switch sort {
		case "popular":
			query = query.Order("cashback_value DESC")
		case "random":
			query = query.Order("RANDOM()")
		case "newest":
			query = query.Order("created_at DESC")
		case "-newest":
			query = query.Order("created_at ASC")
		case "cashback_value":
			query = query.Order("cashback_value DESC")
		case "-cashback_value":
			query = query.Order("cashback_value ASC")
		default:
			query = query.Order("featured DESC, cashback_rate DESC")
		}
	} else {
		query = query.Order("featured DESC, cashback_rate DESC")
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if pageSize, ok := condition["page_size"].(int); ok {
		query = query.Limit(pageSize)
	}

	// 只查询需要的字段
	err := query.Select("merchants.id, merchants.name, merchants.unique_name, merchants.merchant_code, merchants.logo, merchants.website, merchants.track_url, " +
		"merchants.cashback_type, merchants.description, merchants.category_id, merchants.featured, merchants.country_id, " +
		"merchants.supported_countries, merchants.created_at, merchants.updated_at, merchants.cashback_value, merchants.cashback_is_upto, merchants.cashback_is_rev_share, " +
		"merchants.alternative_cashback_type, merchants.alternative_cashback_value, merchants.alternative_cashback_is_upto, " +
		"merchants.alternative_cashback_is_rev_share").
		Preload("Country").
		Find(&merchants).Error
	if err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get merchants")
	}

	return merchants, total, nil
}

// GetMerchantListByConditionWithAllFields 根据条件获取商家列表（返回所有字段）
func (r *merchantPostgresRepository) GetMerchantListByConditionWithAllFields(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Merchant, int64, *ecode.Error) {
	var merchants []*entity.Merchant
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&entity.Merchant{})

	// 处理条件
	if search, ok := condition["search"].(string); ok && search != "" {
		query = query.Where("(name ILIKE ? OR unique_name ILIKE ? OR merchant_code ILIKE ?)",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if categoryID, ok := condition["category_id"].(uint64); ok && categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}

	if status, ok := condition["status"].(int8); ok && status > 0 {
		query = query.Where("status = ?", status)
	}

	if featured, ok := condition["featured"].(bool); ok {
		query = query.Where("featured = ?", featured)
	}

	if country, ok := condition["country"].(string); ok && country != "" {
		query = query.Joins("LEFT JOIN countries ON merchants.country_id = countries.id").
			Where("countries.code = ?", country)
	}

	if countryID, ok := condition["country_id"].(uint64); ok && countryID > 0 {
		query = query.Where("country_id = ?", countryID)
	}

	if startsWith, ok := condition["starts_with"].(string); ok && startsWith != "" {
		if startsWith == "number" {
			query = query.Where("name ~ '^[0-9]'")
		} else {
			query = query.Where("name ILIKE ?", startsWith+"%")
		}
	}

	// 在应用所有筛选条件后计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count merchants")
	}

	// 处理排序
	if sort, ok := condition["sort"].(string); ok {
		switch sort {
		case "popular":
			query = query.Order("cashback_value DESC")
		case "random":
			query = query.Order("RANDOM()")
		case "newest":
			query = query.Order("created_at DESC")
		case "-newest":
			query = query.Order("created_at ASC")
		case "cashback_value":
			query = query.Order("cashback_value DESC")
		case "-cashback_value":
			query = query.Order("cashback_value ASC")
		default:
			query = query.Order("featured DESC, cashback_rate DESC")
		}
	} else {
		query = query.Order("featured DESC, cashback_rate DESC")
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if pageSize, ok := condition["page_size"].(int); ok {
		query = query.Limit(pageSize)
	}

	// 查询所有字段（不使用 Select 限制字段）
	err := query.Preload("Country").Find(&merchants).Error
	if err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get merchants with all fields")
	}

	return merchants, total, nil
}

func (r *merchantPostgresRepository) GetMerchantCount(ctx *gin.Context) (int64, *ecode.Error) {
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&entity.Merchant{})

	// 在应用所有筛选条件后计算总数
	if err := query.Count(&total).Error; err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count merchants")
	}
	return total, nil
}

// CreateMerchant 创建商家
func (r *merchantPostgresRepository) CreateMerchant(ctx *gin.Context, merchant *entity.Merchant) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(merchant).Error; err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create merchant")
	}
	return nil
}

// UpdateMerchant 更新商家
func (r *merchantPostgresRepository) UpdateMerchant(ctx *gin.Context, merchant *entity.Merchant) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(merchant).Error; err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to update merchant")
	}
	return nil
}

// DeleteMerchant 删除商家
func (r *merchantPostgresRepository) DeleteMerchant(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.Merchant{}, id).Error; err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to delete merchant")
	}
	return nil
}

// UpdateMerchantStatus 更新商家状态
func (r *merchantPostgresRepository) UpdateMerchantStatus(ctx *gin.Context, id uint64, status int8) *ecode.Error {
	err := r.db.WithContext(ctx).
		Model(&entity.Merchant{}).
		Where("id = ?", id).
		Update("status", status).Error
	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to update merchant status")
	}
	return nil
}

// UpdateMerchantLogo 只更新商家Logo
func (r *merchantPostgresRepository) UpdateMerchantLogo(ctx *gin.Context, id uint64, logo string) *ecode.Error {
	err := r.db.WithContext(ctx).
		Model(&entity.Merchant{}).
		Where("id = ?", id).
		Update("logo", logo).Error
	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to update merchant logo")
	}
	return nil
}

// BatchUpdateMerchantLogos 批量只更新商家Logo
func (r *merchantPostgresRepository) BatchUpdateMerchantLogos(ctx *gin.Context, merchantLogos map[uint64]string) *ecode.Error {
	if len(merchantLogos) == 0 {
		return nil
	}

	// 开启事务
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return ecode.Wrap(tx.Error, ecode.ErrDatabase.Code, "failed to begin transaction")
	}

	// 确保事务回滚
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 批量更新Logo
	for id, logo := range merchantLogos {
		if err := tx.Model(&entity.Merchant{}).
			Where("id = ?", id).
			Update("logo", logo).Error; err != nil {
			tx.Rollback()
			return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to batch update merchant logos")
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to commit transaction")
	}

	return nil
}

// BatchCreateMerchants 批量创建商家
func (r *merchantPostgresRepository) BatchCreateMerchants(ctx *gin.Context, merchants []*entity.Merchant) *ecode.Error {
	if len(merchants) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).Model([]*entity.Merchant{}).Clauses(clause.OnConflict{DoNothing: true}).CreateInBatches(merchants, 5000).Error
	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to batch create merchants")
	}
	return nil
}

// BatchCreateMerchantsV2 批量创建商家
func (r *merchantPostgresRepository) BatchCreateMerchantsV2(ctx *gin.Context, merchants []*entity.Merchant) *ecode.Error {
	if len(merchants) == 0 {
		return nil
	}

	const (
		batchSize  = 1000 // 每批处理的记录数
		maxRetries = 3    // 最大重试次数
	)

	// 创建一个优化的 Session，使用预处理语句和跳过钩子来提高性能
	session := r.db.WithContext(ctx).Session(&gorm.Session{
		CreateBatchSize:   batchSize,
		PrepareStmt:       true,
		SkipHooks:         true,
		AllowGlobalUpdate: false,
	})

	// 使用事务包装整个批量插入过程
	err := session.Transaction(func(tx *gorm.DB) error {
		// 分批处理数据
		for i := 0; i < len(merchants); i += batchSize {
			end := i + batchSize
			if end > len(merchants) {
				end = len(merchants)
			}

			batch := merchants[i:end]

			// 添加重试机制
			var lastErr error
			for retry := 0; retry < maxRetries; retry++ {
				// 检查上下文是否已取消
				select {
				case <-ctx.Done():
					return ecode.Wrap(ctx.Err(), ecode.ErrDatabase.Code, "context cancelled")
				default:
				}

				// 使用 CreateInBatches 进行批量插入，并设置冲突处理策略
				if err := tx.Clauses(
					clause.OnConflict{
						Columns:   []clause.Column{{Name: "unique_name"}},
						DoNothing: true,
					},
				).CreateInBatches(batch, batchSize).Error; err != nil {
					lastErr = err
					// 检查是否需要重试的错误类型
					if shouldRetry(err) {
						// 指数退避重试
						time.Sleep(time.Millisecond * time.Duration(100*(1<<retry)))
						continue
					}
					// 其他错误直接返回
					return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create merchants batch")
				}

				// 成功则跳出重试循环
				lastErr = nil
				break
			}

			// 如果重试后仍然失败
			if lastErr != nil {
				return ecode.Wrap(lastErr, ecode.ErrDatabase.Code, "failed after max retries")
			}
		}

		return nil
	})

	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to batch create merchants")
	}

	return nil
}

// shouldRetry 判断是否应该重试
func shouldRetry(err error) bool {
	errStr := err.Error()
	retryErrors := []string{
		"deadlock",
		"duplicate key",
		"connection reset",
		"write: broken pipe",
		"no connection to the server",
	}

	for _, retryErr := range retryErrors {
		if strings.Contains(errStr, retryErr) {
			return true
		}
	}

	return false
}

// BatchUpdateMerchants 批量更新商家
func (r *merchantPostgresRepository) BatchUpdateMerchants(ctx *gin.Context, merchants []*entity.Merchant) *ecode.Error {
	if len(merchants) == 0 {
		return nil
	}

	const (
		batchSize  = 1000 // 每批处理的记录数
		maxRetries = 3    // 最大重试次数
	)

	// 创建一个优化的 Session，使用预处理语句和跳过钩子来提高性能
	session := r.db.WithContext(ctx).Session(&gorm.Session{
		PrepareStmt:       true,
		SkipHooks:         true,
		AllowGlobalUpdate: false,
	})

	// 使用事务包装整个批量更新过程
	err := session.Transaction(func(tx *gorm.DB) error {
		// 分批处理数据
		for i := 0; i < len(merchants); i += batchSize {
			end := i + batchSize
			if end > len(merchants) {
				end = len(merchants)
			}

			batch := merchants[i:end]

			// 添加重试机制
			var lastErr error
			for retry := 0; retry < maxRetries; retry++ {
				// 检查上下文是否已取消
				select {
				case <-ctx.Done():
					return ecode.Wrap(ctx.Err(), ecode.ErrDatabase.Code, "context cancelled")
				default:
				}

				// 批量更新商家
				for _, merchant := range batch {
					if err := tx.Save(merchant).Error; err != nil {
						lastErr = err
						// 检查是否需要重试的错误类型
						if shouldRetry(err) {
							// 指数退避重试
							time.Sleep(time.Millisecond * time.Duration(100*(1<<retry)))
							break // 跳出内部循环，重试整个批次
						}
						// 其他错误直接返回
						return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to update merchant batch")
					}
				}

				// 如果没有错误或者不需要重试，跳出重试循环
				if lastErr == nil || !shouldRetry(lastErr) {
					break
				}
			}

			// 如果重试后仍然失败
			if lastErr != nil && shouldRetry(lastErr) {
				return ecode.Wrap(lastErr, ecode.ErrDatabase.Code, "failed after max retries")
			}
		}

		return nil
	})

	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to batch update merchants")
	}

	return nil
}

// GetMerchantListByIDs 批量获取商家信息
func (r *merchantPostgresRepository) GetMerchantListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Merchant, *ecode.Error) {
	var merchants []*entity.Merchant
	err := r.db.WithContext(ctx).
		Select("id, name, unique_name, merchant_code, logo, website, track_url, "+
			"cashback_type, description, category_id, featured, country_id, "+
			"supported_countries, created_at, cashback_value").
		Preload("Country").
		Where("id IN (?)", ids).
		Where("status = ?", constant.StatusActive).
		Find(&merchants).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get merchants")
	}
	return merchants, nil
}
