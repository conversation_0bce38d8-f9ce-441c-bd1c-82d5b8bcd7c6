# BonusEarned - Technical Architecture Documentation

## System Architecture Overview

BonusEarned implements a clean, domain-driven design architecture with clear separation of concerns across multiple layers. This approach ensures maintainability, testability, and scalability of the codebase.

### Architecture Layers

```
[User/Client] → [CDN/WAF] → [Load Balancer]
                                  ↓
                          [Application Servers]
                                  ↓
                     ┌─────────────────────────┐
                     ↓                         ↓
                [API Service]            [Track Service]
                     ↓                         ↓
                     └─────────────────────────┘
                                  ↓
                      [PostgreSQL (Primary/Replica)]
                                  ↓
                           [Redis Cluster]
```

#### 1. Domain Layer (`/domain`)

The core of the application containing business entities and logic.

- **Entities**: Define the core data structures and business rules
- **Repositories**: Define interfaces for data access
- **Services**: Implement core business logic

Example domain entities:
- `Merchant`: Partner retailers offering cashback
- `User`: Platform users earning cashback
- `Order`: Purchase records and cashback calculation
- `Withdrawal`: User cashback withdrawal requests

#### 2. Application Layer (`/application`)

Orchestrates the flow of data and coordinates business operations.

- **Application Services**: Implement use cases by coordinating domain services
- **DTOs**: Data transfer objects for communication between layers

#### 3. Infrastructure Layer (`/infra`)

Provides technical implementations and external integrations.

- **Database**: PostgreSQL and Redis implementations
- **External Services**: Affiliate network integrations
- **Utilities**: Common helper functions and tools

#### 4. Interface Layer (`/interfaces`)

Handles external communication and request processing.

- **API Handlers**: Process HTTP requests and responses
- **Middleware**: Authentication, logging, error handling
- **Routers**: Define API endpoints and routes

### Services

#### API Service (`cmd/api`)

Handles all user-facing operations:
- User authentication and management
- Merchant discovery and browsing
- Order and cashback management
- Withdrawal processing

#### Track Service (`cmd/track`)

Manages tracking and attribution:
- Click tracking and redirection
- Order attribution
- Affiliate link generation

#### Task Service (`cmd/task`)

Handles scheduled operations:
- Order status synchronization
- Merchant data updates
- Automated cashback processing

## Database Design

### PostgreSQL Schema

The database uses a normalized schema with the following key tables:

#### `merchants`
```sql
CREATE TABLE merchants (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    unique_name VARCHAR(255) NOT NULL,
    logo VARCHAR(255) NOT NULL,
    website VARCHAR(255),
    description TEXT,
    merchant_code VARCHAR(32) NOT NULL,
    commission_type SMALLINT,
    commission_value DECIMAL(10,2),
    region VARCHAR(50) NOT NULL,
    supported_regions JSONB,
    category_id BIGINT NOT NULL,
    platform_merchant_id VARCHAR(64) NOT NULL,
    platform_type SMALLINT NOT NULL,
    cashback_rate DECIMAL(10,2) DEFAULT 0.8 NOT NULL,
    status SMALLINT DEFAULT 1,
    display_status SMALLINT DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE
)
```

#### `users`
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    user_code VARCHAR(50) NOT NULL UNIQUE,
    phone VARCHAR(30),
    password VARCHAR(255) NOT NULL,
    nickname VARCHAR(50),
    payment_info JSONB DEFAULT '{}',
    user_balance JSONB DEFAULT '{}',
    status SMALLINT DEFAULT 1 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
```

#### `orders`
```sql
CREATE TABLE orders (
    id BIGSERIAL PRIMARY KEY,
    conversion_id VARCHAR(255) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    user_code VARCHAR(50) NOT NULL,
    merchant_id BIGINT NOT NULL,
    merchant_code VARCHAR(50) NOT NULL,
    order_id VARCHAR(50) NOT NULL,
    order_amount DECIMAL(10,2) NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL,
    cashback_amount DECIMAL(10,2) NOT NULL,
    status SMALLINT DEFAULT 1 NOT NULL,
    order_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
```

### Redis Usage

Redis is used for:
- Caching frequently accessed data
- Session management
- Rate limiting
- Temporary data storage

## API Design

### RESTful API Endpoints

The API follows RESTful principles with the following main endpoints:

#### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout

#### Merchants
- `GET /api/v1/merchants` - List merchants
- `GET /api/v1/merchants/:id` - Get merchant details
- `GET /api/v1/merchants/categories` - List merchant categories

#### Orders
- `GET /api/v1/orders` - List user orders
- `GET /api/v1/orders/:id` - Get order details

#### Withdrawals
- `POST /api/v1/withdrawals` - Create withdrawal request
- `GET /api/v1/withdrawals` - List user withdrawals

#### Tracking
- `GET /t/:merchant_code/:user_code` - Track click and redirect

### Response Format

All API responses follow a consistent format:

```json
{
  "code": 0,           // Error code, 0 means success
  "message": "string", // Error message
  "data": {}           // Response data
}
```

## Cashback Tracking Flow

1. **Click Tracking**:
   - User clicks on a merchant link on BonusEarned
   - System generates a tracking URL with user and merchant identifiers
   - Click is recorded in the database
   - User is redirected to the merchant website

2. **Order Processing**:
   - Merchant reports purchase to affiliate network
   - Affiliate network sends order data to BonusEarned via API or webhook
   - System matches order with click record using tracking parameters
   - Order is created and associated with the user

3. **Cashback Calculation**:
   - System calculates cashback based on commission amount and cashback rate
   - Cashback is marked as pending until confirmation
   - After merchant's return period, cashback is confirmed and added to user balance

4. **Withdrawal Processing**:
   - User requests withdrawal of available balance
   - System validates request and initiates payment
   - Payment is processed through configured payment method
   - Withdrawal status is updated accordingly

## Security Implementation

- **Authentication**: JWT-based token authentication with secure storage
- **Authorization**: Role-based access control for API endpoints
- **Data Protection**: Encryption for sensitive data in transit and at rest
- **Input Validation**: Thorough validation of all user inputs
- **Rate Limiting**: Protection against brute force and DoS attacks
- **Audit Logging**: Comprehensive logging of security-relevant events

## Deployment Architecture

The application is containerized using Docker and deployed with Kubernetes:

```
[Kubernetes Cluster]
  ├── [API Service Pods] × 3+
  ├── [Track Service Pods] × 3+
  ├── [Task Service Pods] × 2
  ├── [PostgreSQL StatefulSet]
  │     ├── Primary × 1
  │     └── Replicas × 2
  └── [Redis StatefulSet]
        ├── Master × 1
        └── Replicas × 2
```

## Monitoring and Observability

- **Logging**: Structured JSON logs with Zap logger
- **Metrics**: Prometheus for system and application metrics
- **Dashboards**: Grafana for visualization
- **Alerts**: Configured for critical system events
- **Tracing**: Distributed tracing for request flows
