# BonusEarned Developer Guide

## Development Environment Setup

### Prerequisites

Before you begin, ensure you have the following installed:

- Go 1.21 or higher
- Node.js 18 or higher
- npm or pnpm
- PostgreSQL 14 or higher
- Redis 6 or higher
- Docker and Docker Compose (optional, for containerized development)
- Git

### Repository Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/bonusearned.git
   cd bonusearned
   ```

2. Install backend dependencies:
   ```bash
   go mod download
   ```

3. Install frontend dependencies:
   ```bash
   cd frontend-react
   npm install
   cd ..
   ```

### Configuration

1. Copy the example configuration files:
   ```bash
   cp configs/local/api.example.yaml configs/local/api.yaml
   cp configs/local/track.example.yaml configs/local/track.yaml
   cp configs/local/common.example.yaml configs/local/common.yaml
   ```

2. Update the configuration files with your local database and Redis settings.

### Database Setup

1. Create the PostgreSQL database:
   ```bash
   createdb bonusearned
   ```

2. Run database migrations:
   ```bash
   make migrate
   ```

### Running the Application

#### Backend Services

1. Start the API service:
   ```bash
   make run-api
   ```

2. Start the Track service:
   ```bash
   make run-track
   ```

3. Start the Task service:
   ```bash
   make run-task
   ```

#### Frontend Development

1. Start the frontend development server:
   ```bash
   cd frontend-react
   npm run dev
   ```

2. Access the application at `http://localhost:5173`

### Using Docker Compose (Alternative)

1. Build and start all services:
   ```bash
   docker-compose up -d
   ```

2. Access the application at `http://localhost:3000`

## Project Structure

### Backend Structure

```
bonusearned/
├── cmd/                  # Main entry points
│   ├── api/             # API service
│   ├── track/           # Tracking service
│   └── task/            # Scheduled tasks
├── configs/             # Configuration files
├── domain/              # Domain layer (core business logic)
│   ├── merchant/        # Merchant domain
│   │   ├── entity/      # Domain entities
│   │   ├── repository/  # Repository interfaces
│   │   └── service/     # Domain services
│   ├── user/            # User domain
│   ├── order/           # Order domain
│   └── ...              # Other domains
├── application/         # Application services
│   ├── merchant/        # Merchant application services
│   │   ├── appservice/  # Application service implementations
│   │   └── dto/         # Data transfer objects
│   └── ...              # Other application services
├── infra/               # Infrastructure implementations
│   ├── database/        # Database connections
│   ├── persistence/     # Repository implementations
│   └── ...              # Other infrastructure
└── interfaces/          # API interfaces and handlers
    ├── api/             # API handlers
    │   ├── handler/     # Request handlers
    │   ├── middleware/  # HTTP middleware
    │   └── router/      # Route definitions
    └── task/            # Task handlers
```

### Frontend Structure

```
frontend-react/
├── public/              # Static assets
├── src/
│   ├── components/      # Reusable UI components
│   │   ├── common/      # Common components
│   │   ├── layout/      # Layout components
│   │   └── ...          # Feature-specific components
│   ├── contexts/        # React context providers
│   ├── hooks/           # Custom React hooks
│   ├── pages/           # Page components
│   ├── services/        # API service clients
│   ├── styles/          # Global styles
│   ├── types/           # TypeScript type definitions
│   ├── utils/           # Utility functions
│   ├── App.tsx          # Main application component
│   └── main.tsx         # Application entry point
└── ...                  # Configuration files
```

## Development Workflow

### Backend Development

#### Creating a New Domain Entity

1. Define the entity in `domain/{domain}/entity/{entity_name}.go`
2. Create repository interface in `domain/{domain}/repository/{entity_name}_repository.go`
3. Implement domain service in `domain/{domain}/service/{entity_name}_service.go`
4. Create DTOs in `application/{domain}/dto/{entity_name}_dto.go`
5. Implement application service in `application/{domain}/appservice/{entity_name}_app.go`
6. Implement repository in `infra/persistence/{entity_name}_repository.go`
7. Create API handler in `interfaces/api/handler/{domain}/{entity_name}_handler.go`
8. Add routes in `interfaces/api/router/router.go`

#### Adding a New API Endpoint

1. Define the handler method in the appropriate handler file
2. Add the route in `interfaces/api/router/router.go`
3. Implement the business logic in the application service
4. Update the domain service if needed

### Frontend Development

#### Creating a New Page

1. Create a new page component in `frontend-react/src/pages/`
2. Add the route in `frontend-react/src/main.tsx`
3. Implement the page UI and logic

#### Creating a New Component

1. Create the component in `frontend-react/src/components/`
2. Use TailwindCSS for styling
3. Implement any required logic or state

## Testing

### Backend Testing

1. Write unit tests for domain services:
   ```bash
   cd domain/{domain}/service
   go test -v
   ```

2. Write integration tests for repositories:
   ```bash
   cd infra/persistence
   go test -v
   ```

3. Run all tests:
   ```bash
   go test ./...
   ```

### Frontend Testing

1. Run unit tests:
   ```bash
   cd frontend-react
   npm test
   ```

2. Run end-to-end tests:
   ```bash
   cd frontend-react
   npm run test:e2e
   ```

## Building for Production

### Backend Build

```bash
make build
```

This will create binaries in the `bin/` directory.

### Frontend Build

```bash
cd frontend-react
npm run build:production
```

This will create optimized production files in the `frontend-react/dist/` directory.

## Deployment

### Docker Deployment

1. Build the Docker images:
   ```bash
   docker build -f Dockerfile.api -t bonusearned-api .
   docker build -f Dockerfile.track -t bonusearned-track .
   docker build -f Dockerfile.frontend -t bonusearned-frontend .
   ```

2. Push the images to your container registry:
   ```bash
   docker push your-registry/bonusearned-api
   docker push your-registry/bonusearned-track
   docker push your-registry/bonusearned-frontend
   ```

3. Deploy using Docker Compose or Kubernetes

### Kubernetes Deployment

1. Apply the Kubernetes manifests:
   ```bash
   kubectl apply -f k8s/
   ```

## Coding Standards

### Go Code Standards

- Follow the [Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments)
- Use meaningful variable and function names
- Write comprehensive comments for public functions
- Implement proper error handling
- Use the project's custom error package (`infra/ecode`)

### TypeScript/React Standards

- Follow the [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
- Use functional components with hooks
- Implement proper TypeScript typing
- Use meaningful component and variable names
- Write JSDoc comments for functions and components

## Contribution Guidelines

1. Create a feature branch from `develop`:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make your changes and commit them:
   ```bash
   git commit -m "feat: add your feature description"
   ```

3. Push your branch and create a pull request:
   ```bash
   git push origin feature/your-feature-name
   ```

4. Ensure all tests pass and code meets the project standards
5. Request a code review from a team member
6. Address any feedback and update your pull request
7. Once approved, your changes will be merged into `develop`

## Troubleshooting

### Common Issues

- **Database Connection Issues**: Verify PostgreSQL is running and credentials are correct
- **Redis Connection Issues**: Ensure Redis is running and accessible
- **Frontend Build Errors**: Check for TypeScript errors or missing dependencies
- **API Errors**: Check the API logs for detailed error messages

### Getting Help

If you encounter issues not covered in this guide, please:
1. Check the project documentation
2. Review existing GitHub issues
3. Ask for help in the project's communication channels
4. Create a new GitHub issue with detailed information about the problem

## Resources

- [Go Documentation](https://golang.org/doc/)
- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Redis Documentation](https://redis.io/documentation)
