# 商家同步国家过滤功能

## 概述

本文档描述了商家同步服务中的国家过滤功能。该功能确保只有在我们支持的国家列表中的商家才会被同步到数据库中。

## 功能特性

### 1. 严格的国家过滤
- 只同步在 `CountryCodeToName` 或 `CountryNameToCode` 映射表中的国家
- 不在支持列表中的国家将被跳过，不会同步到数据库
- 空值或无效值也会被跳过

### 2. 智能国家识别
- 支持国家代码格式（如：US, UK, CA）
- 支持国家名称格式（如：United States, United Kingdom, Canada）
- 自动进行代码和名称之间的转换

### 3. 支持的国家列表
目前支持50个国家，包括：
- 美国 (US / United States)
- 英国 (UK / United Kingdom)
- 加拿大 (CA / Canada)
- 澳大利亚 (AU / Australia)
- 德国 (DE / Germany)
- 法国 (FR / France)
- 日本 (JP / Japan)
- 中国 (CN / China)
- 等等...

## 技术实现

### 1. 常量映射
位置：`infra/constant/country.go`

```go
// 国家代码到名称的映射
var CountryCodeToName = map[string]string{
    "US": "United States",
    "UK": "United Kingdom",
    // ... 其他国家
}

// 国家名称到代码的映射
var CountryNameToCode = map[string]string{
    "United States": "US",
    "United Kingdom": "UK",
    // ... 其他国家
}
```

### 2. 同步逻辑
位置：`domain/task/service/sync_service.go`

```go
// 在 toEntityCreateMerchant 函数中
countryValue := data["country"].(string)
countryID, shouldSync := s.getCountryIDByCodeOrName(ctx, countryValue)
if !shouldSync {
    // 国家不在支持列表中，跳过该商家
    continue
}
merchant.CountryID = countryID
```

### 3. 国家验证函数
```go
func (s *syncService) getCountryIDByCodeOrName(ctx *gin.Context, countryValue string) (uint64, bool) {
    if countryValue == "" {
        return 0, false // 空值不同步
    }

    // 检查是否为有效的国家代码
    if constant.IsValidCountryCode(countryValue) {
        // 获取国家ID并返回
    }

    // 检查是否为有效的国家名称
    if constant.IsValidCountryName(countryValue) {
        // 转换为代码并获取国家ID
    }

    // 都找不到则不同步
    return 0, false
}
```

## 同步行为

### 会被同步的情况
- 国家值为有效的国家代码（如："US", "UK", "CA"）
- 国家值为有效的国家名称（如："United States", "United Kingdom", "Canada"）

### 会被跳过的情况
- 国家值为空字符串
- 国家值不在支持的映射表中
- 国家值为无效格式

## 示例

### 同步示例
```json
// 这些商家会被同步
{"name": "Amazon", "country": "US"}           // ✅ 有效代码
{"name": "ASOS", "country": "United Kingdom"} // ✅ 有效名称
{"name": "Shopify", "country": "CA"}          // ✅ 有效代码
```

### 跳过示例
```json
// 这些商家会被跳过
{"name": "Unknown Store", "country": "XX"}           // ❌ 无效代码
{"name": "Test Store", "country": "Unknown Country"} // ❌ 无效名称
{"name": "Empty Store", "country": ""}               // ❌ 空值
```

## 日志记录

同步过程中会记录以下信息：
- 获取到的商家总数
- 转换后的商家数量（跳过不支持国家后的数量）
- 批量创建的商家数量

## 维护指南

### 添加新国家
1. 在 `infra/constant/country.go` 中添加新的映射关系
2. 在数据库中添加对应的国家记录
3. 重新部署服务

### 移除国家支持
1. 从 `infra/constant/country.go` 中移除映射关系
2. 考虑现有数据的处理方案
3. 重新部署服务

## 注意事项

1. **数据一致性**：确保常量映射与数据库中的国家数据保持一致
2. **性能考虑**：使用内存映射避免频繁的数据库查询
3. **向后兼容**：修改映射时要考虑现有数据的兼容性
4. **错误处理**：对于无效的国家值，系统会优雅地跳过而不是报错

## 测试

可以使用以下命令测试国家过滤功能：
```bash
go run test_sync_country_filter.go
```

该测试会验证各种国家值的处理逻辑，确保过滤功能正常工作。
