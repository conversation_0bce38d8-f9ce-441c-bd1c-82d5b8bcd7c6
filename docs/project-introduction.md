# BonusEarned - Premium Cashback Platform

## Project Overview

BonusEarned is a sophisticated cashback service platform designed for Western markets, enabling users to earn cashback rewards on purchases made through partner merchants. The platform serves as an intermediary between shoppers and online retailers, offering users a percentage of their purchase amount back as cash rewards.

## Target Audience

- Online shoppers in Western markets (primarily US and Europe)
- Value-conscious consumers looking to maximize savings
- Frequent online shoppers who make regular purchases from major retailers

## Problem Statement

Traditional shopping experiences don't reward customer loyalty effectively. Consumers often miss out on potential savings when shopping online, while merchants struggle to attract and retain customers in a competitive e-commerce landscape. BonusEarned bridges this gap by:

1. Providing consumers with cashback incentives for their online purchases
2. Offering merchants increased visibility and customer acquisition opportunities
3. Creating a win-win ecosystem where all parties benefit from the transaction flow

## Unique Value Proposition

Unlike standard cashback platforms, BonusEarned offers:

- **Premium User Experience**: Clean, elegant interface with a purple-themed design that conveys trust and quality
- **Higher Cashback Rates**: Up to 80% of affiliate commissions are passed back to users
- **Transparent Tracking**: Real-time order tracking and detailed cashback calculation
- **Diverse Merchant Network**: Access to tens of thousands of merchants across various categories
- **Multi-regional Support**: Tailored merchant offerings based on user location

## Technical Architecture

### Technology Stack

#### Frontend
- **Framework**: React 19.0.0 with TypeScript
- **Build Tool**: Vite
- **Styling**: TailwindCSS with custom theme configuration
- **State Management**: React Context API and custom hooks
- **Routing**: React Router v6
- **Animation**: Framer Motion, GSAP
- **UI Components**: Custom component library with Headless UI integration
- **API Client**: Axios
- **Form Handling**: React Hook Form with Yup validation

#### Backend
- **Language**: Go 1.23.7
- **Web Framework**: Gin
- **ORM**: GORM
- **Database**: PostgreSQL
- **Caching**: Redis
- **Authentication**: JWT
- **API Documentation**: OpenAPI/Swagger
- **Task Scheduling**: Cron jobs via robfig/cron

#### Infrastructure
- **Containerization**: Docker
- **Orchestration**: Kubernetes
- **CI/CD**: GitHub Actions
- **Cloud Storage**: Cloudflare R2
- **CDN**: Cloudflare
- **Monitoring**: Prometheus, Grafana

### System Architecture

The system follows a clean, domain-driven design architecture with clear separation of concerns:

1. **Domain Layer**: Core business logic and entities
2. **Application Layer**: Orchestrates business processes
3. **Infrastructure Layer**: Technical implementations and external integrations
4. **Interface Layer**: API endpoints and request handling

The application is divided into two main services:
- **API Service**: Handles all user-facing operations
- **Track Service**: Manages click tracking and order attribution

### Database Design

The database uses PostgreSQL with a well-normalized schema including:

- **merchants**: Store information about partner retailers
- **users**: User accounts and profile data
- **orders**: Purchase tracking and cashback calculation
- **withdrawals**: User cashback withdrawal requests
- **coupons**: Promotional offers from merchants
- **categories**: Merchant categorization
- **click_records**: User click tracking for attribution

## Key Features

### For Shoppers
- User registration and authentication
- Merchant discovery and browsing
- Cashback tracking and history
- Withdrawal management
- Coupon and promotional offers
- Personalized merchant recommendations

### For Merchants
- Merchant profile and branding
- Performance analytics
- Promotional campaign management
- Customizable cashback rates
- Regional targeting options

### Technical Features
- Secure tracking link generation
- Affiliate network integration
- Real-time order status updates
- Automated cashback calculation
- Fraud prevention mechanisms
- SEO optimization with sitemaps and robots.txt

## Cashback Calculation System

BonusEarned uses a sophisticated cashback calculation system:

- **Base**: Commission amount from affiliate networks (not order amount)
- **Calculation Methods**:
  - Percentage-based: Commission amount × Cashback rate
  - Fixed amount: Predetermined cashback per order
- **Configuration Priority**:
  1. User-specific merchant settings
  2. Merchant default settings
  3. System default (80% of commission)

## Deployment Strategy

The application is containerized using Docker and deployed with the following environments:

- **Development**: Local development environment
- **Testing**: Staging environment for QA
- **Production**: Multi-region deployment with high availability

## Security Measures

- HTTPS encryption for all communications
- JWT-based authentication
- Parameter validation and sanitization
- SQL injection protection
- XSS and CSRF prevention
- Data encryption for sensitive information
- Access control and permission management

## Project Structure

```
bonusearned/
├── cmd/                  # Main entry points
│   ├── api/             # API service
│   ├── track/           # Tracking service
│   └── task/            # Scheduled tasks
├── configs/             # Configuration files
├── domain/              # Domain layer (core business logic)
│   ├── merchant/
│   ├── user/
│   ├── order/
│   └── ...
├── application/         # Application services
├── infra/               # Infrastructure implementations
├── interfaces/          # API interfaces and handlers
├── frontend-react/      # React frontend application
└── docs/                # Documentation
```

## Getting Started

See the [README.md](../README.md) for detailed setup instructions.

## Future Roadmap

- Mobile application development
- Browser extension for automatic cashback
- Advanced analytics dashboard
- Loyalty program implementation
- Expanded payment options for withdrawals
- AI-powered shopping recommendations
