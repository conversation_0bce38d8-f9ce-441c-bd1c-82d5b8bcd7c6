# 用户余额同步功能

## 概述

在 `SyncOrderList` 函数中集成了用户余额同步功能，确保用户的余额信息能够根据订单状态的变化实时更新。

## 功能特性

### 1. 自动余额计算
- **总金额 (TotalAmount)**: 所有非取消状态订单的返现金额总和
- **待确认金额 (PendingAmount)**: 状态为 `OrderStatusPending` 的订单返现金额
- **已确认金额 (ApprovedAmount)**: 状态为 `OrderStatusApproved` 的订单返现金额  
- **已支付金额 (PaidAmount)**: 状态为 `OrderStatusPaid` 的订单返现金额

### 2. 批量更新优化
- 使用批量更新方法 `BatchUpdateUserBalance` 提高性能
- 分批处理，每批最多1000条记录，避免数据库压力
- 事务保证数据一致性

### 3. 缓存管理
- 更新用户余额后自动清理相关缓存
- 包括本地缓存和Redis缓存的清理

## 实现细节

### 新增的方法

#### 1. UserRepository 接口
```go
// BatchUpdateUserBalance 批量更新用户余额（仅供定时任务调用）
BatchUpdateUserBalance(ctx *gin.Context, users []*entity.User) *ecode.Error
```

#### 2. UserService 接口
```go
// BatchUpdateUserBalance 批量更新用户余额（仅供定时任务调用）
BatchUpdateUserBalance(ctx *gin.Context, users []*entity.User) *ecode.Error
```

#### 3. SyncService 实现
```go
// syncUserBalances 同步用户余额信息
func (s *syncService) syncUserBalances(ctx *gin.Context) *ecode.Error
```

### 数据结构

#### UserBalance 结构
```go
type UserBalance struct {
    TotalAmount    float64 `json:"total_amount"`    // 总金额
    PendingAmount  float64 `json:"pending_amount"`  // 待确认金额
    PaidAmount     float64 `json:"paid_amount"`     // 已支付金额
    ApprovedAmount float64 `json:"approved_amount"` // 已确认金额
}
```

### 订单状态映射

| 订单状态 | 常量 | 余额字段 | 说明 |
|---------|------|----------|------|
| 待确认 | `OrderStatusPending` | PendingAmount | 订单已创建，等待确认 |
| 已确认 | `OrderStatusApproved` | ApprovedAmount | 订单已确认，等待支付 |
| 已取消 | `OrderStatusRejected` | - | 不计入任何余额 |
| 支付中 | `OrderStatusPaying` | ApprovedAmount | 视为已确认状态 |
| 已结算 | `OrderStatusPaid` | PaidAmount | 已完成支付 |

## 执行流程

### SyncOrderList 执行流程
1. 同步订单信息（创建/更新订单）
2. 调用 `syncUserBalances` 同步用户余额
3. 计算所有用户的余额信息
4. 批量更新用户余额到数据库
5. 清理相关缓存

### syncUserBalances 详细流程
1. 获取所有用户列表
2. 初始化每个用户的余额为0
3. 获取所有订单列表
4. 遍历订单，根据状态累加到对应用户的余额字段
5. 将余额数据转换为JSON格式
6. 批量更新用户余额到数据库
7. 清理用户相关缓存

## 性能优化

### 1. 批量操作
- 使用批量查询获取用户和订单数据
- 使用批量更新减少数据库交互次数
- 分批处理避免单次操作数据量过大

### 2. 内存优化
- 使用map结构快速查找和累加余额
- 避免重复查询数据库

### 3. 事务保证
- 批量更新使用事务确保数据一致性
- 失败时自动回滚

## 测试验证

### 测试用例
1. **TestUserBalanceCalculation**: 验证余额计算逻辑正确性
2. **TestUserBalanceJSONConversion**: 验证JSON转换功能
3. **TestEmptyUserBalance**: 验证空余额处理

### 运行测试
```bash
go test -v configs/test/sync_user_balance_test.go
```

## 使用方式

### 手动触发
用户余额同步会在每次执行 `SyncOrderList` 时自动触发，无需额外操作。

### 监控日志
- 成功日志: "批量更新用户余额成功"
- 错误日志: "同步用户余额失败"

## 注意事项

1. **数据一致性**: 确保在订单同步完成后再执行余额同步
2. **性能考虑**: 大量用户时建议在低峰期执行
3. **错误处理**: 余额同步失败不会影响订单同步的执行
4. **缓存清理**: 更新后会自动清理相关缓存，确保数据实时性

## 未来优化建议

1. **增量更新**: 只更新有订单变化的用户余额
2. **异步处理**: 将余额同步改为异步任务
3. **分布式锁**: 在多实例环境下使用分布式锁避免并发问题
4. **监控告警**: 添加余额同步的监控和告警机制
