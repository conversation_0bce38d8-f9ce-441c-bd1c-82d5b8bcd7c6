<svg width="300" height="80" viewBox="0 0 300 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义渐变色 -->
  <defs>
    <linearGradient id="coinGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#FFB74D"/>
      <stop offset="100%" stop-color="#FF7A45"/>
    </linearGradient>
  </defs>

  <!-- 金币图标组 -->
  <g transform="translate(20, 15)">
    <!-- 主金币 -->
    <circle cx="25" cy="25" r="25" fill="url(#coinGradient)">
      <animate attributeName="r" values="25;26;25" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 金币纹理 -->
    <circle cx="25" cy="25" r="20" stroke="#FFFFFF" stroke-width="2" fill="none" opacity="0.6"/>
    <text x="25" y="32" font-family="Arial" font-size="24" font-weight="bold" fill="white" text-anchor="middle">$</text>
    
    <!-- 返现箭头 -->
    <path d="M60 15 C 70 15, 80 25, 70 35 L 80 35 M 70 35 L 60 35" 
          stroke="#FF7A45" 
          stroke-width="3" 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          fill="none">
      <animate attributeName="stroke-dasharray" 
               values="0,100;100,0;0,100" 
               dur="2s" 
               repeatCount="indefinite"/>
    </path>
  </g>

  <!-- 文字部分 -->
  <g transform="translate(110, 45)">
    <!-- Bonus -->
    <text font-family="Arial, sans-serif" 
          font-size="32" 
          font-weight="bold" 
          fill="#FF7A45"
          letter-spacing="1">
      Bonus
    </text>
    
    <!-- Earned -->
    <text x="110" 
          font-family="Arial, sans-serif" 
          font-size="32" 
          font-weight="bold" 
          fill="#FFB74D"
          letter-spacing="1">
      Earned
    </text>
  </g>

  <!-- 装饰性光效 -->
  <circle cx="25" cy="40" r="35" fill="url(#coinGradient)" opacity="0.1">
    <animate attributeName="r" 
             values="35;37;35" 
             dur="2s" 
             repeatCount="indefinite"/>
  </circle>
</svg>