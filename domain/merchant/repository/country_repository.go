package repository

import (
	"bonusearned/domain/merchant/entity"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// CountryRepository 国家仓储接口
type CountryRepository interface {
	// CreateCountry 创建国家
	CreateCountry(ctx *gin.Context, country *entity.Country) *ecode.Error
	// UpdateCountry 更新国家
	UpdateCountry(ctx *gin.Context, country *entity.Country) *ecode.Error
	// GetCountryDetailById 根据ID获取国家详情
	GetCountryDetailById(ctx *gin.Context, id uint64) (*entity.Country, *ecode.Error)
	// GetCountryDetailByCode 根据代码获取国家详情
	GetCountryDetailByCode(ctx *gin.Context, code string) (*entity.Country, *ecode.Error)
	// GetCountryListByCondition 获取国家列表
	GetCountryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Country, int64, *ecode.Error)
	// DeleteCountry 删除国家
	DeleteCountry(ctx *gin.Context, id uint64) *ecode.Error
}
