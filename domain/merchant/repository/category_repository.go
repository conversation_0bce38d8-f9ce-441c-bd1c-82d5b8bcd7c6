package repository

import (
	"bonusearned/domain/merchant/entity"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// CategoryRepository 商家分类仓储接口
type CategoryRepository interface {
	// CreateCategory 创建商家分类
	CreateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error
	// UpdateCategory 更新商家分类
	UpdateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error
	// GetCategoryDetailById 根据ID获取商家分类
	GetCategoryDetailById(ctx *gin.Context, id uint64) (*entity.Category, *ecode.Error)
	// GetCategoryDetailByName 根据名称获取商家分类
	GetCategoryDetailByName(ctx *gin.Context, name string) (*entity.Category, *ecode.Error)
	// GetCategoryListByCondition 获取商家分类列表
	GetCategoryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Category, int64, *ecode.Error)
}
