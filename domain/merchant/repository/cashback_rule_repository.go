package repository

import (
	"bonusearned/domain/merchant/entity"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// CashbackRuleRepository 商家返现规则仓储接口
type CashbackRuleRepository interface {
	// CreateCashbackRule 创建商家返现规则
	CreateCashbackRule(ctx *gin.Context, rule *entity.CashbackRule) *ecode.Error
	// UpdateCashbackRule 更新商家返现规则
	UpdateCashbackRule(ctx *gin.Context, rule *entity.CashbackRule) *ecode.Error
	// GetCashbackRuleDetailByID 根据ID获取商家返现规则
	GetCashbackRuleDetailByID(ctx *gin.Context, id uint64) (*entity.CashbackRule, *ecode.Error)
	// GetCashbackRuleByUserAndMerchant 根据用户ID和商家ID获取商家返现规则
	GetCashbackRuleByUserAndMerchant(ctx *gin.Context, userID, merchantID uint64) (*entity.CashbackRule, *ecode.Error)
	// GetCashbackGlobalRuleByUserId 获取用户的全局返现规则
	GetCashbackGlobalRuleByUserId(ctx *gin.Context, userID uint64) (*entity.CashbackRule, *ecode.Error)
	// GetCashbackRuleList 获取所有返现规则
	GetCashbackRuleList(ctx *gin.Context, condition map[string]interface{}) ([]*entity.CashbackRule, int64, *ecode.Error)
	// DeleteCashbackRule 删除返现规则
	DeleteCashbackRule(ctx *gin.Context, id uint64) *ecode.Error
}
