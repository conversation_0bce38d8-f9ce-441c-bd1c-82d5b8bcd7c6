package repository

import (
	"bonusearned/domain/merchant/entity"
	"bonusearned/infra/ecode"

	"github.com/gin-gonic/gin"
)

// MerchantRepository 商家仓储接口
type MerchantRepository interface {
	GetMerchantDetailById(ctx *gin.Context, id uint64) (*entity.Merchant, *ecode.Error)
	GetMerchantDetailByCode(ctx *gin.Context, merchantCode string) (*entity.Merchant, *ecode.Error)
	GetMerchantAffiliateLinkByCode(ctx *gin.Context, merchantCode string) (*entity.Merchant, *ecode.Error)
	GetMerchantDetailByUniqueName(ctx *gin.Context, uniqueName string) (*entity.Merchant, *ecode.Error)

	GetMerchantListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Merchant, int64, *ecode.Error)
	// GetMerchantListByConditionWithAllFields 根据条件获取商家列表（返回所有字段）
	GetMerchantListByConditionWithAllFields(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Merchant, int64, *ecode.Error)
	GetMerchantCount(ctx *gin.Context) (int64, *ecode.Error)
	// GetMerchantListByIDs 批量获取商家信息
	GetMerchantListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Merchant, *ecode.Error)

	CreateMerchant(ctx *gin.Context, merchant *entity.Merchant) *ecode.Error
	UpdateMerchant(ctx *gin.Context, merchant *entity.Merchant) *ecode.Error
	DeleteMerchant(ctx *gin.Context, id uint64) *ecode.Error
	UpdateMerchantStatus(ctx *gin.Context, id uint64, status int8) *ecode.Error
	// UpdateMerchantLogo 只更新商家Logo
	UpdateMerchantLogo(ctx *gin.Context, id uint64, logo string) *ecode.Error
	// BatchUpdateMerchantLogos 批量只更新商家Logo
	BatchUpdateMerchantLogos(ctx *gin.Context, merchantLogos map[uint64]string) *ecode.Error

	// BatchCreateMerchants 批量创建商家
	BatchCreateMerchants(ctx *gin.Context, merchants []*entity.Merchant) *ecode.Error
	// BatchUpdateMerchants 批量更新商家
	BatchUpdateMerchants(ctx *gin.Context, merchants []*entity.Merchant) *ecode.Error

	BatchCreateMerchantsV2(ctx *gin.Context, merchants []*entity.Merchant) *ecode.Error
}
