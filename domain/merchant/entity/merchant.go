package entity

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// JSONMap 用于处理 PostgreSQL 的 jsonb 类型
type JSONMap map[string]string

// Value 实现 driver.Valuer 接口
func (m JSONMap) Value() (driver.Value, error) {
	if m == nil {
		return json.Marshal(map[string]string{})
	}
	return json.Marshal(m)
}

// Scan 实现 sql.Scanner 接口
func (m *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*m = make(JSONMap)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("failed to unmarshal JSONB value")
	}

	err := json.Unmarshal(bytes, &m)
	if err != nil {
		return err
	}

	return nil
}

// StringArray 用于处理 PostgreSQL 的字符串数组类型
type StringArray []string

// Value 实现 driver.Valuer 接口
func (a StringArray) Value() (driver.Value, error) {
	if len(a) == 0 {
		return "{}", nil
	}
	return "{" + strings.Join(a, ",") + "}", nil
}

// Scan 实现 sql.Scanner 接口
func (a *StringArray) Scan(value interface{}) error {
	if value == nil {
		*a = StringArray{}
		return nil
	}

	str, ok := value.(string)
	if !ok {
		bytes, ok := value.([]byte)
		if !ok {
			return errors.New("failed to unmarshal StringArray value")
		}
		str = string(bytes)
	}

	str = strings.Trim(str, "{}")
	if str == "" {
		*a = StringArray{}
		return nil
	}

	*a = strings.Split(str, ",")
	return nil
}

// Merchant 商家实体
type Merchant struct {
	ID                 uint64          `gorm:"primarykey" json:"id"`
	Name               string          `gorm:"type:varchar(255);not null" json:"name"`                                                                       // 商家名称
	UniqueName         string          `gorm:"type:varchar(255);uniqueIndex;not null" json:"unique_name"`                                                    // 商家唯一名称
	MerchantCode       string          `gorm:"type:varchar(64);uniqueIndex;index:idx_merchant_code_status_deleted,priority:1;not null" json:"merchant_code"` // 商家编码
	Logo               string          `gorm:"type:varchar(1024)" json:"logo"`                                                                               // 商家logo
	Website            string          `gorm:"type:varchar(1024)" json:"website"`                                                                            // 商家网站
	OriginalDomain     string          `gorm:"type:varchar(1024)" json:"original_domain"`                                                                    // 商家网站
	TrackURL           string          `gorm:"type:varchar(255);not null" json:"track_url"`                                                                  // 跟踪链接
	ParamMappings      JSONMap         `gorm:"type:jsonb;not null;default:'{}'" json:"param_mappings"`
	Description        string          `gorm:"type:text" json:"description"`                                                                     // 商家描述
	Status             int8            `gorm:"type:smallint;default:1;index:idx_merchant_code_status_deleted,priority:2;not null" json:"status"` // 状态
	CategoryID         uint64          `gorm:"not null" json:"category_id"`                                                                      // 分类ID
	Featured           bool            `gorm:"default:false;not null" json:"featured"`                                                           // 是否推荐：如果有单，则为 true
	CountryID          uint64          `gorm:"not null;default:1" json:"country_id"`                                                             // 国家ID
	Country            *Country        `gorm:"foreignKey:CountryID" json:"country,omitempty"`                                                    // 国家信息
	SupportedCountries StringArray     `gorm:"type:varchar[];default:'{}'" json:"supported_countries"`                                           // 支持返利的国家（仅仅用于展示，实际没关系）
	CreatedAt          time.Time       `json:"created_at"`
	UpdatedAt          time.Time       `json:"updated_at"`
	DeletedAt          gorm.DeletedAt  `gorm:"index;index:idx_merchant_code_status_deleted,priority:3" json:"-"`
	CashbackRate       decimal.Decimal `gorm:"type:decimal(10,2);default:0.8;not null" json:"cashback_rate"` // 自己平台从上级联盟分配到的返现金额中，再次分配给用户的比例。例如，上级联盟的返现值为 24%，自己平台设置的返现率为 80%，则用户最终拿到的返现比例为 80% × 24% = 19.2%。

	CashbackType       string          `gorm:"type:varchar(50);default:'%';not null" json:"cashback_type"`  // 佣金类型：% / $ / usd ...
	CashbackValue      decimal.Decimal `gorm:"type:decimal(10,2);default:0;not null" json:"cashback_value"` // 基于上级联盟的返现值计算得出的具体返现金额比例。例如，用户最终能拿到的返现比例为 19.2%。优先：parent_cashback_value * cashback_rate = cashback_value
	CashbackIsUpto     bool            `json:"cashback_is_upto"`
	CashbackIsRevShare bool            `json:"cashback_is_rev_share"`
	// 对于组合格式，存储替代性佣金信息
	AlternativeCashbackType       string          `gorm:"type:varchar(50);default:'%';not null" json:"alternative_cashback_type"`  // 佣金类型：% / $ / usd ...
	AlternativeCashbackValue      decimal.Decimal `gorm:"type:decimal(10,2);default:0;not null" json:"alternative_cashback_value"` // 基于上级联盟的返现值计算得出的具体返现金额比例。例如，用户最终能拿到的返现比例为 19.2%。优先：parent_cashback_value * cashback_rate = cashback_value
	AlternativeCashbackIsUpto     bool            `json:"alternative_cashback_is_upto"`
	AlternativeCashbackIsRevShare bool            `json:"alternative_cashback_is_rev_share"`

	// 以下内容绝不展示给用户
	PlatformType        string `gorm:"type:varchar(63);not null" json:"platform_type"`                      // 平台类型：cj、awin
	PlatformMerchantID  string `gorm:"type:varchar(63);not null" json:"platform_merchant_id"`               // 平台商家ID
	AffiliateLink       string `gorm:"type:varchar(2048);not null" json:"affiliate_link"`                   // 上级联盟链接，不带有用户编码的
	ParentCashbackValue string `gorm:"type:varchar(255);default:'-';not null" json:"parent_cashback_value"` // 上级联盟 基于订单金额计算的具体返现金额的比例值。例如，订单金额为 100 美元，2% 表示返现值为 2 美元。
	//ParentCashbackRate  string `gorm:"type:varchar(255);default:'-';not null" json:"parent_cashback_rate"`  // 上级联盟分配给用户的返现金额比例。比如，80% 表示上级联盟将获得的返现金额的 80% 分给用户。

}

// MerchantListCacheData 商家列表缓存数据
type MerchantListCacheData struct {
	Merchants []*Merchant
	Total     int64
}
