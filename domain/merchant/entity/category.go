package entity

import (
	"gorm.io/gorm"
	"time"
)

// Category 商家分类实体
type Category struct {
	ID        uint64         `gorm:"primarykey" json:"id"`
	Name      string         `gorm:"type:varchar(50);uniqueIndex;not null" json:"name"`
	Icon      string         `json:"icon"` // 分类图标
	Status    int8           `gorm:"type:smallint;default:1;not null" json:"status"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}
