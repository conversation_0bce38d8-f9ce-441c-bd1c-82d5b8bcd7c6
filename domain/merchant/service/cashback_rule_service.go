package service

import (
	"bonusearned/domain/merchant/entity"
	"bonusearned/domain/merchant/repository"
	"bonusearned/infra/cache/cachekey"
	"bonusearned/infra/cache/localcache"
	"bonusearned/infra/cache/rediscache"
	"bonusearned/infra/constant"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

// CashbackRuleService 商家返现规则领域服务接口
type CashbackRuleService interface {
	BatchApplyCashbackRate(ctx *gin.Context, userID uint64, merchantList []*entity.Merchant) ([]*entity.Merchant, *ecode.Error)
	ApplyCashbackRate(ctx *gin.Context, userID uint64, merchant *entity.Merchant) (*entity.Merchant, *ecode.Error)

	// CreateCashbackRule 创建商家返现规则
	CreateCashbackRule(ctx *gin.Context, rule *entity.CashbackRule) *ecode.Error
	// UpdateCashbackRule 更新商家返现规则
	UpdateCashbackRule(ctx *gin.Context, rule *entity.CashbackRule) *ecode.Error
	// GetCashbackRuleDetailByID 根据ID获取返现规则详情
	GetCashbackRuleDetailByID(ctx *gin.Context, id uint64) (*entity.CashbackRule, *ecode.Error)
	// GetCashbackRuleByUserAndMerchant 根据用户ID和商家ID获取商家返现规则
	GetCashbackRuleByUserAndMerchant(ctx *gin.Context, userID, merchantID uint64) (*entity.CashbackRule, *ecode.Error)
	// GetCashbackGlobalRuleByUserId 获取用户的全局返现规则
	GetCashbackGlobalRuleByUserId(ctx *gin.Context, userID uint64) (*entity.CashbackRule, *ecode.Error)
	// GetCashbackRuleListByCondition 获取返现规则列表
	GetCashbackRuleListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.CashbackRule, int64, *ecode.Error)
	// DeleteCashbackRule 删除过期的返现规则
	DeleteCashbackRule(ctx *gin.Context, id uint64) *ecode.Error
}

type CashbackRuleServiceImpl struct {
	repo       repository.CashbackRuleRepository
	redis      *redis.Client
	localCache *localcache.Cache // 使用新的本地缓存
	redisCache *rediscache.Cache // 使用新的Redis缓存
	logger     *zap.Logger
}

// NewCashbackRuleService 创建商家返现规则领域服务
func NewCashbackRuleService(repo repository.CashbackRuleRepository, logger *zap.Logger, redis *redis.Client) CashbackRuleService {
	return &CashbackRuleServiceImpl{
		repo:       repo,
		redis:      redis,
		localCache: localcache.New(),      // 创建本地缓存
		redisCache: rediscache.New(redis), // 创建Redis缓存
		logger:     logger,
	}
}

func (s *CashbackRuleServiceImpl) BatchApplyCashbackRate(ctx *gin.Context, userID uint64, merchantList []*entity.Merchant) ([]*entity.Merchant, *ecode.Error) {
	if len(merchantList) <= 0 {
		return merchantList, nil
	}

	// 1. 获取用户全局返现比例
	globalCashback, err := s.GetCashbackGlobalRuleByUserId(ctx, userID)
	if err != nil {
		return merchantList, err
	}

	// 2. 获取用户所有商家特定的返现比例
	condition := map[string]interface{}{}
	condition["user_id"] = userID
	condition["status"] = constant.StatusActive
	condition["is_global"] = false
	specificCashbackList, _, err := s.GetCashbackRuleListByCondition(ctx, condition)
	if err != nil {
		return merchantList, err
	}
	// 如果都没有获取到，说明没有规则
	if globalCashback == nil && len(specificCashbackList) <= 0 {
		return merchantList, nil
	}

	// 构建商家ID到返现比例的映射
	cashbackMap := make(map[uint64]decimal.Decimal)
	for _, cb := range specificCashbackList {
		cashbackMap[cb.MerchantID] = cb.CashbackRate
	}

	// 3. 应用返现比例
	for i := range merchantList {
		// 优先使用特定商家的返现比例
		if rate, exists := cashbackMap[merchantList[i].ID]; exists {
			if !merchantList[i].CashbackValue.IsZero() {
				merchantList[i].CashbackValue = merchantList[i].CashbackRate.Div(merchantList[i].CashbackValue).Mul(rate)
				merchantList[i].AlternativeCashbackValue = merchantList[i].CashbackRate.Div(merchantList[i].AlternativeCashbackValue).Mul(rate)
			}
			merchantList[i].CashbackRate = rate
		} else if globalCashback != nil {
			// 如果没有特定商家的返现比例，使用全局返现比例
			if !merchantList[i].CashbackValue.IsZero() {
				merchantList[i].CashbackValue = merchantList[i].CashbackRate.Div(merchantList[i].CashbackValue).Mul(globalCashback.CashbackRate)
				merchantList[i].AlternativeCashbackValue = merchantList[i].CashbackRate.Div(merchantList[i].AlternativeCashbackValue).Mul(globalCashback.CashbackRate)
			}
			merchantList[i].CashbackRate = globalCashback.CashbackRate
		}
	}

	return merchantList, nil
}

func (s *CashbackRuleServiceImpl) ApplyCashbackRate(ctx *gin.Context, userID uint64, merchant *entity.Merchant) (*entity.Merchant, *ecode.Error) {
	if merchant == nil {
		return merchant, nil
	}

	// 1. 获取用户全局返现比例
	globalCashback, err := s.GetCashbackGlobalRuleByUserId(ctx, userID)
	if err != nil {
		return merchant, err
	}

	// 2. 获取用户所有商家特定的返现比例
	specificCashback, err := s.GetCashbackRuleByUserAndMerchant(ctx, userID, merchant.ID)
	if err != nil {
		return merchant, err
	}
	// 如果都没有获取到，说明没有规则
	if globalCashback == nil && specificCashback == nil {
		return merchant, nil
	}

	// 优先使用特定商家的返现比例
	if specificCashback != nil {
		if !merchant.CashbackValue.IsZero() {
			merchant.CashbackValue = merchant.CashbackRate.Div(merchant.CashbackValue).Mul(specificCashback.CashbackRate)
		}
		merchant.CashbackRate = specificCashback.CashbackRate
		return merchant, nil
	}
	// 如果没有特定商家的返现比例，使用全局返现比例
	if !merchant.CashbackValue.IsZero() {
		merchant.CashbackValue = merchant.CashbackRate.Div(merchant.CashbackValue).Mul(globalCashback.CashbackRate)
	}
	merchant.CashbackRate = globalCashback.CashbackRate
	return merchant, nil
}

func (s *CashbackRuleServiceImpl) CreateCashbackRule(ctx *gin.Context, rule *entity.CashbackRule) *ecode.Error {
	// 如果是全局规则，检查是否已存在
	if rule.IsGlobal {
		existingRule, err := s.repo.GetCashbackGlobalRuleByUserId(ctx, rule.UserID)
		if err != nil {
			return err
		}
		if existingRule != nil {
			return ecode.New(ecode.ErrInternalServer.Code, "global rule already exists")
		}
	} else {
		// 检查特定商家规则是否已存在
		existingRule, err := s.repo.GetCashbackRuleByUserAndMerchant(ctx, rule.UserID, rule.MerchantID)
		if err != nil {
			return err
		}
		if existingRule != nil {
			return ecode.New(ecode.ErrInternalServer.Code, "merchant rule already exists")
		}
	}
	return s.repo.CreateCashbackRule(ctx, rule)
}

func (s *CashbackRuleServiceImpl) UpdateCashbackRule(ctx *gin.Context, rule *entity.CashbackRule) *ecode.Error {
	return s.repo.UpdateCashbackRule(ctx, rule)
}

func (s *CashbackRuleServiceImpl) GetCashbackRuleDetailByID(ctx *gin.Context, id uint64) (*entity.CashbackRule, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateCashbackRuleKey(id)
	// 1. 尝试从内存缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if cashbackRule, ok := value.(*entity.CashbackRule); ok {
			return cashbackRule, nil
		}
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if cashbackRule, ok := value.(*entity.CashbackRule); ok {
			// 设置内存缓存（存储未处理的商家数据）
			s.localCache.Set(ctx, cacheKey, cashbackRule, cachekey.LongExpiration)
			return cashbackRule, nil
		}
	}

	// 3. 从数据库获取
	cashbackRule, err := s.repo.GetCashbackRuleDetailByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 设置缓存（存储未处理的商家数据）
	s.localCache.Set(ctx, cacheKey, cashbackRule, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, cashbackRule, cachekey.LongExpiration)

	return cashbackRule, nil
}

func (s *CashbackRuleServiceImpl) GetCashbackRuleByUserAndMerchant(ctx *gin.Context, userID, merchantID uint64) (*entity.CashbackRule, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateCashbackRuleByUserAndMerchantKey(userID, merchantID)
	// 1. 尝试从内存缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if cashbackRule, ok := value.(*entity.CashbackRule); ok {
			return cashbackRule, nil
		}
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if cashbackRule, ok := value.(*entity.CashbackRule); ok {
			// 设置内存缓存（存储未处理的商家数据）
			s.localCache.Set(ctx, cacheKey, cashbackRule, cachekey.LongExpiration)
			return cashbackRule, nil
		}
	}

	// 3. 从数据库获取
	cashbackRule, err := s.repo.GetCashbackRuleByUserAndMerchant(ctx, userID, merchantID)
	if err != nil {
		return nil, err
	}

	// 设置缓存（存储未处理的商家数据）
	s.localCache.Set(ctx, cacheKey, cashbackRule, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, cashbackRule, cachekey.LongExpiration)

	return cashbackRule, nil
}

func (s *CashbackRuleServiceImpl) GetCashbackGlobalRuleByUserId(ctx *gin.Context, userID uint64) (*entity.CashbackRule, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateCashbackRuleByUserKey(userID)
	// 1. 尝试从内存缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if cashbackRule, ok := value.(*entity.CashbackRule); ok {
			return cashbackRule, nil
		}
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if cashbackRule, ok := value.(*entity.CashbackRule); ok {
			// 设置内存缓存（存储未处理的商家数据）
			s.localCache.Set(ctx, cacheKey, cashbackRule, cachekey.LongExpiration)
			return cashbackRule, nil
		}
	}

	// 3. 从数据库获取
	cashbackRule, err := s.repo.GetCashbackGlobalRuleByUserId(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 设置缓存（存储未处理的商家数据）
	s.localCache.Set(ctx, cacheKey, cashbackRule, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, cashbackRule, cachekey.LongExpiration)

	return cashbackRule, nil
}

func (s *CashbackRuleServiceImpl) GetCashbackRuleListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.CashbackRule, int64, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateCashbackRuleListKey(condition)

	// 1. 尝试从内存缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if cashbackRuleList, ok := value.([]*entity.CashbackRule); ok {
			return cashbackRuleList, int64(len(cashbackRuleList)), nil
		}
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if cashbackRuleList, ok := value.([]*entity.CashbackRule); ok {
			// 设置内存缓存
			s.localCache.Set(ctx, cacheKey, cashbackRuleList, cachekey.LongExpiration)
			return cashbackRuleList, int64(len(cashbackRuleList)), nil
		}
	}

	// 3. 从数据库获取
	cashbackRuleList, total, err := s.repo.GetCashbackRuleList(ctx, condition)
	if err != nil {
		return nil, 0, err
	}

	// 设置缓存
	s.localCache.Set(ctx, cacheKey, cashbackRuleList, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, cashbackRuleList, cachekey.LongExpiration)

	return cashbackRuleList, total, nil
}

func (s *CashbackRuleServiceImpl) DeleteCashbackRule(ctx *gin.Context, id uint64) *ecode.Error {
	return s.repo.DeleteCashbackRule(ctx, id)
}
