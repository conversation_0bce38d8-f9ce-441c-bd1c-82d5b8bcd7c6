package service

import (
	"bonusearned/domain/merchant/entity"
	"bonusearned/domain/merchant/repository"
	"bonusearned/infra/cache/cachekey"
	"bonusearned/infra/cache/localcache"
	"bonusearned/infra/cache/rediscache"
	"bonusearned/infra/ecode"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// CategoryService 商家分类领域服务接口
type CategoryService interface {
	// CreateCategory 创建商家分类
	CreateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error
	// UpdateCategory 更新商家分类
	UpdateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error
	// GetCategoryDetailById 根据ID获取商家分类
	GetCategoryDetailById(ctx *gin.Context, id uint64) (*entity.Category, *ecode.Error)
	// GetCategoryDetailByName 根据名称获取商家分类
	GetCategoryDetailByName(ctx *gin.Context, name string) (*entity.Category, *ecode.Error)
	// GetCategoryListByCondition 获取商家分类列表
	GetCategoryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Category, int64, *ecode.Error)
}

type categoryService struct {
	repo       repository.CategoryRepository
	redis      *redis.Client
	localCache *localcache.Cache // 使用新的本地缓存
	redisCache *rediscache.Cache // 使用新的Redis缓存
	logger     *zap.Logger
}

// NewCategoryService 创建商家分类领域服务
func NewCategoryService(repo repository.CategoryRepository, logger *zap.Logger, redis *redis.Client) CategoryService {
	return &categoryService{
		repo:       repo,
		redis:      redis,
		localCache: localcache.New(),      // 创建本地缓存
		redisCache: rediscache.New(redis), // 创建Redis缓存
		logger:     logger,
	}
}

func (s *categoryService) CreateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error {
	if category == nil {
		return ecode.ErrInvalidParameter
	}
	err := s.repo.CreateCategory(ctx, category)
	if err != nil {
		s.logger.Error("Failed to create category",
			zap.Any("category", category),
			zap.Error(err),
		)
		return err
	}

	return nil
}

func (s *categoryService) UpdateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error {
	if category == nil {
		return ecode.ErrInvalidParameter
	}
	err := s.repo.UpdateCategory(ctx, category)
	if err != nil {
		s.logger.Error("Failed to update category",
			zap.Any("category", category),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *categoryService) GetCategoryDetailById(ctx *gin.Context, id uint64) (*entity.Category, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateCategoryIdKey(id)
	// 1. 尝试从内存缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if category, ok := value.(*entity.Category); ok {
			return category, nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}
	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if category, ok := value.(*entity.Category); ok {
			s.localCache.Set(ctx, cacheKey, category, cachekey.LongExpiration)
			return category, nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}
	category, err := s.repo.GetCategoryDetailById(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get category from database",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return nil, err
	}
	// 设置缓存
	s.localCache.Set(ctx, cacheKey, category, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, category, cachekey.LongExpiration)
	return category, nil
}

func (s *categoryService) GetCategoryDetailByName(ctx *gin.Context, name string) (*entity.Category, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateCategoryNameKey(name)
	// 1. 尝试从内存缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if category, ok := value.(*entity.Category); ok {
			return category, nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}
	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if category, ok := value.(*entity.Category); ok {
			s.localCache.Set(ctx, cacheKey, category, cachekey.LongExpiration)
			return category, nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}
	category, err := s.repo.GetCategoryDetailByName(ctx, name)
	if err != nil {
		s.logger.Error("Failed to get category from database",
			zap.String("name", name),
			zap.Error(err),
		)
		return nil, err
	}
	// 设置缓存
	s.localCache.Set(ctx, cacheKey, category, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, category, cachekey.LongExpiration)
	return category, nil
}

func (s *categoryService) GetCategoryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Category, int64, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateCategoryListKey(condition)

	// 1. 尝试从内存缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if categoryList, ok := value.([]*entity.Category); ok {
			return categoryList, int64(len(categoryList)), nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if categoryList, ok := value.([]*entity.Category); ok {
			// 设置内存缓存
			s.localCache.Set(ctx, cacheKey, categoryList, cachekey.LongExpiration)
			return categoryList, int64(len(categoryList)), nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}

	categoryList, total, err := s.repo.GetCategoryListByCondition(ctx, condition)
	if err != nil {
		s.logger.Error("Failed to get category list from database",
			zap.Any("condition", condition),
			zap.Error(err),
		)
		return nil, 0, err
	}
	// 设置缓存
	s.localCache.Set(ctx, cacheKey, categoryList, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, categoryList, cachekey.LongExpiration)
	return categoryList, total, nil
}
