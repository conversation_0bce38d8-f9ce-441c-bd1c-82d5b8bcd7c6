package service

import (
	"bonusearned/domain/merchant/entity"
	"bonusearned/domain/merchant/repository"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CountryService 国家服务接口
type CountryService interface {
	// CreateCountry 创建国家
	CreateCountry(ctx *gin.Context, country *entity.Country) *ecode.Error
	// UpdateCountry 更新国家
	UpdateCountry(ctx *gin.Context, country *entity.Country) *ecode.Error
	// GetCountryDetailById 根据ID获取国家详情
	GetCountryDetailById(ctx *gin.Context, id uint64) (*entity.Country, *ecode.Error)
	// GetCountryDetailByCode 根据代码获取国家详情
	GetCountryDetailByCode(ctx *gin.Context, code string) (*entity.Country, *ecode.Error)
	// GetCountryListByCondition 获取国家列表
	GetCountryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Country, int64, *ecode.Error)
	// DeleteCountry 删除国家
	DeleteCountry(ctx *gin.Context, id uint64) *ecode.Error
}

type CountryServiceImpl struct {
	repo   repository.CountryRepository
	logger *zap.Logger
}

// NewCountryService 创建国家服务
func NewCountryService(repo repository.CountryRepository, logger *zap.Logger) CountryService {
	return &CountryServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

// CreateCountry 创建国家
func (s *CountryServiceImpl) CreateCountry(ctx *gin.Context, country *entity.Country) *ecode.Error {
	if country == nil {
		return ecode.ErrInvalidParameter
	}
	
	// 检查国家代码是否已存在
	existingCountry, err := s.repo.GetCountryDetailByCode(ctx, country.Code)
	if err != nil && err != ecode.ErrCountryNotFound {
		return err
	}
	if existingCountry != nil {
		return ecode.New(409, "country code already exists")
	}
	
	if err := s.repo.CreateCountry(ctx, country); err != nil {
		s.logger.Error("Failed to create country",
			zap.Any("country", country),
			zap.Error(err))
		return err
	}
	return nil
}

// UpdateCountry 更新国家
func (s *CountryServiceImpl) UpdateCountry(ctx *gin.Context, country *entity.Country) *ecode.Error {
	if country == nil {
		return ecode.ErrInvalidParameter
	}
	
	if err := s.repo.UpdateCountry(ctx, country); err != nil {
		s.logger.Error("Failed to update country",
			zap.Any("country", country),
			zap.Error(err))
		return err
	}
	return nil
}

// GetCountryDetailById 根据ID获取国家详情
func (s *CountryServiceImpl) GetCountryDetailById(ctx *gin.Context, id uint64) (*entity.Country, *ecode.Error) {
	if id == 0 {
		return nil, ecode.ErrInvalidID
	}
	
	country, err := s.repo.GetCountryDetailById(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get country by id",
			zap.Uint64("id", id),
			zap.Error(err))
		return nil, err
	}
	return country, nil
}

// GetCountryDetailByCode 根据代码获取国家详情
func (s *CountryServiceImpl) GetCountryDetailByCode(ctx *gin.Context, code string) (*entity.Country, *ecode.Error) {
	if code == "" {
		return nil, ecode.ErrInvalidParameter
	}
	
	country, err := s.repo.GetCountryDetailByCode(ctx, code)
	if err != nil {
		s.logger.Error("Failed to get country by code",
			zap.String("code", code),
			zap.Error(err))
		return nil, err
	}
	return country, nil
}

// GetCountryListByCondition 获取国家列表
func (s *CountryServiceImpl) GetCountryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Country, int64, *ecode.Error) {
	countries, total, err := s.repo.GetCountryListByCondition(ctx, condition)
	if err != nil {
		s.logger.Error("Failed to get country list",
			zap.Any("condition", condition),
			zap.Error(err))
		return nil, 0, err
	}
	return countries, total, nil
}

// DeleteCountry 删除国家
func (s *CountryServiceImpl) DeleteCountry(ctx *gin.Context, id uint64) *ecode.Error {
	if id == 0 {
		return ecode.ErrInvalidID
	}
	
	if err := s.repo.DeleteCountry(ctx, id); err != nil {
		s.logger.Error("Failed to delete country",
			zap.Uint64("id", id),
			zap.Error(err))
		return err
	}
	return nil
}
