package service

import (
	"bonusearned/config"
	"bonusearned/domain/merchant/entity"
	"bonusearned/domain/merchant/repository"
	"bonusearned/infra/cache/cachekey"
	"bonusearned/infra/cache/localcache"
	"bonusearned/infra/cache/rediscache"
	"bonusearned/infra/ecode"
	"bonusearned/infra/utils/trackurlutil"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

type MerchantService interface {
	GetMerchantDetailById(ctx *gin.Context, id uint64, userId uint64, userCode string) (*entity.Merchant, *ecode.Error)
	GetMerchantDetailByCode(ctx *gin.Context, merchantCode string, userId uint64, userCode string) (*entity.Merchant, *ecode.Error)
	GetMerchantAffiliateLinkByCode(ctx *gin.Context, code string) (*entity.Merchant, *ecode.Error)
	GetMerchantDetailByUniqueName(ctx *gin.Context, uniqueName string, userId uint64, userCode string) (*entity.Merchant, *ecode.Error)

	GetMerchantListByCondition(ctx *gin.Context, condition map[string]interface{}, userId uint64, userCode string) ([]*entity.Merchant, int64, *ecode.Error)
	// GetMerchantListByIDs 批量获取商家信息
	GetMerchantListByIDs(ctx *gin.Context, ids []uint64, userId uint64, userCode string) ([]*entity.Merchant, *ecode.Error)

	CreateMerchant(ctx *gin.Context, merchant *entity.Merchant) *ecode.Error
	UpdateMerchant(ctx *gin.Context, merchant *entity.Merchant) *ecode.Error
	DeleteMerchant(ctx *gin.Context, id uint64) *ecode.Error
	UpdateMerchantStatus(ctx *gin.Context, id uint64, status int8) *ecode.Error
	GetMerchantCount(ctx *gin.Context) (int64, *ecode.Error)

	// BatchCreateMerchants 批量创建商家
	BatchCreateMerchants(ctx *gin.Context, merchants []*entity.Merchant) *ecode.Error
	// BatchUpdateMerchants 批量更新商家
	BatchUpdateMerchants(ctx *gin.Context, merchants []*entity.Merchant) *ecode.Error
}

type MerchantServiceImpl struct {
	repo                repository.MerchantRepository
	cashbackRuleService CashbackRuleService
	redis               *redis.Client
	localCache          *localcache.Cache // 使用新的本地缓存
	redisCache          *rediscache.Cache // 使用新的Redis缓存
	config              *config.Config
	logger              *zap.Logger
}

func NewMerchantService(
	repo repository.MerchantRepository,
	cashbackRuleService CashbackRuleService,
	redis *redis.Client,
	config *config.Config,
	logger *zap.Logger,
) MerchantService {
	return &MerchantServiceImpl{
		repo:                repo,
		cashbackRuleService: cashbackRuleService,
		redis:               redis,
		localCache:          localcache.New(),      // 创建本地缓存
		redisCache:          rediscache.New(redis), // 创建Redis缓存
		config:              config,
		logger:              logger,
	}
}

func (s *MerchantServiceImpl) GetMerchantDetailById(ctx *gin.Context, id uint64, userId uint64, userCode string) (*entity.Merchant, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateMerchantIdKey(id)
	// 1. 尝试从内存缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if merchant, ok := value.(*entity.Merchant); ok {
			merchant, _ = s.ProcessMerchantSpecificData(ctx, userId, userCode, merchant)
			return merchant, nil
		}
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if merchant, ok := value.(*entity.Merchant); ok {
			// 设置内存缓存（存储未处理的商家数据）
			s.localCache.Set(ctx, cacheKey, merchant, cachekey.LongExpiration)
			merchant, _ = s.ProcessMerchantSpecificData(ctx, userId, userCode, merchant)
			return merchant, nil
		}
	}

	// 3. 从数据库获取
	merchant, err := s.repo.GetMerchantDetailById(ctx, id)
	if err != nil {
		return nil, err
	}

	// 设置缓存（存储未处理的商家数据）
	s.localCache.Set(ctx, cacheKey, merchant, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, merchant, cachekey.LongExpiration)
	merchant, _ = s.ProcessMerchantSpecificData(ctx, userId, userCode, merchant)
	return merchant, nil
}

func (s *MerchantServiceImpl) GetMerchantDetailByUniqueName(ctx *gin.Context, uniqueName string, userId uint64, userCode string) (*entity.Merchant, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateMerchantByUniqueNameKey(uniqueName)

	// 1. 尝试从内存缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if merchant, ok := value.(*entity.Merchant); ok {
			merchant, _ = s.ProcessMerchantSpecificData(ctx, userId, userCode, merchant)
			return merchant, nil
		}
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if merchant, ok := value.(*entity.Merchant); ok {
			// 设置内存缓存
			s.localCache.Set(ctx, cacheKey, merchant, cachekey.LongExpiration)
			merchant, _ = s.ProcessMerchantSpecificData(ctx, userId, userCode, merchant)
			return merchant, nil
		}
	}

	// 3. 从数据库获取
	merchant, err := s.repo.GetMerchantDetailByUniqueName(ctx, uniqueName)
	if err != nil {
		return nil, err
	}

	// 设置缓存（存储未处理的商家数据）
	s.localCache.Set(ctx, cacheKey, merchant, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, merchant, cachekey.LongExpiration)
	merchant, _ = s.ProcessMerchantSpecificData(ctx, userId, userCode, merchant)
	return merchant, nil
}

func (s *MerchantServiceImpl) GetMerchantDetailByCode(ctx *gin.Context, code string, userId uint64, userCode string) (*entity.Merchant, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateMerchantByCodeKey(code)
	cacheKeyMerchantTrack := cachekey.GenerateMerchantTrackByCodeKey(code)

	// 1. 尝试从内存缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if merchant, ok := value.(*entity.Merchant); ok {
			merchant, _ = s.ProcessMerchantSpecificData(ctx, userId, userCode, merchant)
			return merchant, nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if merchant, ok := value.(*entity.Merchant); ok {
			// 设置内存缓存
			s.localCache.Set(ctx, cacheKey, merchant, cachekey.LongExpiration)
			s.localCache.Set(ctx, cacheKeyMerchantTrack, merchant, cachekey.LongExpiration)
			merchant, _ = s.ProcessMerchantSpecificData(ctx, userId, userCode, merchant)
			return merchant, nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}

	// 3. 从数据库获取
	merchant, err := s.repo.GetMerchantDetailByCode(ctx, code)
	if err != nil {
		s.logger.Error("Failed to get merchant from database",
			zap.String("code", code),
			zap.Error(err),
		)
		return nil, err
	}

	// 设置缓存（存储未处理的商家数据）
	s.localCache.Set(ctx, cacheKey, merchant, cachekey.LongExpiration)
	s.localCache.Set(ctx, cacheKeyMerchantTrack, merchant, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, merchant, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKeyMerchantTrack, merchant, cachekey.LongExpiration)
	merchant, _ = s.ProcessMerchantSpecificData(ctx, userId, userCode, merchant)
	return merchant, nil
}

func (s *MerchantServiceImpl) GetMerchantAffiliateLinkByCode(ctx *gin.Context, code string) (*entity.Merchant, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateMerchantTrackByCodeKey(code)
	// 1. 尝试从内存缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if merchant, ok := value.(*entity.Merchant); ok {
			return merchant, nil
		}
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if merchant, ok := value.(*entity.Merchant); ok {
			// 设置内存缓存
			s.localCache.Set(ctx, cacheKey, merchant, cachekey.LongExpiration)
			return merchant, nil
		}
	}

	// 3. 从数据库获取
	merchant, err := s.repo.GetMerchantAffiliateLinkByCode(ctx, code)
	if err != nil {
		s.logger.Error("Failed to get merchant from database",
			zap.String("code", code),
			zap.Error(err),
		)
		return nil, err
	}
	// 设置缓存（存储未处理的商家数据）
	s.localCache.Set(ctx, cacheKey, merchant, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, merchant, cachekey.LongExpiration)
	return merchant, nil
}

func (s *MerchantServiceImpl) GetMerchantListByCondition(ctx *gin.Context, condition map[string]interface{}, userId uint64, userCode string) ([]*entity.Merchant, int64, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateMerchantListKey(condition)

	// 1. 尝试从内存缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if cacheData, ok := value.(*entity.MerchantListCacheData); ok {
			merchants, _ := s.BatchProcessMerchantSpecificData(ctx, userId, userCode, cacheData.Merchants)
			return merchants, cacheData.Total, nil
		}
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if cacheData, ok := value.(*entity.MerchantListCacheData); ok {
			// 设置内存缓存
			s.localCache.Set(ctx, cacheKey, cacheData, cachekey.LongExpiration)
			merchants, _ := s.BatchProcessMerchantSpecificData(ctx, userId, userCode, cacheData.Merchants)
			return merchants, cacheData.Total, nil
		}
	}

	// 3. 从数据库获取
	merchants, total, err := s.repo.GetMerchantListByCondition(ctx, condition)
	if err != nil {
		return nil, 0, err
	}

	// 创建缓存数据结构
	cacheData := &entity.MerchantListCacheData{
		Merchants: merchants,
		Total:     total,
	}

	// 设置缓存
	s.localCache.Set(ctx, cacheKey, cacheData, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, cacheData, cachekey.LongExpiration)
	merchants, _ = s.BatchProcessMerchantSpecificData(ctx, userId, userCode, cacheData.Merchants)
	return merchants, total, nil
}

func (s *MerchantServiceImpl) CreateMerchant(ctx *gin.Context, merchant *entity.Merchant) *ecode.Error {
	if merchant == nil {
		return ecode.ErrInvalidParameter
	}
	if err := s.repo.CreateMerchant(ctx, merchant); err != nil {
		s.logger.Error("Failed to create merchant",
			zap.Any("merchant", merchant),
			zap.Error(err))
		return err
	}
	return nil
}

func (s *MerchantServiceImpl) UpdateMerchant(ctx *gin.Context, merchant *entity.Merchant) *ecode.Error {
	if merchant == nil {
		return ecode.ErrInvalidParameter
	}

	// 更新数据库
	if err := s.repo.UpdateMerchant(ctx, merchant); err != nil {
		s.logger.Error("Failed to update merchant",
			zap.Uint64("id", merchant.ID),
			zap.Error(err))
		return err
	}

	return nil
}

func (s *MerchantServiceImpl) DeleteMerchant(ctx *gin.Context, id uint64) *ecode.Error {
	// 删除商家
	err := s.repo.DeleteMerchant(ctx, id)
	if err != nil {
		s.logger.Error("Failed to delete merchant",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *MerchantServiceImpl) GetMerchantCount(ctx *gin.Context) (int64, *ecode.Error) {
	// 更新商家状态
	total, err := s.repo.GetMerchantCount(ctx)
	if err != nil {
		s.logger.Error("Failed to get merchant count",
			zap.Error(err),
		)
		return 0, err
	}

	return total, nil
}

func (s *MerchantServiceImpl) UpdateMerchantStatus(ctx *gin.Context, id uint64, status int8) *ecode.Error {
	// 更新商家状态
	err := s.repo.UpdateMerchantStatus(ctx, id, status)
	if err != nil {
		s.logger.Error("Failed to update merchant status",
			zap.Uint64("id", id),
			zap.Int8("status", status),
			zap.Error(err),
		)
		return err
	}

	return nil
}

func (s *MerchantServiceImpl) BatchCreateMerchants(ctx *gin.Context, merchants []*entity.Merchant) *ecode.Error {
	if len(merchants) == 0 {
		return nil
	}
	// 批量创建商家
	err := s.repo.BatchCreateMerchantsV2(ctx, merchants)
	//err := s.repo.BatchCreateMerchants(ctx, merchants)
	if err != nil {
		s.logger.Error("Failed to batch create merchants",
			zap.Int("count", len(merchants)),
			zap.Error(err),
		)
		return err
	}

	return nil
}

func (s *MerchantServiceImpl) BatchUpdateMerchants(ctx *gin.Context, merchants []*entity.Merchant) *ecode.Error {
	if len(merchants) == 0 {
		return nil
	}

	// 批量更新商家
	err := s.repo.BatchUpdateMerchants(ctx, merchants)
	if err != nil {
		s.logger.Error("Failed to batch update merchants",
			zap.Int("count", len(merchants)),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *MerchantServiceImpl) GetMerchantListByIDs(ctx *gin.Context, ids []uint64, userId uint64, userCode string) ([]*entity.Merchant, *ecode.Error) {
	if len(ids) == 0 {
		return make([]*entity.Merchant, 0), nil
	}

	// 1. 从本地缓存批量获取
	merchants := make([]*entity.Merchant, 0, len(ids))
	localMissedIDs := make([]uint64, 0)

	for _, id := range ids {
		key := cachekey.GenerateMerchantIdKey(id)
		if merchant, ok := s.localCache.Get(ctx, key); ok {
			if m, ok := merchant.(*entity.Merchant); ok {
				merchants = append(merchants, m)
				continue
			}
		}
		localMissedIDs = append(localMissedIDs, id)
	}

	if len(localMissedIDs) == 0 {
		merchants, _ = s.BatchProcessMerchantSpecificData(ctx, userId, userCode, merchants)
		return merchants, nil
	}

	// 2. 从Redis缓存批量获取本地缓存未命中的ID
	redisMerchants, missedRedisIDs, cacheErr := s.redisCache.MGet(ctx, cachekey.GenerateMerchantIdKey, localMissedIDs)
	if cacheErr != nil {
		s.logger.Error("get merchants from redis failed", zap.Error(cacheErr))
		// Redis出错时，所有本地未命中的ID都需要从数据库获取
		missedRedisIDs = localMissedIDs
	} else {
		for _, item := range redisMerchants {
			if merchant, ok := item.(*entity.Merchant); ok {
				merchants = append(merchants, merchant)
				// 写入本地缓存
				key := cachekey.GenerateMerchantIdKey(merchant.ID)
				s.localCache.Set(ctx, key, merchant, cachekey.LongExpiration)
			}
		}
		missedRedisIDs = missedRedisIDs
	}

	if len(missedRedisIDs) == 0 {
		merchants, _ = s.BatchProcessMerchantSpecificData(ctx, userId, userCode, merchants)
		return merchants, nil
	}

	// 3. 从数据库批量获取Redis缓存未命中的ID
	dbMerchants, err := s.repo.GetMerchantListByIDs(ctx, missedRedisIDs)
	if err != nil {
		return nil, err
	}

	// 4. 写入缓存
	for _, merchant := range dbMerchants {
		merchants = append(merchants, merchant)
		// 写入本地缓存
		key := cachekey.GenerateMerchantIdKey(merchant.ID)
		s.localCache.Set(ctx, key, merchant, cachekey.LongExpiration)
		// 写入Redis缓存
		_ = s.redisCache.Set(ctx, key, merchant, cachekey.LongExpiration)
	}
	merchants, _ = s.BatchProcessMerchantSpecificData(ctx, userId, userCode, merchants)
	return merchants, nil
}

func (s *MerchantServiceImpl) ProcessMerchantSpecificData(ctx *gin.Context, userId uint64, userCode string, merchant *entity.Merchant) (*entity.Merchant, *ecode.Error) {
	// 创建新的商家对象
	result := *merchant
	result.TrackURL = trackurlutil.GenTrackURL(result.TrackURL, userCode)
	if userId > 0 && merchant != nil {
		// 生成追踪链接
		ret, _ := s.cashbackRuleService.ApplyCashbackRate(ctx, userId, &result)
		return ret, nil
	}
	return &result, nil
}

func (s *MerchantServiceImpl) BatchProcessMerchantSpecificData(ctx *gin.Context, userId uint64, userCode string, merchantList []*entity.Merchant) ([]*entity.Merchant, *ecode.Error) {
	// 创建新的商家对象
	result := make([]*entity.Merchant, 0)

	for i := range merchantList {
		merchant := *merchantList[i]
		merchant.TrackURL = trackurlutil.GenTrackURL(merchant.TrackURL, userCode)
		result = append(result, &merchant)
	}
	// 获取用户ID，如果存在的话
	if userId > 0 && len(merchantList) > 0 {
		ret, _ := s.cashbackRuleService.BatchApplyCashbackRate(ctx, userId, result)
		return ret, nil
	}
	return result, nil
}
