package service

import (
	clickrecordentity "bonusearned/domain/clickrecord/entity"
	clickrecordservice "bonusearned/domain/clickrecord/service"
	"bonusearned/domain/merchant/entity"
	"bonusearned/domain/merchant/service"
	userservice "bonusearned/domain/user/service"
	"bonusearned/infra/constant"
	"bonusearned/infra/ecode"
	"bonusearned/infra/utils/uniqueutil"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/url"
	"strings"
	"time"
)

// TrackService 跟踪记录领域服务接口
type TrackService interface {
	// GetTrackUrl 创建跟踪记录
	GetTrackUrl(ctx *gin.Context, userCode, merchantCode, sub1 string) (string, *ecode.Error)
}

type TrackServiceImpl struct {
	merchantService    service.MerchantService
	clickRecordService clickrecordservice.ClickRecordService
	userService        userservice.UserService
}

// NewTrackService 创建跟踪记录领域服务
func NewTrackService(merchantService service.MerchantService, clickRecordService clickrecordservice.ClickRecordService, userService userservice.UserService) TrackService {
	return &TrackServiceImpl{
		merchantService:    merchantService,
		clickRecordService: clickRecordService,
		userService:        userService,
	}
}

func (s *TrackServiceImpl) GetTrackUrl(ctx *gin.Context, userCode, merchantCode, sub1 string) (string, *ecode.Error) {
	clickId := userCode // 作为兜底，至少要关联到对应用户
	// 生成点击id（实际只用点击id）
	clickId, err := uniqueutil.EncodeClick(merchantCode, userCode, sub1, constant.ClickKey)
	if err != nil {
		clickId = userCode // 作为兜底，至少要关联到对应用户
	}
	merchant, err := s.merchantService.GetMerchantAffiliateLinkByCode(ctx, merchantCode)
	if err != nil {
		return "", ecode.New(ecode.ErrInternalServer.Code, fmt.Sprintf("Error getting merchant: %v", err))
	}
	if merchant == nil {
		return "", ecode.New(ecode.ErrNotFound.Code, "Merchant not found")
	}

	// 异步保存点击记录
	go func() {
		user, _ := s.userService.GetUserDetailByUserCode(ctx, userCode)
		record := new(clickrecordentity.ClickRecord)
		if user == nil {
			record.UserID = 0
		} else {
			record.UserID = user.ID
		}
		record.ClickDate = time.Now()
		record.ClickID = clickId
		record.TrackUID = clickId
		record.UserCode = userCode
		record.MerchantCode = merchantCode
		record.MerchantID = merchant.ID
		record.Sub1 = sub1
		record.CreatedAt = time.Now()
		s.clickRecordService.CreateClickRecord(ctx, record)
	}()

	// 构建重定向url
	redirectURL := s.buildRedirectUrl(ctx, merchant, clickId)
	if redirectURL == "" {
		return "", ecode.New(ecode.ErrInternalServer.Code, "Failed to build redirect URL")
	}
	return redirectURL, nil
}

// buildRedirectUrl 构建重定向URL
func (s *TrackServiceImpl) buildRedirectUrl(ctx *gin.Context, merchant *entity.Merchant, clickId string) string {
	baseURL := merchant.AffiliateLink

	// 预分配空间
	builder := strings.Builder{}
	builder.Grow(len(baseURL) + 100)
	builder.WriteString(baseURL)

	// 确定起始字符，只需判断一次
	separator := byte('&')
	if !strings.Contains(baseURL, "?") {
		separator = '?'
	}

	// 一次性构建所有参数
	paramMappings := merchant.ParamMappings
	if paramMappings == nil {
		return baseURL
	}

	// 直接使用map查找，避免多次if-else
	if key, exists := paramMappings["click_id"]; exists {
		builder.WriteByte(separator)
		builder.WriteString(key)
		builder.WriteByte('=')
		builder.WriteString(url.QueryEscape(clickId))
	}

	return builder.String()
}
