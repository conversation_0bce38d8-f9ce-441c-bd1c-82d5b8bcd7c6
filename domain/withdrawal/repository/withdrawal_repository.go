package repository

import (
	"bonusearned/domain/withdrawal/entity"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// WithdrawalRepository 提现仓储接口
type WithdrawalRepository interface {
	// CreateWithdrawal 创建提现记录
	CreateWithdrawal(ctx *gin.Context, withdrawal *entity.Withdrawal) *ecode.Error
	// GetWithdrawalDetailByID 根据ID获取提现记录
	GetWithdrawalDetailByID(ctx *gin.Context, id uint64) (*entity.Withdrawal, *ecode.Error)
	// GetWithdrawalListByCondition 获取用户的提现记录
	GetWithdrawalListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Withdrawal, int64, *ecode.Error)
	// UpdateWithdrawalStatus 更新提现状态
	UpdateWithdrawalStatus(ctx *gin.Context, withdrawal *entity.Withdrawal) *ecode.Error
}
