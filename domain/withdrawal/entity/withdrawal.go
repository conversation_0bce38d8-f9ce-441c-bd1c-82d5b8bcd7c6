package entity

import (
	"bonusearned/infra/database/postgres"
	"encoding/json"
	"gorm.io/gorm"
	"time"
)

// WithdrawalStatus 提现状态
type WithdrawalStatus int8

const (
	WithdrawalStatusPending    WithdrawalStatus = 1 // 待处理
	WithdrawalStatusProcessing WithdrawalStatus = 2 // 处理中
	WithdrawalStatusCompleted  WithdrawalStatus = 3 // 已完成
	WithdrawalStatusFailed     WithdrawalStatus = 4 // 已失败
	WithdrawalStatusCanceled   WithdrawalStatus = 5 // 已取消
)

// PaymentMethod 支付方式
type PaymentMethod string

const (
	PaymentMethodPaypal PaymentMethod = "paypal"
	PaymentMethodBank   PaymentMethod = "bank"
)

// PaymentInfo 支付信息
type PaymentInfo struct {
	Method      PaymentMethod `json:"method"`
	PaypalEmail string        `json:"paypal_email,omitempty"`
	BankAccount string        `json:"bank_account,omitempty"`
	BankName    string        `json:"bank_name,omitempty"`
	BankBranch  string        `json:"bank_branch,omitempty"`
}

// Withdrawal 提现实体
type Withdrawal struct {
	ID            uint64           `gorm:"primarykey" json:"id"`
	WithdrawalNo  string           `gorm:"type:varchar(255);uniqueIndex;not null" json:"withdrawal_no"`
	UserID        uint64           `gorm:"index;not null" json:"user_id"`
	Amount        float64          `gorm:"type:decimal(10,2);not null;check:amount >= 0 AND amount <= 1000" json:"amount"`
	PaymentInfo   postgres.JSON    `gorm:"type:jsonb" json:"payment_info"` // 支付信息
	Status        WithdrawalStatus `gorm:"type:smallint;default:1;not null" json:"status"`
	ProcessTime   *time.Time       `json:"process_time"`                            // 处理时间
	CompleteTime  *time.Time       `json:"complete_time"`                           // 完成时间
	FailReason    string           `gorm:"type:text" json:"fail_reason"`            // 失败原因
	ProcessorID   uint64           `json:"processor_id"`                            // 处理人ID
	TransactionID string           `gorm:"type:varchar(255)" json:"transaction_id"` // 支付平台交易ID
	CreatedAt     time.Time        `json:"created_at"`
	UpdatedAt     time.Time        `json:"updated_at"`
	DeletedAt     gorm.DeletedAt   `gorm:"index" json:"-"`
}

func (w WithdrawalStatus) String() string {
	switch w {
	case WithdrawalStatusPending:
		return "pending"
	case WithdrawalStatusProcessing:
		return "processing"
	case WithdrawalStatusCompleted:
		return "completed"
	case WithdrawalStatusFailed:
		return "failed"
	case WithdrawalStatusCanceled:
		return "canceled"
	default:
		return "pending"
	}
}

// MarshalJSON 自定义JSON序列化
func (w WithdrawalStatus) MarshalJSON() ([]byte, error) {
	var status string
	switch w {
	case WithdrawalStatusPending:
		status = "pending"
	case WithdrawalStatusProcessing:
		status = "processing"
	case WithdrawalStatusCompleted:
		status = "completed"
	case WithdrawalStatusFailed:
		status = "failed"
	case WithdrawalStatusCanceled:
		status = "canceled"
	default:
		status = "unknown"
	}
	return json.Marshal(status)
}

// UnmarshalJSON 自定义JSON反序列化
func (w *WithdrawalStatus) UnmarshalJSON(data []byte) error {
	var status string
	if err := json.Unmarshal(data, &status); err != nil {
		return err
	}

	switch status {
	case "pending":
		*w = WithdrawalStatusPending
	case "processing":
		*w = WithdrawalStatusProcessing
	case "completed":
		*w = WithdrawalStatusCompleted
	case "failed":
		*w = WithdrawalStatusFailed
	case "canceled":
		*w = WithdrawalStatusCanceled
	default:
		*w = WithdrawalStatusPending
	}
	return nil
}
