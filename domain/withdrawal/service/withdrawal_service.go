package service

import (
	"bonusearned/domain/withdrawal/entity"
	"bonusearned/domain/withdrawal/repository"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// WithdrawalService 提现领域服务接口
type WithdrawalService interface {
	// GetWithdrawalListByCondition 获取用户的提现记录
	GetWithdrawalListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Withdrawal, int64, *ecode.Error)
}

type withdrawalService struct {
	repo repository.WithdrawalRepository
}

// NewWithdrawalService 创建提现领域服务
func NewWithdrawalService(repo repository.WithdrawalRepository) WithdrawalService {
	return &withdrawalService{
		repo: repo,
	}
}

func (s *withdrawalService) GetWithdrawalListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Withdrawal, int64, *ecode.Error) {
	return s.repo.GetWithdrawalListByCondition(ctx, condition)
}

// isValidStatusTransition 检查状态转换是否合法
func (s *withdrawalService) isValidStatusTransition(from, to entity.WithdrawalStatus) bool {
	switch from {
	case entity.WithdrawalStatusPending:
		return to == entity.WithdrawalStatusProcessing || to == entity.WithdrawalStatusCanceled
	case entity.WithdrawalStatusProcessing:
		return to == entity.WithdrawalStatusCompleted || to == entity.WithdrawalStatusFailed
	default:
		return false
	}
}
