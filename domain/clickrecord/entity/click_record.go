package entity

import (
	"gorm.io/gorm"
	"time"
)

// ClickRecord 点击记录实体
type ClickRecord struct {
	ID           uint64    `gorm:"primaryKey" json:"id"`
	ClickDate    time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"click_date"`
	ClickID      string    `gorm:"type:varchar(63);uniqueIndex;not null" json:"click_id"`
	TrackUID     string    `gorm:"type:varchar(63)" json:"track_uid"`              // 生成的唯一追踪UID
	UserCode     string    `gorm:"type:varchar(63)" json:"user_code"`              // 实际用户编码
	MerchantCode string    `gorm:"type:varchar(63);not null" json:"merchant_code"` // 商家编码
	UserID       uint64    `json:"user_id"`
	MerchantID   uint64    `json:"merchant_id"`
	Sub1         string    `gorm:"type:varchar(63)" json:"sub1"`
	CreatedAt    time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
}

// BeforeCreate GORM 的钩子，在创建记录前自动设置时间
func (c *ClickRecord) BeforeCreate(tx *gorm.DB) error {
	if c.CreatedAt.IsZero() {
		c.CreatedAt = time.Now()
	}
	if c.ClickDate.IsZero() {
		c.ClickDate = time.Now()
	}
	return nil
}
