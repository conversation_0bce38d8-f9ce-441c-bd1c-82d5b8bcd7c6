package repository

import (
	"bonusearned/domain/clickrecord/entity"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// ClickRepository 点击记录仓储接口
type ClickRepository interface {
	// CreateClickRecord 创建点击记录
	CreateClickRecord(ctx *gin.Context, record *entity.ClickRecord) *ecode.Error
	// GetClickRecordDetailById 根据ID获取点击记录
	GetClickRecordDetailById(ctx *gin.Context, id uint64) (*entity.ClickRecord, *ecode.Error)
	// GetClickRecordListByCondition 获取用户的点击记录
	GetClickRecordListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.ClickRecord, int64, *ecode.Error)
	// DeleteClickRecord 删除点击记录
	DeleteClickRecord(ctx *gin.Context, id uint64) *ecode.Error
}
