package service

import (
	"bonusearned/domain/clickrecord/entity"
	"bonusearned/domain/clickrecord/repository"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ClickRecordService 点击记录领域服务接口
type ClickRecordService interface {
	// CreateClickRecord 创建点击记录
	CreateClickRecord(ctx *gin.Context, record *entity.ClickRecord) *ecode.Error
	// GetClickRecordDetailById 根据ID获取点击记录
	GetClickRecordDetailById(ctx *gin.Context, id uint64) (*entity.ClickRecord, *ecode.Error)
	// GetClickRecordListByCondition 获取用户的点击记录
	GetClickRecordListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.ClickRecord, int64, *ecode.Error)
	// DeleteClickRecord 删除点击记录
	DeleteClickRecord(ctx *gin.Context, id uint64) *ecode.Error
}

type clickRecordService struct {
	repo   repository.ClickRepository
	logger *zap.Logger
}

// NewClickRecordService 创建点击记录领域服务
func NewClickRecordService(repo repository.ClickRepository, logger *zap.Logger) ClickRecordService {
	return &clickRecordService{
		repo:   repo,
		logger: logger,
	}
}

func (s *clickRecordService) CreateClickRecord(ctx *gin.Context, record *entity.ClickRecord) *ecode.Error {
	// 创建商家
	err := s.repo.CreateClickRecord(ctx, record)
	if err != nil {
		s.logger.Error("Failed to create click record",
			zap.Any("record", record),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *clickRecordService) GetClickRecordDetailById(ctx *gin.Context, id uint64) (*entity.ClickRecord, *ecode.Error) {
	clickRecord, err := s.repo.GetClickRecordDetailById(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get click record from database",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return nil, err
	}

	return clickRecord, nil
}

func (s *clickRecordService) GetClickRecordListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.ClickRecord, int64, *ecode.Error) {
	clickRecord, total, err := s.repo.GetClickRecordListByCondition(ctx, condition)
	if err != nil {
		s.logger.Error("Failed to get click record from database",
			zap.Any("condition", condition),
			zap.Error(err),
		)
		return nil, 0, err
	}
	return clickRecord, total, nil
}

func (s *clickRecordService) DeleteClickRecord(ctx *gin.Context, id uint64) *ecode.Error {
	err := s.repo.DeleteClickRecord(ctx, id)
	if err != nil {
		s.logger.Error("Failed to delete click record",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return err
	}
	return nil
}
