package repository

import (
	"bonusearned/domain/blog/entity"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// BlogRepository 博客仓储接口
type BlogRepository interface {
	// CreateBlog 创建博客
	CreateBlog(ctx *gin.Context, blog *entity.Blog) *ecode.Error
	// UpdateBlog 更新博客
	UpdateBlog(ctx *gin.Context, blog *entity.Blog) *ecode.Error
	// DeleteBlog 删除博客
	DeleteBlog(ctx *gin.Context, id uint64) *ecode.Error
	// GetBlogDetailById 根据ID查找博客
	GetBlogDetailById(ctx *gin.Context, id uint64) (*entity.Blog, *ecode.Error)
	// GetBlogListByCondition 搜索博客
	GetBlogListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Blog, int64, *ecode.Error)
	GetBlogCount(ctx *gin.Context) (int64, *ecode.Error)
}
