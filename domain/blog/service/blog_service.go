package service

import (
	"bonusearned/domain/blog/entity"
	"bonusearned/domain/blog/repository"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type BlogService interface {
	// CreateBlog 创建博客
	CreateBlog(ctx *gin.Context, blog *entity.Blog) *ecode.Error
	// UpdateBlog 更新博客
	UpdateBlog(ctx *gin.Context, blog *entity.Blog) *ecode.Error
	// DeleteBlog 删除博客
	DeleteBlog(ctx *gin.Context, id uint64) *ecode.Error
	// GetBlogDetailById 根据ID查找博客
	GetBlogDetailById(ctx *gin.Context, id uint64) (*entity.Blog, *ecode.Error)
	// GetBlogListByCondition 搜索博客
	GetBlogListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Blog, int64, *ecode.Error)
	GetBlogCount(ctx *gin.Context) (int64, *ecode.Error)
}

type BlogServiceImpl struct {
	repo   repository.BlogRepository
	logger *zap.Logger
}

func NewBlogService(repo repository.BlogRepository, logger *zap.Logger) BlogService {
	return &BlogServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

func (s *BlogServiceImpl) CreateBlog(ctx *gin.Context, blog *entity.Blog) *ecode.Error {
	// 创建 blog
	err := s.repo.CreateBlog(ctx, blog)
	if err != nil {
		s.logger.Error("Failed to create blog",
			zap.Any("blog", blog),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *BlogServiceImpl) UpdateBlog(ctx *gin.Context, blog *entity.Blog) *ecode.Error {
	// 更新商家
	err := s.repo.UpdateBlog(ctx, blog)
	if err != nil {
		s.logger.Error("Failed to update blog",
			zap.Any("blog", blog),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *BlogServiceImpl) DeleteBlog(ctx *gin.Context, id uint64) *ecode.Error {
	// 删除商家
	err := s.repo.DeleteBlog(ctx, id)
	if err != nil {
		s.logger.Error("Failed to delete blog",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return err
	}

	return nil
}

func (s *BlogServiceImpl) GetBlogDetailById(ctx *gin.Context, id uint64) (*entity.Blog, *ecode.Error) {
	blog, err := s.repo.GetBlogDetailById(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get blog from database",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return nil, err
	}
	return blog, nil
}

func (s *BlogServiceImpl) GetBlogListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Blog, int64, *ecode.Error) {
	blog, total, err := s.repo.GetBlogListByCondition(ctx, condition)
	if err != nil {
		s.logger.Error("Failed to get blogs from database",
			zap.Any("condition", condition),
			zap.Error(err),
		)
		return nil, 0, err
	}
	return blog, total, nil
}

func (s *BlogServiceImpl) GetBlogCount(ctx *gin.Context) (int64, *ecode.Error) {
	// 更新商家状态
	total, err := s.repo.GetBlogCount(ctx)
	if err != nil {
		s.logger.Error("Failed to get blog count",
			zap.Error(err),
		)
		return 0, err
	}

	return total, nil
}
