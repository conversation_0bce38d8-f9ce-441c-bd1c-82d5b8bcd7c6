package entity

import (
	"github.com/lib/pq"
	"time"
)

// Blog 博客实体
type Blog struct {
	ID            uint64         `json:"id" gorm:"primaryKey"`
	Title         string         `json:"title" gorm:"type:varchar(255);not null"`
	Description   string         `json:"description" gorm:"type:text"`
	Content       string         `json:"content" gorm:"type:text;not null"`
	FeaturedImage string         `json:"featured_image" gorm:"type:varchar(255)"`
	PublishedAt   time.Time      `json:"published_at"`
	Category      string         `json:"category" gorm:"type:varchar(255)"`
	Tags          pq.StringArray `json:"tags" gorm:"type:text[]"`
	MerchantID    uint64         `json:"merchant_id"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
}
