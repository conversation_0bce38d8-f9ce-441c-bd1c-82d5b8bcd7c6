package service

import (
	"bonusearned/config"
	"bonusearned/domain/user/entity"
	"bonusearned/domain/user/repository"
	"bonusearned/infra/cache/cachekey"
	"bonusearned/infra/cache/localcache"
	"bonusearned/infra/cache/rediscache"
	"bonusearned/infra/ecode"
	"bonusearned/interfaces/api/middleware"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
)

type UserService interface {
	Register(ctx *gin.Context, user *entity.User) *ecode.Error
	Login(ctx *gin.Context, email, password string) (*entity.User, string, *ecode.Error)
	GetUserDetailById(ctx *gin.Context, id uint64) (*entity.User, *ecode.Error)
	GetUserDetailByEmail(ctx *gin.Context, email string) (*entity.User, *ecode.Error)
	GetUserDetailByUserCode(ctx *gin.Context, userCode string) (*entity.User, *ecode.Error)
	UpdateProfile(ctx *gin.Context, id uint64, phone, nickname string) *ecode.Error
	UpdatePaymentInfo(ctx *gin.Context, id uint64, paymentInfo map[string]interface{}) *ecode.Error
	UpdateUserPassword(ctx *gin.Context, id uint64, oldPassword, newPassword string) *ecode.Error
	UpdateUserBalance(ctx *gin.Context, user *entity.User) *ecode.Error
	// BatchUpdateUserBalance 批量更新用户余额（仅供定时任务调用）
	BatchUpdateUserBalance(ctx *gin.Context, users []*entity.User) *ecode.Error
	Logout(ctx *gin.Context, id uint64) *ecode.Error
	GetUserListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.User, int64, *ecode.Error)
}

type UserServiceImpl struct {
	repo       repository.UserRepository
	redis      *redis.Client
	localCache *localcache.Cache
	redisCache *rediscache.Cache
	logger     *zap.Logger
}

func NewUserService(
	repo repository.UserRepository,
	redis *redis.Client,
	cfg *config.Config,
	logger *zap.Logger,
) UserService {
	return &UserServiceImpl{
		repo:       repo,
		redis:      redis,
		localCache: localcache.New(),
		redisCache: rediscache.New(redis),
		logger:     logger,
	}
}

func (s *UserServiceImpl) Register(ctx *gin.Context, user *entity.User) *ecode.Error {
	// 检查邮箱是否已存在
	if existingUser, _ := s.GetUserDetailByEmail(ctx, user.Email); existingUser != nil {
		return ecode.ErrUserExists
	}

	// 生成密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
	if err != nil {
		return ecode.ErrInternalServer
	}
	user.Password = string(hashedPassword)

	// 创建用户
	if err := s.repo.CreateUser(ctx, user); err != nil {
		return err
	}

	return nil
}

func (s *UserServiceImpl) Login(ctx *gin.Context, email, password string) (*entity.User, string, *ecode.Error) {
	// 获取用户信息
	user, err := s.GetUserDetailByEmail(ctx, email)
	if err != nil {
		return nil, "", err
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return nil, "", ecode.ErrInvalidPassword
	}

	// 使用中间件生成 token
	resp, err := middleware.GenerateLoginResponse(user.ID, user.UserCode, user.Email)
	if err != nil {
		return nil, "", ecode.ErrInternalServer
	}

	// 设置缓存
	cacheKey := cachekey.GenerateUserByEmailKey(user.Email)
	s.localCache.Set(ctx, cacheKey, user, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, user, cachekey.LongExpiration)

	return user, resp.Token, nil
}

func (s *UserServiceImpl) GetUserDetailById(ctx *gin.Context, id uint64) (*entity.User, *ecode.Error) {
	// 尝试从缓存获取
	cacheKey := cachekey.GenerateUserByIdKey(id)
	if user, ok := s.localCache.Get(ctx, cacheKey); ok {
		return user.(*entity.User), nil
	}

	// 从数据库获取
	user, err := s.repo.GetUserDetailById(ctx, id)
	if err != nil {
		return nil, err
	}

	// 设置缓存
	s.localCache.Set(ctx, cacheKey, user, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, user, cachekey.LongExpiration)

	return user, nil
}

func (s *UserServiceImpl) GetUserDetailByEmail(ctx *gin.Context, email string) (*entity.User, *ecode.Error) {
	// 尝试从缓存获取
	cacheKey := cachekey.GenerateUserByEmailKey(email)
	if user, ok := s.localCache.Get(ctx, cacheKey); ok {
		return user.(*entity.User), nil
	}

	// 从数据库获取
	user, err := s.repo.GetUserDetailByEmail(ctx, email)
	if err != nil {
		return nil, err
	}

	// 设置缓存
	s.localCache.Set(ctx, cacheKey, user, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, user, cachekey.LongExpiration)

	return user, nil
}

func (s *UserServiceImpl) GetUserDetailByUserCode(ctx *gin.Context, userCode string) (*entity.User, *ecode.Error) {
	// 尝试从缓存获取
	cacheKey := cachekey.GenerateUserByCodeKey(userCode)
	if user, ok := s.localCache.Get(ctx, cacheKey); ok {
		return user.(*entity.User), nil
	}

	// 从数据库获取
	user, err := s.repo.GetUserDetailByUserCode(ctx, userCode)
	if err != nil {
		return nil, err
	}

	// 设置缓存
	s.localCache.Set(ctx, cacheKey, user, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, user, cachekey.LongExpiration)

	return user, nil
}

func (s *UserServiceImpl) UpdateProfile(ctx *gin.Context, id uint64, phone, nickname string) *ecode.Error {
	// 获取用户信息
	user, err := s.GetUserDetailById(ctx, id)
	if err != nil {
		return err
	}

	// 更新信息
	user.Phone = phone
	user.Nickname = nickname

	// 保存到数据库
	if err := s.repo.UpdateProfile(ctx, user); err != nil {
		return err
	}

	// 删除缓存
	s.localCache.Delete(ctx, cachekey.GenerateUserByIdKey(id))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByIdKey(id))
	s.localCache.Delete(ctx, cachekey.GenerateUserByEmailKey(user.Email))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByEmailKey(user.Email))
	s.localCache.Delete(ctx, cachekey.GenerateUserByCodeKey(user.UserCode))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByCodeKey(user.UserCode))

	return nil
}

func (s *UserServiceImpl) UpdatePaymentInfo(ctx *gin.Context, id uint64, paymentInfo map[string]interface{}) *ecode.Error {
	// 更新支付信息
	if err := s.repo.UpdatePaymentInfo(ctx, id, paymentInfo); err != nil {
		return err
	}

	// 删除缓存
	user, err := s.GetUserDetailById(ctx, id)
	if err != nil {
		return err
	}

	s.localCache.Delete(ctx, cachekey.GenerateUserByIdKey(id))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByIdKey(id))
	s.localCache.Delete(ctx, cachekey.GenerateUserByEmailKey(user.Email))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByEmailKey(user.Email))
	s.localCache.Delete(ctx, cachekey.GenerateUserByCodeKey(user.UserCode))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByCodeKey(user.UserCode))

	return nil
}

func (s *UserServiceImpl) UpdateUserPassword(ctx *gin.Context, id uint64, oldPassword, newPassword string) *ecode.Error {
	// 获取用户信息
	user, err := s.GetUserDetailById(ctx, id)
	if err != nil {
		return err
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(oldPassword)); err != nil {
		return ecode.ErrInvalidPassword
	}

	// 生成新密码哈希
	hashedPassword, errc := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if errc != nil {
		return ecode.ErrInternalServer
	}

	// 更新密码
	if err := s.repo.UpdateUserPassword(ctx, id, string(hashedPassword)); err != nil {
		return err
	}

	// 删除缓存
	s.localCache.Delete(ctx, cachekey.GenerateUserByIdKey(id))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByIdKey(id))
	s.localCache.Delete(ctx, cachekey.GenerateUserByEmailKey(user.Email))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByEmailKey(user.Email))
	s.localCache.Delete(ctx, cachekey.GenerateUserByCodeKey(user.UserCode))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByCodeKey(user.UserCode))

	return nil
}

func (s *UserServiceImpl) UpdateUserBalance(ctx *gin.Context, user *entity.User) *ecode.Error {
	// 更新用户余额
	if err := s.repo.UpdateUserBalance(ctx, user); err != nil {
		return err
	}

	// 删除缓存
	s.localCache.Delete(ctx, cachekey.GenerateUserByIdKey(user.ID))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByIdKey(user.ID))
	s.localCache.Delete(ctx, cachekey.GenerateUserByEmailKey(user.Email))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByEmailKey(user.Email))
	s.localCache.Delete(ctx, cachekey.GenerateUserByCodeKey(user.UserCode))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByCodeKey(user.UserCode))

	return nil
}

// BatchUpdateUserBalance 批量更新用户余额（仅供定时任务调用）
func (s *UserServiceImpl) BatchUpdateUserBalance(ctx *gin.Context, users []*entity.User) *ecode.Error {
	if len(users) == 0 {
		return nil
	}

	// 批量更新用户余额
	if err := s.repo.BatchUpdateUserBalance(ctx, users); err != nil {
		return err
	}

	// 批量删除缓存
	for _, user := range users {
		s.localCache.Delete(ctx, cachekey.GenerateUserByIdKey(user.ID))
		s.redisCache.Delete(ctx, cachekey.GenerateUserByIdKey(user.ID))
		s.localCache.Delete(ctx, cachekey.GenerateUserByEmailKey(user.Email))
		s.redisCache.Delete(ctx, cachekey.GenerateUserByEmailKey(user.Email))
		s.localCache.Delete(ctx, cachekey.GenerateUserByCodeKey(user.UserCode))
		s.redisCache.Delete(ctx, cachekey.GenerateUserByCodeKey(user.UserCode))
	}

	return nil
}

func (s *UserServiceImpl) Logout(ctx *gin.Context, id uint64) *ecode.Error {
	// 获取用户信息
	user, err := s.GetUserDetailById(ctx, id)
	if err != nil {
		return err
	}

	// 删除缓存
	s.localCache.Delete(ctx, cachekey.GenerateUserByIdKey(user.ID))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByIdKey(user.ID))

	s.localCache.Delete(ctx, cachekey.GenerateUserByEmailKey(user.Email))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByEmailKey(user.Email))

	s.localCache.Delete(ctx, cachekey.GenerateUserByCodeKey(user.UserCode))
	s.redisCache.Delete(ctx, cachekey.GenerateUserByCodeKey(user.UserCode))

	return nil
}

func (s *UserServiceImpl) GetUserListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.User, int64, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateUserListKey(condition)

	// 1. 尝试从内存缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if cacheData, ok := value.(*entity.UserListCacheData); ok {
			return cacheData.UserList, cacheData.Total, nil
		}
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if cacheData, ok := value.(*entity.UserListCacheData); ok {
			// 设置内存缓存
			s.localCache.Set(ctx, cacheKey, cacheData, cachekey.LongExpiration)
			return cacheData.UserList, cacheData.Total, nil
		}
	}

	// 3. 从数据库获取
	userList, total, err := s.repo.GetUserListByCondition(ctx, condition)
	if err != nil {
		return nil, 0, err
	}

	// 创建缓存数据结构
	cacheData := &entity.UserListCacheData{
		UserList: userList,
		Total:    total,
	}

	// 设置缓存
	s.localCache.Set(ctx, cacheKey, cacheData, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, cacheData, cachekey.LongExpiration)
	return userList, total, nil
}
