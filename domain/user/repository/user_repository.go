package repository

import (
	"bonusearned/domain/user/entity"
	"bonusearned/infra/ecode"

	"github.com/gin-gonic/gin"
)

// UserRepository 用户仓储接口
type UserRepository interface {
	// GetUserDetailById 根据ID获取用户详情
	GetUserDetailById(ctx *gin.Context, id uint64) (*entity.User, *ecode.Error)
	// GetUserDetailByEmail 根据邮箱获取用户详情
	GetUserDetailByEmail(ctx *gin.Context, email string) (*entity.User, *ecode.Error)
	// GetUserDetailByUserCode 根据用户代码获取用户详情
	GetUserDetailByUserCode(ctx *gin.Context, userCode string) (*entity.User, *ecode.Error)
	// CreateUser 创建用户
	CreateUser(ctx *gin.Context, user *entity.User) *ecode.Error
	// UpdateProfile 更新用户基本信息（仅限：phone、nickname）
	UpdateProfile(ctx *gin.Context, user *entity.User) *ecode.Error
	// UpdatePaymentInfo 更新用户支付信息
	UpdatePaymentInfo(ctx *gin.Context, id uint64, paymentInfo map[string]interface{}) *ecode.Error
	// UpdateUserPassword 更新用户密码
	UpdateUserPassword(ctx *gin.Context, id uint64, hashedPassword string) *ecode.Error
	// UpdateUserBalance 更新用户余额（仅供定时任务调用）
	UpdateUserBalance(ctx *gin.Context, user *entity.User) *ecode.Error
	// DeleteUser 删除用户
	DeleteUser(ctx *gin.Context, id uint64) *ecode.Error

	GetUserListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.User, int64, *ecode.Error)
	// BatchUpdateUserBalance 批量更新用户余额（仅供定时任务调用）
	BatchUpdateUserBalance(ctx *gin.Context, users []*entity.User) *ecode.Error
}
