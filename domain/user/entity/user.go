package entity

import (
	"bonusearned/infra/database/postgres"
	"gorm.io/gorm"
	"time"
)

// User 用户实体
type User struct {
	ID          uint64        `gorm:"primarykey" json:"id"`
	Email       string        `gorm:"type:varchar(255);uniqueIndex;not null" json:"email"`
	UserCode    string        `gorm:"type:varchar(50);uniqueIndex;not null" json:"user_code"`
	Phone       string        `gorm:"type:varchar(30)" json:"phone"`
	Password    string        `gorm:"type:varchar(255);not null" json:"password"`
	Nickname    string        `gorm:"type:varchar(50)" json:"nickname"`
	PaymentInfo postgres.JSON `gorm:"type:jsonb;default:'{}'" json:"payment_info,omitempty"`
	// 用户余额
	UserBalance postgres.JSON  `gorm:"type:jsonb;default:'{}'" json:"user_balance,omitempty"`
	Status      int8           `gorm:"type:smallint;default:1;not null" json:"status"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// UserBalance 用户余额
type UserBalance struct {
	TotalAmount    float64 `gorm:"type:decimal(10,2);default:0;not null" json:"total_amount"`
	PendingAmount  float64 `gorm:"type:decimal(10,2);default:0;not null" json:"pending_amount"`
	PaidAmount     float64 `gorm:"type:decimal(10,2);default:0;not null" json:"paid_amount"`
	ApprovedAmount float64 `gorm:"type:decimal(10,2);default:0;not null" json:"approved_amount"`
}

type BankInfo struct {
	BankCurrency      string `json:"bank_currency"`
	BankCountry       string `json:"bank_country"`
	BankName          string `json:"bank_name"`
	BankAddress       string `json:"bank_address"`
	BankAccountName   string `json:"bank_account_name"`
	BankAccountNumber int    `json:"bank_account_number"`
	BankRoutingNumber int    `json:"bank_routing_number"`
	BankSwiftCode     string `json:"bank_swift_code"`
	BankIBAN          string `json:"bank_iban"`
	BankSortCode      string `json:"bank_sort_code"`
	BankTaxpayerID    string `json:"bank_taxpayer_id"`
	DefaultMethod     bool   `json:"default_method"` // 主要支付方式，可选值：bank, paypal
}

type PaypalInfo struct {
	PaypalEmail   string `json:"paypal_email"`
	PaypalName    string `json:"paypal_name"`
	DefaultMethod bool   `json:"default_method"` // 主要支付方式，可选值：bank, paypal
}

type PaymentInfo struct {
	BankInfo   *BankInfo   `json:"bank_info"`
	PaypalInfo *PaypalInfo `json:"paypal_info"`
}

// UserListCacheData 用户列表缓存数据
type UserListCacheData struct {
	UserList []*User
	Total    int64
}
