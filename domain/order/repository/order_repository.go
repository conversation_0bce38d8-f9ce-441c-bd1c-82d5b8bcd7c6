package repository

import (
	"bonusearned/domain/order/entity"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

type OrderRepository interface {
	// GetOrderDetailById 根据ID获取订单
	GetOrderDetailById(ctx *gin.Context, id uint64) (*entity.Order, *ecode.Error)
	// GetOrderListByCondition 获取订单列表
	GetOrderListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Order, int64, *ecode.Error)
	// CreateOrder 创建订单
	CreateOrder(ctx *gin.Context, order *entity.Order) *ecode.Error
	// UpdateOrder 更新订单
	UpdateOrder(ctx *gin.Context, order *entity.Order) *ecode.Error
	// UpdateOrderStatus 更新订单状态
	UpdateOrderStatus(ctx *gin.Context, id uint64, status entity.OrderStatus) *ecode.Error
	// DeleteOrder 删除订单
	DeleteOrder(ctx *gin.Context, id uint64) *ecode.Error
	// BatchCreateOrder 批量创建订单
	BatchCreateOrder(ctx *gin.Context, orders []*entity.Order) *ecode.Error
	// BatchUpdateOrder 批量更新订单
	BatchUpdateOrder(ctx *gin.Context, orders []*entity.Order) *ecode.Error
	// BatchUpdateOrderStatus 批量更新订单状态
	BatchUpdateOrderStatus(ctx *gin.Context, ids []uint64, status entity.OrderStatus) *ecode.Error
}
