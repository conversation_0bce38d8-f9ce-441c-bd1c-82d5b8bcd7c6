package service

import (
	"bonusearned/domain/order/entity"
	"bonusearned/domain/order/repository"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

type OrderService interface {
	// GetOrderDetailById 根据ID获取订单
	GetOrderDetailById(ctx *gin.Context, id uint64) (*entity.Order, *ecode.Error)
	// GetOrderListByCondition 获取订单列表
	GetOrderListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Order, int64, *ecode.Error)
	// CreateOrder 创建订单
	CreateOrder(ctx *gin.Context, order *entity.Order) *ecode.Error
	// UpdateOrder 更新订单
	UpdateOrder(ctx *gin.Context, order *entity.Order) *ecode.Error
	// UpdateOrderStatus 更新订单状态
	UpdateOrderStatus(ctx *gin.Context, id uint64, status entity.OrderStatus) *ecode.Error
	// DeleteOrder 删除订单
	DeleteOrder(ctx *gin.Context, id uint64) *ecode.Error
	// BatchCreateOrder 批量创建订单
	BatchCreateOrder(ctx *gin.Context, orders []*entity.Order) *ecode.Error
	// BatchUpdateOrder 批量更新订单
	BatchUpdateOrder(ctx *gin.Context, orders []*entity.Order) *ecode.Error
	// BatchUpdateOrderStatus 批量更新订单状态
	BatchUpdateOrderStatus(ctx *gin.Context, ids []uint64, status entity.OrderStatus) *ecode.Error
}

type OrderServiceImpl struct {
	repo   repository.OrderRepository
	redis  *redis.Client
	logger *zap.Logger
}

func NewOrderService(
	repo repository.OrderRepository,
	redis *redis.Client,
	logger *zap.Logger,
) OrderService {
	return &OrderServiceImpl{
		repo:   repo,
		redis:  redis,
		logger: logger,
	}
}

func (s *OrderServiceImpl) GetOrderDetailById(ctx *gin.Context, id uint64) (*entity.Order, *ecode.Error) {
	order, err := s.repo.GetOrderDetailById(ctx, id)
	if err != nil {
		return nil, err
	}
	return order, nil
}

func (s *OrderServiceImpl) GetOrderListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Order, int64, *ecode.Error) {
	orderList, total, err := s.repo.GetOrderListByCondition(ctx, condition)
	if err != nil {
		return nil, 0, err
	}
	return orderList, total, nil
}

func (s *OrderServiceImpl) CreateOrder(ctx *gin.Context, order *entity.Order) *ecode.Error {
	err := s.repo.CreateOrder(ctx, order)
	if err != nil {
		return err
	}
	return nil
}

func (s *OrderServiceImpl) UpdateOrder(ctx *gin.Context, order *entity.Order) *ecode.Error {
	err := s.repo.UpdateOrder(ctx, order)
	if err != nil {
		return err
	}
	return nil
}

func (s *OrderServiceImpl) UpdateOrderStatus(ctx *gin.Context, id uint64, status entity.OrderStatus) *ecode.Error {
	err := s.repo.UpdateOrderStatus(ctx, id, status)
	if err != nil {
		return err
	}
	return nil
}

func (s *OrderServiceImpl) DeleteOrder(ctx *gin.Context, id uint64) *ecode.Error {
	err := s.repo.DeleteOrder(ctx, id)
	if err != nil {
		return err
	}
	return nil
}

func (s *OrderServiceImpl) BatchCreateOrder(ctx *gin.Context, orders []*entity.Order) *ecode.Error {
	err := s.repo.BatchCreateOrder(ctx, orders)
	if err != nil {
		return err
	}
	return nil
}

func (s *OrderServiceImpl) BatchUpdateOrder(ctx *gin.Context, orders []*entity.Order) *ecode.Error {
	err := s.repo.BatchUpdateOrder(ctx, orders)
	if err != nil {
		return err
	}
	return nil
}

func (s *OrderServiceImpl) BatchUpdateOrderStatus(ctx *gin.Context, ids []uint64, status entity.OrderStatus) *ecode.Error {
	err := s.repo.BatchUpdateOrderStatus(ctx, ids, status)
	if err != nil {
		return err
	}
	return nil
}
