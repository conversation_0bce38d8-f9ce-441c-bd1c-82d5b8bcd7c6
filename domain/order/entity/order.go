package entity

import (
	"encoding/json"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"time"
)

// OrderStatus 订单状态
type OrderStatus int8

const (
	OrderStatusPending  OrderStatus = 1 // 待确认，默认订单为待确认
	OrderStatusApproved OrderStatus = 2 // 已确认，当上级联盟已经支付该订单时，才为已确认
	OrderStatusRejected OrderStatus = 3 // 已取消，当上级联盟取消该订单时，为已取消
	OrderStatusPaying   OrderStatus = 4 // 支付中，不需要用户发起提现请求，规定好时间自动从已确认变为支付中
	OrderStatusPaid     OrderStatus = 5 // 已结算，当自己平台支付该订单时，为已结算
)

// PlatformOrderRejected 上级联盟中，order可能的状态
var PlatformOrderRejected = []string{
	"rejected", "-20", "Rejected", "expired", "declined",
}
var PlatformOrderPaid = []string{
	"paid", "Paid",
}

func (s OrderStatus) String() string {
	switch s {
	case OrderStatusPending:
		return "pending"
	case OrderStatusApproved:
		return "approved"
	case OrderStatusRejected:
		return "rejected"
	case OrderStatusPaying:
		return "paying"
	case OrderStatusPaid:
		return "paid"
	default:
		return "pending"
	}
}

// MarshalJSON 实现 json.Marshaler 接口
func (s OrderStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(s.String())
}

// Order 订单实体
type Order struct {
	ID                   uint64          `gorm:"primarykey" json:"id"`
	ConversionId         string          `gorm:"uniqueIndex;not null" json:"conversion_id"` // 转化id，不可重复，不同id展示的原因是：不能让用户发现有多少订单（根据自增id可以判断出订单量，这算是信息泄漏了）
	UserID               uint64          `gorm:"index;not null" json:"user_id"`
	UserCode             string          `gorm:"type:varchar(50);index;not null" json:"user_code"` // 用户编码
	MerchantID           uint64          `gorm:"index;not null" json:"merchant_id"`
	MerchantCode         string          `gorm:"type:varchar(50);index;not null" json:"merchant_code"` // 商家编码
	OrderID              string          `gorm:"type:varchar(50);not null" json:"order_id"`            // 订单id、可以重复
	ClickID              string          `gorm:"type:varchar(63);not null" json:"click_id"`            // 点击id，可能重复
	Sub1                 string          `gorm:"type:varchar(63)" json:"sub1"`                         // 点击id，可能重复
	OrderAmount          decimal.Decimal `gorm:"type:decimal(10,2);not null" json:"order_amount"`      // 订单金额
	CashbackAmount       decimal.Decimal `gorm:"type:decimal(10,2);not null" json:"cashback_amount"`   // 佣金金额
	Status               OrderStatus     `gorm:"type:smallint;default:1;not null" json:"status"`       // 订单状态
	PlatformStatus       string          `gorm:"type:varchar(20);not null" json:"platform_status"`     // 平台订单状态 - 不可展示给用户，仅仅用于内部记录
	PlatformType         string          `gorm:"type:varchar(63)" json:"platform_type"`                // 平台类型：cj、awin
	PlatformMerchantID   string          `gorm:"type:varchar(63)" json:"platform_merchant_id"`         // 平台商家ID
	PlatformConversionId string          `gorm:"type:varchar(63)" json:"platform_conversion_id"`       // 平台转化ID

	OrderTime    time.Time      `json:"order_time"`                     // 下单时间
	ApproveTime  time.Time      `json:"approve_time"`                   // 确认时间
	CancelTime   time.Time      `json:"cancel_time"`                    // 取消时间
	PaidTime     time.Time      `json:"paid_time"`                      // 已支付时间
	LastSyncTime time.Time      `json:"last_sync_time"`                 // 最后同步时间
	CancelReason string         `gorm:"type:text" json:"cancel_reason"` // 取消原因
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}
