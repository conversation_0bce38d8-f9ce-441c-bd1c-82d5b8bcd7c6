package entity

import (
	"time"
)

const CouponTypeWithCoupon = "coupon"

// Coupon 优惠券实体
type Coupon struct {
	ID           uint64     `json:"id" gorm:"id"`
	MerchantID   uint64     `json:"merchant_id" gorm:"merchant_id"`
	Code         string     `json:"code" gorm:"code"`
	Featured     bool       `json:"featured" gorm:"default:false;not null"`
	DiscountRate string     `json:"discount_rate" gorm:"discount_rate"`
	Title        string     `json:"title" gorm:"title"`
	Description  string     `json:"description" gorm:"description"`
	CouponType   string     `json:"coupon_type" gorm:"coupon_type"`
	PlatformType string     `json:"platform_type"  gorm:"platform_type"`
	StartedAt    *time.Time `json:"started_at" gorm:"started_at"`
	EndedAt      *time.Time `json:"ended_at,omitempty" gorm:"ended_at"`
	CreatedAt    time.Time  `json:"created_at" gorm:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

// IsValid 检查优惠券是否有效
func (c *Coupon) IsValid() bool {
	now := time.Now()
	if now.Before(*c.StartedAt) {
		return false
	}
	if c.EndedAt != nil && now.After(*c.EndedAt) {
		return false
	}
	return true
}

// IsExpired 检查优惠券是否过期
func (c *Coupon) IsExpired() bool {
	if c.EndedAt == nil {
		return false
	}
	return time.Now().After(*c.EndedAt)
}
