package service

import (
	"bonusearned/domain/coupon/entity"
	"bonusearned/domain/coupon/repository"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// CouponService 优惠券领域服务
type CouponService interface {
	// CreateCoupon 创建优惠券
	CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error
	// UpdateCoupon 更新优惠券
	UpdateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error
	// DeleteCoupon 删除优惠券
	DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error
	// GetCouponDetailById 根据ID查找优惠券
	GetCouponDetailById(ctx *gin.Context, id uint64) (*entity.Coupon, *ecode.Error)
	// GetCouponDetailByCode 根据优惠券码查找优惠券
	GetCouponDetailByCode(ctx *gin.Context, code string) (*entity.Coupon, *ecode.Error)
	// GetCouponListByCondition 查找优惠券列表
	GetCouponListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Coupon, int64, *ecode.Error)
	// BatchCreateCoupon 批量创建优惠券
	BatchCreateCoupon(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error
	// BatchUpdateCoupon 批量更新优惠券
	BatchUpdateCoupon(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error
	// BatchDeleteCoupon 批量删除优惠券
	BatchDeleteCoupon(ctx *gin.Context, ids []uint64) *ecode.Error
}

type couponService struct {
	repo repository.CouponRepository
}

// NewCouponService 创建优惠券领域服务
func NewCouponService(repo repository.CouponRepository) CouponService {
	return &couponService{
		repo: repo,
	}
}

func (s *couponService) CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error {
	// 创建 coupon
	err := s.repo.CreateCoupon(ctx, coupon)
	if err != nil {
		return err
	}
	return nil
}

func (s *couponService) UpdateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error {
	// 更新 coupon
	err := s.repo.UpdateCoupon(ctx, coupon)
	if err != nil {
		return err
	}
	return nil
}

func (s *couponService) DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error {
	// 删除 coupon
	err := s.repo.DeleteCoupon(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

func (s *couponService) GetCouponDetailById(ctx *gin.Context, id uint64) (*entity.Coupon, *ecode.Error) {
	coupon, err := s.repo.GetCouponDetailById(ctx, id)
	if err != nil {
		return nil, err
	}
	return coupon, nil
}

func (s *couponService) GetCouponDetailByCode(ctx *gin.Context, code string) (*entity.Coupon, *ecode.Error) {
	coupon, err := s.repo.GetCouponDetailByCode(ctx, code)
	if err != nil {
		return nil, err
	}
	return coupon, nil
}

func (s *couponService) GetCouponListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Coupon, int64, *ecode.Error) {
	couponList, total, err := s.repo.GetCouponListByCondition(ctx, condition)
	if err != nil {
		return nil, 0, err
	}
	return couponList, total, nil
}

func (s *couponService) BatchCreateCoupon(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error {
	err := s.repo.BatchCreateCoupon(ctx, coupons)
	if err != nil {
		return err
	}
	return nil
}

func (s *couponService) BatchUpdateCoupon(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error {
	err := s.repo.BatchUpdateCoupon(ctx, coupons)
	if err != nil {
		return err
	}
	return nil
}

func (s *couponService) BatchDeleteCoupon(ctx *gin.Context, ids []uint64) *ecode.Error {
	err := s.repo.BatchDeleteCoupon(ctx, ids)
	if err != nil {
		return err
	}
	return nil
}
