package repository

import (
	"bonusearned/domain/coupon/entity"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// CouponRepository 优惠券仓储接口
type CouponRepository interface {
	// CreateCoupon 创建优惠券
	CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error
	// UpdateCoupon 更新优惠券
	UpdateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error
	// DeleteCoupon 删除优惠券
	DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error
	// GetCouponDetailById 根据ID查找优惠券
	GetCouponDetailById(ctx *gin.Context, id uint64) (*entity.Coupon, *ecode.Error)
	// GetCouponDetailByCode 根据优惠券码查找优惠券
	GetCouponDetailByCode(ctx *gin.Context, code string) (*entity.Coupon, *ecode.Error)
	// GetCouponListByCondition 查找优惠券列表
	GetCouponListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Coupon, int64, *ecode.Error)
	// BatchCreateCoupon 批量创建优惠券
	BatchCreateCoupon(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error
	// BatchUpdateCoupon 批量更新优惠券
	BatchUpdateCoupon(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error
	// BatchDeleteCoupon 批量删除优惠券
	BatchDeleteCoupon(ctx *gin.Context, ids []uint64) *ecode.Error
}
