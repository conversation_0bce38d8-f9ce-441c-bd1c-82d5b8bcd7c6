-- 为国家表添加商家数量字段
ALTER TABLE countries ADD COLUMN IF NOT EXISTS merchant_count BIGINT DEFAULT 0 NOT NULL;

-- 删除不在支持列表中的国家
DELETE FROM countries WHERE code NOT IN (
    'US', 'CA', 'AT', 'DE', 'FR', 'UK', 'ES', 'IT', 'MX', 'IN', 
    'PT', 'PL', 'NL', 'AU', 'BE', 'BR', 'CL', 'HK', 'AR', 'FI', 
    'SE', 'SG', 'DK', 'NO', 'PE', 'CZ', 'TR', 'IE', 'SA', 'CH', 
    'MY', 'RO', 'AE', 'NZ'
);

-- 更新现有国家的名称（如果需要）
UPDATE countries SET name = 'Czechia' WHERE code = 'CZ';
UPDATE countries SET name = 'Turkiye' WHERE code = 'TR';

-- 插入缺失的国家（如果不存在）
INSERT INTO countries (name, code, flag, merchant_count, status) VALUES
('Romania', 'RO', '🇷🇴', 0, 1)
ON CONFLICT (code) DO NOTHING;

-- 初始化商家数量统计
UPDATE countries SET merchant_count = (
    SELECT COUNT(*) 
    FROM merchants 
    WHERE merchants.country_id = countries.id 
    AND merchants.deleted_at IS NULL
) WHERE countries.deleted_at IS NULL;
