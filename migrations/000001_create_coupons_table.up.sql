CREATE TABLE coupons (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL,
    code VA<PERSON>HAR(50) NOT NULL,
    commission_rate VARCHAR(10) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    type VA<PERSON>HAR(20) NOT NULL DEFAULT 'coupon',
    started_at TIMESTAMPTZ NOT NULL,
    ended_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_merchant
        FOREIGN KEY(merchant_id)
        REFERENCES merchants(id)
        ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_coupons_merchant ON coupons(merchant_id);
CREATE INDEX idx_coupons_dates ON coupons(started_at, ended_at);
CREATE UNIQUE INDEX idx_coupons_code_merchant ON coupons(merchant_id, code) WHERE ended_at IS NULL OR ended_at > CURRENT_TIMESTAMP;
