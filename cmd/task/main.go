package main

import (
	"bonusearned/config"
	"bonusearned/infra/database"
	"bonusearned/infra/logger"
	"bonusearned/interfaces/task/router"
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	"github.com/joho/godotenv"
	"go.uber.org/zap"
)

func main() {
	// 获取环境配置的优先级：
	// 1. 命令行参数
	// 2. 环境变量
	// 3. .env 文件
	// 4. 默认值

	var env string
	flag.StringVar(&env, "env", "", "environment (local, test, live)")
	flag.Parse()

	// 如果没有通过命令行指定环境，则尝试从环境变量获取
	if env == "" {
		env = os.Getenv("APP_ENV")
	}

	// 如果环境变量也没有设置，则尝试加载 .env 文件
	if env == "" {
		// 获取项目根目录
		rootDir := findProjectRoot()

		// 加载 .env 文件
		if err := godotenv.Load(filepath.Join(rootDir, "env")); err != nil {
			log.Printf("Warning: Error loading .env file: %v", err)
		}

		env = os.Getenv("APP_ENV")
	}

	// 如果所有方式都没有设置环境，则使用默认值
	if env == "" {
		env = "local"
	}

	// 加载配置
	cfg, err := config.LoadConfig(env)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger, err := logger.NewLogger(cfg.Logger)
	if err != nil {
		log.Fatalf("Failed to create logger: %v", err)
	}
	defer logger.Sync()

	// 连接数据库
	postgres, err := database.NewPostgresDB(cfg.Postgres)
	if err != nil {
		logger.Fatal("Failed to connect to database", zap.Error(err))
	}

	// 连接 Redis
	redisClient, err := database.NewRedisClient(cfg.Redis)
	if err != nil {
		logger.Fatal("Failed to connect to redis", zap.Error(err))
	}
	defer redisClient.Close()

	// 创建上下文和取消函数
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 设置信号处理
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动 task 服务
	server := router.NewServer(cfg, postgres, redisClient, logger)
	addr := fmt.Sprintf("%s:%d", cfg.Task.Host, cfg.Task.Port)

	// 在goroutine中启动服务器
	go func() {
		logger.Info("Starting server", zap.String("addr", addr))
		if err := server.Run(addr); err != nil && err != http.ErrServerClosed {
			logger.Error("Server error", zap.Error(err))
			cancel()
		}
	}()

	// 等待信号
	select {
	case sig := <-signalChan:
		logger.Info("Received signal", zap.String("signal", sig.String()))
	case <-ctx.Done():
		logger.Info("Server context cancelled")
	}

	// 优雅关闭
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Error("Error during server shutdown", zap.Error(err))
	}

	// 关闭数据库连接
	sqlDB, err := postgres.DB()
	if err != nil {
		logger.Error("Error getting database instance", zap.Error(err))
	} else {
		if err := sqlDB.Close(); err != nil {
			logger.Error("Error closing database", zap.Error(err))
		}
	}

	logger.Info("Server shutdown completed")
}

// findProjectRoot 查找项目根目录
func findProjectRoot() string {
	// 从当前目录开始向上查找，直到找到包含 .env 文件的目录
	dir, err := os.Getwd()
	if err != nil {
		return ""
	}

	for {
		if _, err := os.Stat(filepath.Join(dir, "env")); err == nil {
			return dir
		}

		parent := filepath.Dir(dir)
		if parent == dir {
			return ""
		}
		dir = parent
	}
}
