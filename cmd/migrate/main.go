package main

import (
	"bonusearned/config"
	blogentity "bonusearned/domain/blog/entity"
	clickrecordentity "bonusearned/domain/clickrecord/entity"
	couponentity "bonusearned/domain/coupon/entity"
	merchantentity "bonusearned/domain/merchant/entity"
	orderentity "bonusearned/domain/order/entity"
	userentity "bonusearned/domain/user/entity"
	withdrawalentity "bonusearned/domain/withdrawal/entity"
	"bonusearned/infra/database"
	"flag"
	"log"
	"os"
	"path/filepath"

	"github.com/joho/godotenv"
)

func main() {
	// 获取环境配置的优先级：
	// 1. 命令行参数
	// 2. 环境变量
	// 3. .env 文件
	// 4. 默认值

	var env string
	flag.StringVar(&env, "env", "", "environment (local, test, live)")
	flag.Parse()

	// 如果没有通过命令行指定环境，则尝试从环境变量获取
	if env == "" {
		env = os.Getenv("APP_ENV")
	}

	// 如果环境变量也没有设置，则尝试加载 .env 文件
	if env == "" {
		// 获取项目根目录
		rootDir := findProjectRoot()

		// 加载 env 文件
		if err := godotenv.Load(filepath.Join(rootDir, "env")); err != nil {
			log.Printf("Warning: Error loading env file: %v", err)
		}

		env = os.Getenv("APP_ENV")
	}

	// 如果所有方式都没有设置环境，则使用默认值
	if env == "" {
		env = "local"
	}

	// 加载配置
	cfg, err := config.LoadConfig(env)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	postgres, err := database.NewPostgresDB(cfg.Postgres)
	if err != nil {
		log.Fatalf("Failed to connect to database： %v", err)
	}

	// 运行迁移
	err = postgres.AutoMigrate(
		&blogentity.Blog{},
		&clickrecordentity.ClickRecord{},
		&couponentity.Coupon{},
		&merchantentity.Merchant{},
		&merchantentity.Category{},
		&merchantentity.CashbackRule{},
		&orderentity.Order{},
		&userentity.User{},
		&withdrawalentity.Withdrawal{},
	)
	if err != nil {
		log.Fatalf("Failed to run migrations: %v", err)
	}

	log.Println("Migrations completed successfully")
}

// findProjectRoot 查找项目根目录
func findProjectRoot() string {
	// 从当前目录开始向上查找，直到找到包含 .env 文件的目录
	dir, err := os.Getwd()
	if err != nil {
		return ""
	}

	for {
		if _, err := os.Stat(filepath.Join(dir, "env")); err == nil {
			return dir
		}

		parent := filepath.Dir(dir)
		if parent == dir {
			return ""
		}
		dir = parent
	}
}
