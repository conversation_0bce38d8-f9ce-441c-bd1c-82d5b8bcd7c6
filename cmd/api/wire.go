//go:build wireinject
// +build wireinject

package main

import (
	blogapp "bonusearned/application/blog/appservice"
	clickapp "bonusearned/application/clickrecord/appservice"
	couponapp "bonusearned/application/coupon/appservice"
	merchantapp "bonusearned/application/merchant/appservice"
	orderapp "bonusearned/application/order/appservice"
	userapp "bonusearned/application/user/appservice"
	withdrawalapp "bonusearned/application/withdrawal/appservice"
	"bonusearned/config"
	blogservice "bonusearned/domain/blog/service"
	clickservice "bonusearned/domain/clickrecord/service"
	couponservice "bonusearned/domain/coupon/service"
	merchantservice "bonusearned/domain/merchant/service"
	orderservice "bonusearned/domain/order/service"
	userservice "bonusearned/domain/user/service"
	withdrawalservice "bonusearned/domain/withdrawal/service"
	"bonusearned/infra/database"
	"bonusearned/infra/logger"
	"bonusearned/infra/persistence"
	"bonusearned/interfaces/api/handler/blog"
	"bonusearned/interfaces/api/handler/clickrecord"
	"bonusearned/interfaces/api/handler/coupon"
	"bonusearned/interfaces/api/handler/merchant"
	"bonusearned/interfaces/api/handler/order"
	"bonusearned/interfaces/api/handler/seo"
	"bonusearned/interfaces/api/handler/user"
	"bonusearned/interfaces/api/handler/withdrawal"
	"bonusearned/interfaces/api/router"

	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ProviderSet 定义所有依赖注入的提供者
var ProviderSet = wire.NewSet(
	// Infrastructure
	ProvidePostgres,
	ProvideRedis,
	ProvideLogger,

	// Repositories
	persistence.NewBlogPostgresRepository,
	persistence.NewClickPostgresRepository,
	persistence.NewMerchantPostgresRepository,
	persistence.NewOrderPostgresRepository,
	persistence.NewCouponPostgresRepository,
	persistence.NewWithdrawalPostgresRepository,
	persistence.NewUserPostgresRepository,
	persistence.NewCategoryPostgresRepository,
	persistence.NewCountryPostgresRepository,
	persistence.NewCashbackRulePostgresRepository,

	// Domain Services
	blogservice.NewBlogService,
	clickservice.NewClickRecordService,
	merchantservice.NewMerchantService,
	orderservice.NewOrderService,
	couponservice.NewCouponService,
	withdrawalservice.NewWithdrawalService,
	userservice.NewUserService,
	merchantservice.NewCategoryService,
	merchantservice.NewCountryService,
	merchantservice.NewCashbackRuleService,

	// Application Services
	blogapp.NewBlogAppService,
	clickapp.NewClickRecordAppService,
	merchantapp.NewMerchantAppService,
	orderapp.NewOrderAppService,
	couponapp.NewCouponAppService,
	withdrawalapp.NewWithdrawalApp,
	merchantapp.NewCategoryAppService,
	merchantapp.NewCashbackRuleAppService,
	userapp.NewUserApp,

	// Handlers
	blog.NewBlogHandler,
	clickrecord.NewClickHandler,
	merchant.NewCategoryHandler,
	merchant.NewCountryHandler,
	coupon.NewCouponHandler,
	merchant.NewHandler,
	order.NewOrderHandler,
	withdrawal.NewWithdrawalHandler,
	user.NewUserHandler,
	seo.NewSEOHandler,

	// Router
	router.NewApiServer,
)

// Application 应用程序结构体
type Application struct {
	Router     *router.ApiServer
	Logger     *zap.Logger
	Config     *config.Config
	PostgresDB *gorm.DB
	RedisDB    *redis.Client
}

// ProvidePostgres 提供 PostgreSQL 连接
func ProvidePostgres(cfg *config.Config) (*gorm.DB, error) {
	return database.NewPostgresDB(cfg.Postgres)
}

// ProvideRedis 提供 Redis 连接
func ProvideRedis(cfg *config.Config) (*redis.Client, error) {
	return database.NewRedisClient(cfg.Redis)
}

// ProvideLogger 提供日志实例
func ProvideLogger(cfg *config.Config) (*zap.Logger, error) {
	return logger.NewLogger(cfg.Logger)
}

// InitializeApplication 初始化应用程序
func InitializeApplication(cfg *config.Config) (*Application, error) {
	wire.Build(
		ProviderSet,
		wire.Struct(new(Application), "*"),
	)
	return nil, nil
}
