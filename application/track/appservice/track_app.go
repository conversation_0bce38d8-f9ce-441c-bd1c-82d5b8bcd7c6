package appservice

import (
	"bonusearned/domain/track/service"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

type TrackAppService interface {
	GetTrackUrl(ctx *gin.Context, userCode, merchantCode, sub1 string) (string, *ecode.Error)
}

type TrackAppServiceImpl struct {
	trackService service.TrackService
}

func NewTrackAppService(trackService service.TrackService) TrackAppService {
	return &TrackAppServiceImpl{
		trackService: trackService,
	}
}

func (app *TrackAppServiceImpl) GetTrackUrl(ctx *gin.Context, userCode, merchantCode, sub1 string) (string, *ecode.Error) {
	if len(sub1) > 5 {
		return "", ecode.New(ecode.ErrInvalidParameter.Code, "sub1 too long (max 5 chars)")
	}
	if len(userCode) > 7 {
		return "", ecode.New(ecode.ErrInvalidParameter.Code, "user code too long (max 7 chars)")
	}
	if len(merchantCode) > 7 {
		return "", ecode.New(ecode.ErrInvalidParameter.Code, "merchant code too long (max 7 chars)")
	}
	trackUrl, err := app.trackService.GetTrackUrl(ctx, userCode, merchantCode, sub1)
	if err != nil {
		return trackUrl, err
	}
	return trackUrl, nil
}
