package appservice

import (
	"bonusearned/application/order/dto"
	merchantentity "bonusearned/domain/merchant/entity"
	merchantservice "bonusearned/domain/merchant/service"
	"bonusearned/domain/order/service"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type OrderAppService interface {
	// GetOrderList 获取订单列表
	GetOrderList(ctx *gin.Context, userID uint64, req *dto.GetOrderListReq, userCode string) (*dto.OrderListResp, *ecode.Error)
}

type OrderAppServiceImpl struct {
	orderService    service.OrderService
	merchantService merchantservice.MerchantService
	logger          *zap.Logger
}

func NewOrderAppService(
	orderService service.OrderService,
	merchantService merchantservice.MerchantService,
	logger *zap.Logger,
) OrderAppService {
	return &OrderAppServiceImpl{
		orderService:    orderService,
		merchantService: merchantService,
		logger:          logger,
	}
}

func (app *OrderAppServiceImpl) GetOrderList(ctx *gin.Context, userID uint64, req *dto.GetOrderListReq, userCode string) (*dto.OrderListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetOrderList()
	if userID <= 0 {
		return nil, ecode.ErrUserUnauthorized
	}
	condition["user_id"] = userID

	orderList, total, err := app.orderService.GetOrderListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	merchantIds := make([]uint64, 0)
	for i := range orderList {
		merchantIds = append(merchantIds, orderList[i].MerchantID)
	}
	merchantList, err := app.merchantService.GetMerchantListByIDs(ctx, merchantIds, userID, userCode)
	if err != nil {
		return nil, err
	}
	merchantMap := make(map[uint64]*merchantentity.Merchant, 0)
	for i := range merchantList {
		merchantMap[merchantList[i].ID] = merchantList[i]
	}

	return dto.Entity2DtoOrderListResp(req.PageSize, req.Page, total, orderList, merchantMap, nil), nil
}
