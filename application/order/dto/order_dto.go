package dto

import (
	merchantdto "bonusearned/application/merchant/dto"
	userdto "bonusearned/application/user/dto"
	merchantentity "bonusearned/domain/merchant/entity"
	"bonusearned/domain/order/entity"
	userentity "bonusearned/domain/user/entity"
	"github.com/shopspring/decimal"
	"time"
)

type GetOrderListReq struct {
	Page       int    `json:"page" form:"page"`
	PageSize   int    `json:"page_size" form:"page_size"`
	Offset     int    `json:"offset"`
	MerchantID uint64 `json:"merchant_id"  form:"merchant_id"`
	Status     int8   `json:"status" form:"status"`
}

// CreateOrderReq 创建订单请求
type CreateOrderReq struct {
	UserCode       string             `json:"user_code"`
	MerchantCode   string             `json:"merchant_code" binding:"required"`
	OrderID        string             `json:"order_id"`                                           // 订单id、可以重复
	ClickID        string             `json:"click_id" binding:"required"`                        // 点击id，可能重复
	OrderAmount    decimal.Decimal    `gorm:"type:decimal(10,2);not null" json:"order_amount"`    // 订单金额
	CashbackAmount decimal.Decimal    `gorm:"type:decimal(10,2);not null" json:"cashback_amount"` // 佣金金额
	Status         entity.OrderStatus `json:"status"`                                             // 订单状态，创建时，均为 pending
	PlatformStatus string             `gorm:"type:varchar(20);not null" json:"platform_status"`   // 平台订单状态 - 不可展示给用户，仅仅用于内部记录
	OrderTime      string             `json:"order_time" binding:"required"`
}

// UpdateOrderRequest 更新订单请求
type UpdateOrderRequest struct {
	ConversionId   string             `json:"conversion_id"` // 转化id，不可重复，不同id展示的原因是：不能让用户发现有多少订单（根据自增id可以判断出订单量，这算是信息泄漏了）
	UserID         uint64             `json:"user_id"`
	UserCode       string             `json:"user_code"` // 用户编码
	MerchantID     uint64             `json:"merchant_id"`
	MerchantCode   string             `json:"merchant_code"`   // 商家编码
	OrderID        string             `json:"order_id"`        // 订单id、可以重复
	ClickID        string             `json:"click_id"`        // 点击id，可能重复
	OrderAmount    decimal.Decimal    `json:"order_amount"`    // 订单金额
	CashbackAmount decimal.Decimal    `json:"cashback_amount"` // 佣金金额
	Status         entity.OrderStatus `json:"status"`          // 订单状态
	PlatformStatus string             `json:"platform_status"` // 平台订单状态 - 不可展示给用户，仅仅用于内部记录
	OrderTime      *time.Time         `json:"order_time"`      // 下单时间
	ApproveTime    *time.Time         `json:"approve_time"`    // 确认时间
	CancelTime     *time.Time         `json:"cancel_time"`     // 取消时间
	PaidTime       *time.Time         `json:"paid_time"`       // 已支付时间
	LastSyncTime   *time.Time         `json:"last_sync_time"`  // 最后同步时间
	CancelReason   string             `json:"cancel_reason"`   // 取消原因
}

// UpdateOrderStatusRequest 更新订单状态请求
type UpdateOrderStatusRequest struct {
	Status entity.OrderStatus `json:"status" binding:"required"`
}

type OrderDetailResp struct {
	ConversionId   string                          `json:"conversion_id"` // 转化id，不可重复，不同id展示的原因是：不能让用户发现有多少订单（根据自增id可以判断出订单量，这算是信息泄漏了）
	UserInfo       *userdto.UserDetailResponse     `json:"user_info"`
	UserCode       string                          `json:"user_code"` // 用户编码
	MerchantInfo   *merchantdto.MerchantDetailResp `json:"merchant_info"`
	MerchantCode   string                          `json:"merchant_code"`                  // 商家编码
	OrderID        string                          `json:"order_id"`                       // 订单id、可以重复
	ClickID        string                          `json:"click_id"`                       // 点击id，可能重复
	OrderAmount    decimal.Decimal                 `json:"order_amount"`                   // 订单金额
	CashbackAmount decimal.Decimal                 `json:"cashback_amount"`                // 佣金金额
	Status         string                          `json:"status"`                         // 订单状态
	OrderTime      *time.Time                      `json:"order_time"`                     // 下单时间
	ApproveTime    *time.Time                      `json:"approve_time"`                   // 确认时间
	CancelTime     *time.Time                      `json:"cancel_time"`                    // 取消时间
	PaidTime       *time.Time                      `json:"paid_time"`                      // 已支付时间
	CancelReason   string                          `gorm:"type:text" json:"cancel_reason"` // 取消原因
}

type OrderListResp struct {
	Total     int64              `json:"total"`
	Page      int                `json:"page"`
	PageSize  int                `json:"page_size"`
	OrderList []*OrderDetailResp `json:"order_list"`
}

func (req *GetOrderListReq) Dto2ConditionGetOrderList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Status > 0 {
		condition["status"] = req.Status
	}
	if req.MerchantID > 0 {
		condition["merchant_id"] = req.MerchantID
	}
	return condition
}

func Entity2DtoOrderDetailResp(order *entity.Order, merchant *merchantentity.Merchant, user *userentity.User) (resp *OrderDetailResp) {
	resp = &OrderDetailResp{}
	resp.ConversionId = order.ConversionId
	resp.UserCode = order.UserCode
	resp.MerchantCode = order.MerchantCode
	resp.OrderID = order.OrderID
	resp.ClickID = order.ClickID
	resp.OrderAmount = order.OrderAmount
	resp.CashbackAmount = order.CashbackAmount
	resp.Status = order.Status.String()
	resp.OrderTime = &order.OrderTime
	resp.ApproveTime = &order.ApproveTime
	resp.CancelTime = &order.CancelTime
	resp.PaidTime = &order.PaidTime
	resp.CancelReason = order.CancelReason
	if merchant != nil {
		resp.MerchantInfo = merchantdto.Entity2DtoMerchantDetailResp(merchant, nil)
	} else {
		resp.MerchantInfo = &merchantdto.MerchantDetailResp{}
	}

	//if user != nil {
	//	resp.UserInfo = merchantdto.Entity2DtoMerchantDetailResp(merchant, nil)
	//} else {
	//	resp.MerchantInfo = &merchantdto.MerchantDetailResp{}
	//}
	return resp
}

func Entity2DtoOrderListResp(pageSize int, page int, total int64, orderList []*entity.Order, merchantMap map[uint64]*merchantentity.Merchant, user *userentity.User) (resp *OrderListResp) {
	// 获取列表时可以接受没有商家信息
	resp = &OrderListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range orderList {
		if merchant, ok := merchantMap[orderList[i].MerchantID]; ok {
			resp.OrderList = append(resp.OrderList, Entity2DtoOrderDetailResp(orderList[i], merchant, user))
		} else {
			resp.OrderList = append(resp.OrderList, Entity2DtoOrderDetailResp(orderList[i], nil, user))
		}
	}
	return resp
}
