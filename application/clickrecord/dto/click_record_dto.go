package dto

import (
	"bonusearned/application/merchant/dto"
	"bonusearned/domain/clickrecord/entity"
	merchantentity "bonusearned/domain/merchant/entity"
	"time"
)

type GetClickRecordListReq struct {
	Page     int `form:"page,default=1" binding:"min=1"`
	PageSize int `form:"page_size,default=30" binding:"min=20,max=100"`
}

// CreateClickRecordReq 创建点击记录请求
type CreateClickRecordReq struct {
	MerchantCode string `json:"merchant_code" binding:"required"`
	TrackUID     string `json:"track_uid"`
	Sub1         string `json:"sub1,omitempty"`
	ClickID      string `json:"click_id"`
	UserCode     string `gorm:"type:varchar(50)" json:"user_code"` // 实际用户编码
}

// ClickRecordDetailResp 点击记录响应
type ClickRecordDetailResp struct {
	ID           uint64                  `json:"id"`
	ClickDate    time.Time               `json:"click_date"`
	ClickID      string                  `json:"click_id"`
	Sub1         string                  `json:"sub1,omitempty"`
	CreatedAt    time.Time               `json:"created_at"`
	MerchantInfo *dto.MerchantDetailResp `json:"merchant_info"`
}

// ClickRecordListResp 点击记录列表响应
type ClickRecordListResp struct {
	Total           int64                    `json:"total"`
	Page            int                      `json:"page"`
	PageSize        int                      `json:"page_size"`
	ClickRecordList []*ClickRecordDetailResp `json:"click_record_list"`
}

func (req *GetClickRecordListReq) Dto2ConditionGetClickRecordList(userId uint64) (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if userId > 0 {
		condition["user_id"] = userId
	}
	return condition
}

func (req *CreateClickRecordReq) Dto2EntityCreateClickRecordReq() (clickRecord *entity.ClickRecord) {
	clickRecord = &entity.ClickRecord{}
	clickRecord.ClickDate = time.Now()
	clickRecord.ClickID = req.ClickID
	clickRecord.TrackUID = req.TrackUID
	clickRecord.UserCode = req.UserCode
	clickRecord.MerchantCode = req.MerchantCode
	clickRecord.UserID = 0
	clickRecord.MerchantID = 0
	clickRecord.Sub1 = req.Sub1
	clickRecord.CreatedAt = time.Now()
	return clickRecord
}

func Entity2DtoClickRecordDetailResp(clickRecord *entity.ClickRecord, merchant *merchantentity.Merchant) (resp *ClickRecordDetailResp) {
	resp = &ClickRecordDetailResp{}
	resp.ID = clickRecord.ID
	resp.ClickDate = clickRecord.ClickDate
	resp.ClickID = clickRecord.ClickID
	resp.Sub1 = clickRecord.Sub1
	resp.CreatedAt = clickRecord.CreatedAt
	if merchant != nil {
		resp.MerchantInfo = dto.Entity2DtoMerchantDetailResp(merchant, nil)
	} else {
		resp.MerchantInfo = &dto.MerchantDetailResp{}
	}
	return resp
}

func Entity2DtoClickRecordListResp(pageSize int, page int, total int64, clickRecordList []*entity.ClickRecord, merchantMap map[uint64]*merchantentity.Merchant) (resp *ClickRecordListResp) {
	// 获取列表时可以接受没有商家信息
	resp = &ClickRecordListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range clickRecordList {
		if merchant, ok := merchantMap[clickRecordList[i].MerchantID]; ok {
			resp.ClickRecordList = append(resp.ClickRecordList, Entity2DtoClickRecordDetailResp(clickRecordList[i], merchant))
		} else {
			resp.ClickRecordList = append(resp.ClickRecordList, Entity2DtoClickRecordDetailResp(clickRecordList[i], nil))
		}
	}
	return resp
}
