package appservice

import (
	"bonusearned/application/clickrecord/dto"
	"bonusearned/domain/clickrecord/service"
	merchantentity "bonusearned/domain/merchant/entity"
	merchantservice "bonusearned/domain/merchant/service"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// ClickRecordAppService 点击记录应用服务接口
type ClickRecordAppService interface {
	// CreateClickRecord 创建点击记录
	CreateClickRecord(ctx *gin.Context, req *dto.CreateClickRecordReq) *ecode.Error
	// GetClickRecordDetailById 根据ID获取点击记录
	GetClickRecordDetailById(ctx *gin.Context, id uint64, userID uint64, userCode string) (*dto.ClickRecordDetailResp, *ecode.Error)
	// GetClickRecordList 获取用户的点击记录
	GetClickRecordList(ctx *gin.Context, userId uint64, req *dto.GetClickRecordListReq, userCode string) (*dto.ClickRecordListResp, *ecode.Error)
	// DeleteClickRecord 删除点击记录
	DeleteClickRecord(ctx *gin.Context, id uint64) *ecode.Error
}

type ClickRecordAppServiceImpl struct {
	clickRecordService service.ClickRecordService
	merchantService    merchantservice.MerchantService
}

// NewClickRecordAppService 创建点击记录应用服务
func NewClickRecordAppService(clickRecordService service.ClickRecordService, merchantService merchantservice.MerchantService) ClickRecordAppService {
	return &ClickRecordAppServiceImpl{
		clickRecordService: clickRecordService,
		merchantService:    merchantService,
	}
}

func (app *ClickRecordAppServiceImpl) CreateClickRecord(ctx *gin.Context, req *dto.CreateClickRecordReq) *ecode.Error {
	clickRecord := req.Dto2EntityCreateClickRecordReq()
	// 创建点击记录
	err := app.clickRecordService.CreateClickRecord(ctx, clickRecord)
	if err != nil {
		return err
	}
	return nil
}

func (app *ClickRecordAppServiceImpl) GetClickRecordDetailById(ctx *gin.Context, id uint64, userID uint64, userCode string) (*dto.ClickRecordDetailResp, *ecode.Error) {
	clickRecord, err := app.clickRecordService.GetClickRecordDetailById(ctx, id)
	if err != nil {
		return nil, err
	}
	merchant, err := app.merchantService.GetMerchantDetailById(ctx, id, userID, userCode)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoClickRecordDetailResp(clickRecord, merchant), nil
}

func (app *ClickRecordAppServiceImpl) GetClickRecordList(ctx *gin.Context, userId uint64, req *dto.GetClickRecordListReq, userCode string) (*dto.ClickRecordListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetClickRecordList(userId)
	clickRecordList, total, err := app.clickRecordService.GetClickRecordListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	merchantIds := make([]uint64, 0)
	for i := range clickRecordList {
		merchantIds = append(merchantIds, clickRecordList[i].MerchantID)
	}
	merchantList, err := app.merchantService.GetMerchantListByIDs(ctx, merchantIds, userId, userCode)
	if err != nil {
		return nil, err
	}
	merchantMap := make(map[uint64]*merchantentity.Merchant, 0)
	for i := range merchantList {
		merchantMap[merchantList[i].ID] = merchantList[i]
	}
	return dto.Entity2DtoClickRecordListResp(req.PageSize, req.Page, total, clickRecordList, merchantMap), nil
}

func (app *ClickRecordAppServiceImpl) DeleteClickRecord(ctx *gin.Context, id uint64) *ecode.Error {
	err := app.clickRecordService.DeleteClickRecord(ctx, id)
	if err != nil {
		return err
	}
	return nil
}
