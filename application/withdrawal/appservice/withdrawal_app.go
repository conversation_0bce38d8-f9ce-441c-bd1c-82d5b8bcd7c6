package appservice

import (
	"bonusearned/application/withdrawal/dto"
	userservice "bonusearned/domain/user/service"
	"bonusearned/domain/withdrawal/service"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// WithdrawalAppService 提现应用服务接口
type WithdrawalAppService interface {
	// GetWithdrawalList 获取用户的提现列表
	GetWithdrawalList(ctx *gin.Context, userID uint64, req *dto.GetWithdrawalListReq) (*dto.WithdrawalListResponse, *ecode.Error)
}

type withdrawalAppServiceImpl struct {
	withdrawalService service.WithdrawalService
	userService       userservice.UserService
}

// NewWithdrawalApp 创建提现应用服务
func NewWithdrawalApp(withdrawalService service.WithdrawalService, userService userservice.UserService) WithdrawalAppService {
	return &withdrawalAppServiceImpl{
		withdrawalService: withdrawalService,
		userService:       userService,
	}
}

func (app *withdrawalAppServiceImpl) GetWithdrawalList(ctx *gin.Context, userID uint64, req *dto.GetWithdrawalListReq) (*dto.WithdrawalListResponse, *ecode.Error) {
	condition := req.Dto2ConditionGetWithdrawalList()
	condition["user_id"] = userID
	user, err := app.userService.GetUserDetailById(ctx, userID)
	if err != nil {
		return nil, err
	}
	withdrawalList, total, err := app.withdrawalService.GetWithdrawalListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}

	resp := dto.Entity2DtoWithdrawalListResp(req.PageSize, req.Page, total, withdrawalList, user)

	return resp, nil
}
