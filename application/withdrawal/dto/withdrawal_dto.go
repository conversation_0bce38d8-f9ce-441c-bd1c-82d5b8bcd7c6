package dto

import (
	userdto "bonusearned/application/user/dto"
	userentity "bonusearned/domain/user/entity"
	"bonusearned/domain/withdrawal/entity"
	"time"
)

type GetWithdrawalListReq struct {
	Page     int  `json:"page" form:"page"`
	PageSize int  `json:"page_size" form:"page_size"`
	Offset   int  `json:"offset"`
	Status   int8 `json:"status"`
}

// WithdrawalDetailResponse 提现响应
type WithdrawalDetailResponse struct {
	WithdrawalNo  string                      `json:"withdrawal_no"`
	UserInfo      *userdto.UserDetailResponse `json:"user_info"`
	Amount        float64                     `json:"amount"`
	PaymentInfo   map[string]interface{}      `json:"payment_info"`
	Status        string                      `json:"status"`
	ProcessTime   *time.Time                  `json:"process_time,omitempty"`
	CompleteTime  *time.Time                  `json:"complete_time,omitempty"`
	FailReason    string                      `json:"fail_reason,omitempty"`
	ProcessorID   uint64                      `json:"processor_id,omitempty"`
	TransactionID string                      `json:"transaction_id,omitempty"`
	CreatedAt     time.Time                   `json:"created_at"`
}

// WithdrawalListResponse 提现列表响应
type WithdrawalListResponse struct {
	Total          int64                       `json:"total"`
	Page           int                         `json:"page"`
	PageSize       int                         `json:"page_size"`
	WithdrawalList []*WithdrawalDetailResponse `json:"withdrawal_list"`
}

func (req *GetWithdrawalListReq) Dto2ConditionGetWithdrawalList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Status > 0 {
		condition["status"] = req.Status
	}
	return condition
}

func Entity2DtoWithdrawalDetailResp(withdrawal *entity.Withdrawal, user *userentity.User) (resp *WithdrawalDetailResponse) {
	resp = &WithdrawalDetailResponse{}
	if user != nil {
		resp.UserInfo = userdto.Entity2DtoUserDetailResp(user)
	} else {
		return resp
	}
	paymentInfo := make(map[string]interface{})
	if user.PaymentInfo != nil && !user.PaymentInfo.IsNull() {
		paymentInfoMap, _ := user.PaymentInfo.ToMap()
		for k, v := range paymentInfoMap {
			if defaultMethod, ok := v.(map[string]interface{})["default_method"]; ok && defaultMethod.(bool) {
				paymentInfo[k] = v
			}
		}
	}
	resp.PaymentInfo = paymentInfo
	resp.WithdrawalNo = withdrawal.WithdrawalNo
	resp.Amount = withdrawal.Amount
	resp.Status = withdrawal.Status.String()
	resp.ProcessTime = withdrawal.ProcessTime
	resp.CompleteTime = withdrawal.CompleteTime
	resp.FailReason = withdrawal.FailReason
	resp.TransactionID = withdrawal.TransactionID
	resp.CreatedAt = withdrawal.CreatedAt
	return resp
}

func Entity2DtoWithdrawalListResp(pageSize int, page int, total int64, withdrawalList []*entity.Withdrawal, user *userentity.User) (resp *WithdrawalListResponse) {
	// 获取列表时可以接受没有商家信息
	resp = &WithdrawalListResponse{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range withdrawalList {
		resp.WithdrawalList = append(resp.WithdrawalList, Entity2DtoWithdrawalDetailResp(withdrawalList[i], user))
	}
	return resp
}
