package appservice

import (
	"bonusearned/domain/task/service"

	"github.com/gin-gonic/gin"
)

// SyncResult 同步结果
type SyncResult struct {
	Success     bool   `json:"success"`
	Message     string `json:"message"`
	SyncedCount int    `json:"synced_count"`
	FailedCount int    `json:"failed_count"`
}

// SyncApp 同步应用服务接口
type SyncApp interface {
	// SyncMerchantList 同步商家信息
	SyncMerchantList(ctx *gin.Context)
	SyncCouponList(ctx *gin.Context)
	SyncOrderList(ctx *gin.Context)
	// SyncMerchantLogoList 同步商家Logo
	SyncMerchantLogoList(ctx *gin.Context)
}

type syncApp struct {
	syncService service.SyncService
}

// NewSyncApp 创建同步应用服务
func NewSyncApp(syncService service.SyncService) SyncApp {
	return &syncApp{
		syncService: syncService,
	}
}

func (a *syncApp) SyncMerchantList(ctx *gin.Context) {
	a.syncService.SyncMerchantList(ctx)
	return
}

func (a *syncApp) SyncCouponList(ctx *gin.Context) {
	a.syncService.SyncCouponList(ctx)
	return
}

func (a *syncApp) SyncOrderList(ctx *gin.Context) {
	a.syncService.SyncOrderList(ctx)
	return
}

func (a *syncApp) SyncMerchantLogoList(ctx *gin.Context) {
	a.syncService.SyncMerchantLogoList(ctx)
	return
}
