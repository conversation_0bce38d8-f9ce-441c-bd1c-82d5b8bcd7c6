package dto

import (
	"bonusearned/application/merchant/dto"
	"bonusearned/domain/coupon/entity"
	merchantentity "bonusearned/domain/merchant/entity"
	"strconv"
	"time"
)

type GetCouponListReq struct {
	Page       int    `json:"page" form:"page"`
	PageSize   int    `json:"page_size" form:"page_size"`
	Featured   string `json:"featured" form:"featured"`
	Search     string `json:"search" form:"search"`
	Offset     int    `json:"offset"`
	MerchantID uint64 `json:"merchant_id" form:"merchant_id"`
}

// CreateCouponReq 创建优惠券请求
type CreateCouponReq struct {
	MerchantID   uint64     `json:"merchant_id" binding:"required"`
	Code         string     `json:"code" binding:"required"`
	DiscountRate string     `json:"discount_rate"`
	Title        string     `json:"title"`
	Description  string     `json:"description"`
	Type         string     `json:"type" binding:"required"`
	StartedAt    *time.Time `json:"started_at"`
	EndedAt      *time.Time `json:"ended_at"`
}

type BatchCreateCouponReq struct {
	CouponList []*CreateCouponReq `json:"coupon_list"`
}

// UpdateCouponReq 更新优惠券请求
type UpdateCouponReq struct {
	ID           uint64     `json:"id" binding:"required"`
	MerchantID   uint64     `json:"merchant_id" binding:"required"`
	Code         string     `json:"code" binding:"required"`
	DiscountRate string     `json:"discount_rate"`
	Title        string     `json:"title"`
	Description  string     `json:"description"`
	Type         string     `json:"type" binding:"required"`
	StartedAt    time.Time  `json:"started_at"`
	EndedAt      *time.Time `json:"ended_at"`
}

// CouponDetailResp 优惠券响应
type CouponDetailResp struct {
	ID           uint64                  `json:"id"`
	MerchantInfo *dto.MerchantDetailResp `json:"merchant_info"`
	Code         string                  `json:"code"`
	DiscountRate string                  `json:"discount_rate"`
	Title        string                  `json:"title"`
	Description  string                  `json:"description"`
	Type         string                  `json:"type"`
	StartedAt    *time.Time              `json:"started_at"`
	EndedAt      *time.Time              `json:"ended_at,omitempty"`
}

// CouponListResp 优惠券列表项响应
type CouponListResp struct {
	Total      int64               `json:"total"`
	Page       int                 `json:"page"`
	PageSize   int                 `json:"page_size"`
	CouponList []*CouponDetailResp `json:"coupon_list"`
}

func (req *GetCouponListReq) Dto2ConditionGetCouponList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Featured != "" {
		condition["featured"], _ = strconv.ParseBool(req.Featured)
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.MerchantID > 0 {
		condition["merchant_id"] = req.MerchantID
	}
	return condition
}

func (req *CreateCouponReq) Dto2EntityCreateCouponReq() (coupon *entity.Coupon) {
	coupon = &entity.Coupon{}
	coupon.MerchantID = req.MerchantID
	coupon.Code = req.Code
	coupon.DiscountRate = req.DiscountRate
	coupon.Title = req.Title
	coupon.Description = req.Description
	coupon.CouponType = entity.CouponTypeWithCoupon
	coupon.StartedAt = req.StartedAt
	coupon.EndedAt = req.EndedAt
	coupon.CreatedAt = time.Now()
	coupon.UpdatedAt = time.Now()
	return coupon
}

func (req *BatchCreateCouponReq) Dto2EntityBatchCreateCouponReq() (couponList []*entity.Coupon) {
	couponList = make([]*entity.Coupon, 0)
	for i := range req.CouponList {
		coupon := req.CouponList[i].Dto2EntityCreateCouponReq()
		couponList = append(couponList, coupon)
	}
	return couponList
}

func Entity2DtoCouponDetailResp(coupon *entity.Coupon, merchant *merchantentity.Merchant) (resp *CouponDetailResp) {
	resp = &CouponDetailResp{}
	resp.ID = coupon.ID
	resp.Code = coupon.Code
	resp.DiscountRate = coupon.DiscountRate
	resp.Title = coupon.Title
	resp.Description = coupon.Description
	resp.Type = coupon.CouponType
	resp.StartedAt = coupon.StartedAt
	resp.EndedAt = coupon.EndedAt
	if merchant != nil {
		resp.MerchantInfo = dto.Entity2DtoMerchantDetailResp(merchant, nil)
	} else {
		resp.MerchantInfo = &dto.MerchantDetailResp{}
	}
	return resp
}

func Entity2DtoCouponListResp(pageSize int, page int, total int64, couponList []*entity.Coupon, merchantMap map[uint64]*merchantentity.Merchant) (resp *CouponListResp) {
	// 获取列表时可以接受没有商家信息
	resp = &CouponListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range couponList {
		if merchant, ok := merchantMap[couponList[i].MerchantID]; ok {
			resp.CouponList = append(resp.CouponList, Entity2DtoCouponDetailResp(couponList[i], merchant))
		} else {
			resp.CouponList = append(resp.CouponList, Entity2DtoCouponDetailResp(couponList[i], nil))
		}
	}
	return resp
}
