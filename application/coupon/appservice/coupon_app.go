package appservice

import (
	"bonusearned/application/coupon/dto"
	"bonusearned/domain/coupon/service"
	merchantentity "bonusearned/domain/merchant/entity"
	merchantservice "bonusearned/domain/merchant/service"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// CouponAppService 优惠券应用服务接口
type CouponAppService interface {
	// CreateCoupon 创建优惠券
	CreateCoupon(ctx *gin.Context, req *dto.CreateCouponReq) *ecode.Error
	// UpdateCoupon 更新优惠券
	UpdateCoupon(ctx *gin.Context, req *dto.UpdateCouponReq) *ecode.Error
	// DeleteCoupon 删除优惠券
	DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error
	// GetCouponDetailById 根据ID查找优惠券
	GetCouponDetailById(ctx *gin.Context, id uint64, userId uint64, userCode string) (*dto.CouponDetailResp, *ecode.Error)
	// GetCouponDetailByCode 根据优惠券码查找优惠券
	GetCouponDetailByCode(ctx *gin.Context, code string, userId uint64, userCode string) (*dto.CouponDetailResp, *ecode.Error)
	// GetCouponList 查找优惠券列表
	GetCouponList(ctx *gin.Context, req *dto.GetCouponListReq, userId uint64, userCode string) (*dto.CouponListResp, *ecode.Error)
	// BatchCreateCoupon 批量创建优惠券
	BatchCreateCoupon(ctx *gin.Context, req *dto.BatchCreateCouponReq) *ecode.Error
	// BatchDeleteCoupon 批量删除优惠券
	BatchDeleteCoupon(ctx *gin.Context, ids []uint64) *ecode.Error
}

type CouponAppServiceImpl struct {
	couponService   service.CouponService
	merchantService merchantservice.MerchantService
}

// NewCouponAppService 创建优惠券应用服务
func NewCouponAppService(couponService service.CouponService, merchantService merchantservice.MerchantService) CouponAppService {
	return &CouponAppServiceImpl{
		couponService:   couponService,
		merchantService: merchantService,
	}
}

func (app *CouponAppServiceImpl) CreateCoupon(ctx *gin.Context, req *dto.CreateCouponReq) *ecode.Error {
	coupon := req.Dto2EntityCreateCouponReq()
	if err := app.couponService.CreateCoupon(ctx, coupon); err != nil {
		return err
	}
	return nil
}

func (app *CouponAppServiceImpl) UpdateCoupon(ctx *gin.Context, req *dto.UpdateCouponReq) *ecode.Error {
	return nil
}

func (app *CouponAppServiceImpl) DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error {
	return app.couponService.DeleteCoupon(ctx, id)
}

func (app *CouponAppServiceImpl) GetCouponDetailById(ctx *gin.Context, id uint64, userId uint64, userCode string) (*dto.CouponDetailResp, *ecode.Error) {
	coupon, err := app.couponService.GetCouponDetailById(ctx, id)
	if err != nil {
		return nil, err
	}
	merchant, err := app.merchantService.GetMerchantDetailById(ctx, coupon.MerchantID, userId, userCode)
	if err != nil {
		return nil, err
	}

	return dto.Entity2DtoCouponDetailResp(coupon, merchant), nil
}

func (app *CouponAppServiceImpl) GetCouponDetailByCode(ctx *gin.Context, code string, userId uint64, userCode string) (*dto.CouponDetailResp, *ecode.Error) {
	coupon, err := app.couponService.GetCouponDetailByCode(ctx, code)
	if err != nil {
		return nil, err
	}
	merchant, err := app.merchantService.GetMerchantDetailById(ctx, coupon.MerchantID, userId, userCode)
	if err != nil {
		return nil, err
	}

	return dto.Entity2DtoCouponDetailResp(coupon, merchant), nil
}

func (app *CouponAppServiceImpl) GetCouponList(ctx *gin.Context, req *dto.GetCouponListReq, userId uint64, userCode string) (*dto.CouponListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetCouponList()
	couponList, total, err := app.couponService.GetCouponListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	merchantIds := make([]uint64, 0)
	for i := range couponList {
		merchantIds = append(merchantIds, couponList[i].MerchantID)
	}
	merchantList, err := app.merchantService.GetMerchantListByIDs(ctx, merchantIds, userId, userCode)
	if err != nil {
		return nil, err
	}
	merchantMap := make(map[uint64]*merchantentity.Merchant, 0)
	for i := range merchantList {
		merchantMap[merchantList[i].ID] = merchantList[i]
	}
	return dto.Entity2DtoCouponListResp(req.PageSize, req.Page, total, couponList, merchantMap), nil
}

func (app *CouponAppServiceImpl) BatchCreateCoupon(ctx *gin.Context, req *dto.BatchCreateCouponReq) *ecode.Error {
	couponList := req.Dto2EntityBatchCreateCouponReq()
	if err := app.couponService.BatchCreateCoupon(ctx, couponList); err != nil {
		return err
	}
	return nil
}

func (app *CouponAppServiceImpl) BatchDeleteCoupon(ctx *gin.Context, ids []uint64) *ecode.Error {
	return app.couponService.BatchDeleteCoupon(ctx, ids)
}
