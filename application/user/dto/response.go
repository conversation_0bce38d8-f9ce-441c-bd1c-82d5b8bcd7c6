package dto

import (
	"bonusearned/domain/user/entity"
)

// UserDetailResponse 用户信息
type UserDetailResponse struct {
	Email       string                 `json:"email"`
	UserCode    string                 `json:"user_code"`
	Phone       string                 `json:"phone,omitempty"`
	Nickname    string                 `json:"nickname"`
	PaymentInfo map[string]interface{} `json:"payment_info,omitempty"`
	UserBalance map[string]interface{} `json:"user_balance,omitempty"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	UserInfo *UserDetailResponse `json:"user_info"`
	Token    string              `json:"token"`
}

func Entity2DtoUserDetailResp(user *entity.User) (resp *UserDetailResponse) {
	if user == nil {
		return nil
	}
	paymentInfo := make(map[string]interface{})
	if user.PaymentInfo != nil {
		paymentInfo, _ = user.PaymentInfo.ToMap()
	}
	balanceInfo := make(map[string]interface{})
	if user.UserBalance != nil {
		balanceInfo, _ = user.UserBalance.ToMap()
	}

	resp = &UserDetailResponse{}
	resp.Email = user.Email
	resp.UserCode = user.UserCode
	resp.Phone = user.Phone
	resp.Nickname = user.Nickname
	resp.PaymentInfo = paymentInfo
	resp.UserBalance = balanceInfo
	return resp
}
