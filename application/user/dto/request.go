package dto

import (
	"bonusearned/domain/user/entity"
	"bonusearned/infra/constant"
	"bonusearned/infra/database/postgres"
	"bonusearned/infra/ecode"
	"bonusearned/infra/utils/uniqueutil"
	"time"
)

// RegisterRequest 用户注册请求
type RegisterRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6,max=50"`
	Nickname string `json:"nickname" binding:"required,min=2,max=50"`
	Phone    string `json:"phone"`
}

// LoginRequest 用户登录请求
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// UpdateProfileRequest 更新用户资料请求
type UpdateProfileRequest struct {
	Nickname string `json:"nickname" binding:"required,min=2,max=50"`
	Phone    string `json:"phone"`
}

// UpdatePasswordRequest 更新密码请求
type UpdatePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6,max=50"`
}

// UpdatePaymentInfoRequest 更新支付信息请求
type UpdatePaymentInfoRequest struct {
	PaymentInfo map[string]interface{} `json:"payment_info" binding:"required"`
}

func (req *RegisterRequest) Dto2EntityRegisterReq() (user *entity.User, error2 *ecode.Error) {
	// 创建用户实体
	user = &entity.User{}
	// 参数验证
	if len(req.Email) <= 0 {
		return nil, ecode.ErrInvalidParameter
	}
	if len(req.Password) <= 0 {
		return nil, ecode.ErrInvalidParameter
	}
	if len(req.Nickname) <= 0 {
		return nil, ecode.ErrInvalidParameter
	}
	user.Email = req.Email
	user.UserCode = uniqueutil.GenerateCode(user.Email)
	user.Phone = req.Phone
	user.Password = req.Password
	user.Nickname = req.Nickname

	// 使用空的 JSON 对象
	user.PaymentInfo = postgres.FromString("{}")
	user.UserBalance = postgres.FromString("{}")

	user.Status = constant.StatusActive
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()
	return user, nil
}
