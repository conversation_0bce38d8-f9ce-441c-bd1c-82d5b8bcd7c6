package appservice

import (
	"bonusearned/application/user/dto"
	"bonusearned/domain/user/service"
	"bonusearned/infra/ecode"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserApp interface {
	Register(ctx *gin.Context, req *dto.RegisterRequest) (*dto.UserDetailResponse, *ecode.Error)
	Login(ctx *gin.Context, req *dto.LoginRequest) (*dto.LoginResponse, *ecode.Error)
	GetProfile(ctx *gin.Context, id uint64) (*dto.UserDetailResponse, *ecode.Error)
	UpdateProfile(ctx *gin.Context, id uint64, req *dto.UpdateProfileRequest) *ecode.Error
	UpdatePaymentInfo(ctx *gin.Context, id uint64, req *dto.UpdatePaymentInfoRequest) *ecode.Error
	UpdatePassword(ctx *gin.Context, id uint64, req *dto.UpdatePasswordRequest) *ecode.Error
	Logout(ctx *gin.Context, id uint64) *ecode.Error
}

type userApp struct {
	userService service.UserService
	logger      *zap.Logger
}

func NewUserApp(userService service.UserService, logger *zap.Logger) UserApp {
	return &userApp{
		userService: userService,
		logger:      logger,
	}
}

func (a *userApp) Register(ctx *gin.Context, req *dto.RegisterRequest) (*dto.UserDetailResponse, *ecode.Error) {
	// 创建用户实体
	user, err := req.Dto2EntityRegisterReq()
	if err != nil {
		return nil, err
	}

	// 调用领域服务注册用户
	if err := a.userService.Register(ctx, user); err != nil {
		return nil, err
	}

	// 转换为DTO返回
	return dto.Entity2DtoUserDetailResp(user), nil
}

func (a *userApp) Login(ctx *gin.Context, req *dto.LoginRequest) (*dto.LoginResponse, *ecode.Error) {
	// 参数验证
	if len(req.Email) <= 0 {
		return nil, ecode.ErrInvalidParameter
	}
	if len(req.Password) <= 0 {
		return nil, ecode.ErrInvalidParameter
	}
	// 调用领域服务进行登录
	user, token, err := a.userService.Login(ctx, req.Email, req.Password)
	if err != nil {
		return nil, err
	}

	// 构造登录响应
	return &dto.LoginResponse{
		UserInfo: dto.Entity2DtoUserDetailResp(user),
		Token:    token,
	}, nil
}

func (a *userApp) GetProfile(ctx *gin.Context, id uint64) (*dto.UserDetailResponse, *ecode.Error) {
	// 获取用户信息
	user, err := a.userService.GetUserDetailById(ctx, id)
	if err != nil {
		return nil, err
	}

	return dto.Entity2DtoUserDetailResp(user), nil
}

func (a *userApp) UpdateProfile(ctx *gin.Context, id uint64, req *dto.UpdateProfileRequest) *ecode.Error {
	return a.userService.UpdateProfile(ctx, id, req.Phone, req.Nickname)
}

func (a *userApp) UpdatePaymentInfo(ctx *gin.Context, id uint64, req *dto.UpdatePaymentInfoRequest) *ecode.Error {
	// 将支付信息转换为JSON字符串
	_, err := json.Marshal(req.PaymentInfo)
	if err != nil {
		return ecode.ErrInvalidParameter
	}

	return a.userService.UpdatePaymentInfo(ctx, id, req.PaymentInfo)
}

func (a *userApp) UpdatePassword(ctx *gin.Context, id uint64, req *dto.UpdatePasswordRequest) *ecode.Error {
	return a.userService.UpdateUserPassword(ctx, id, req.OldPassword, req.NewPassword)
}

func (a *userApp) Logout(ctx *gin.Context, id uint64) *ecode.Error {
	return a.userService.Logout(ctx, id)
}
