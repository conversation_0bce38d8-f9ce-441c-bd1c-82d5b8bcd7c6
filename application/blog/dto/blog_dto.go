package dto

import (
	"bonusearned/application/merchant/dto"
	"bonusearned/domain/blog/entity"
	merchantentity "bonusearned/domain/merchant/entity"
	"strings"
	"time"
)

// GetBlogListReq 获取博客列表请求
type GetBlogListReq struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=30" binding:"min=1,max=100"`
	Category string `form:"category"`
	Search   string `form:"search"`
}

// CreateBlogReq 创建博客请求
type CreateBlogReq struct {
	Title         string    `json:"title" binding:"required"`
	Description   string    `json:"description"`
	Content       string    `json:"content" binding:"required"`
	FeaturedImage string    `json:"featured_image"`
	Category      string    `json:"category"`
	PublishedAt   time.Time `json:"published_at"`
	Tags          []string  `json:"tags"`
	MerchantID    uint64    `json:"merchant_id"`
}

// UpdateBlogReq 更新博客请求
type UpdateBlogReq struct {
	ID            uint64    `json:"id"  binding:"required"`
	Title         string    `json:"title"`
	Description   string    `json:"description"`
	Content       string    `json:"content"`
	FeaturedImage string    `json:"featured_image"`
	Category      string    `json:"category"`
	PublishedAt   time.Time `json:"published_at"`
	Tags          []string  `json:"tags"`
	MerchantID    uint64    `json:"merchant_id"`
}

// BlogDetailResp 博客信息
type BlogDetailResp struct {
	ID            uint64                  `json:"id"`
	Title         string                  `json:"title"`
	Description   string                  `json:"description"`
	Content       string                  `json:"content"`
	FeaturedImage string                  `json:"featured_image"`
	PublishedAt   time.Time               `json:"published_at"`
	Category      string                  `json:"category"`
	Tags          []string                `json:"tags"`
	MerchantInfo  *dto.MerchantDetailResp `json:"merchant_info,omitempty"`
}

// BlogListResp 获取博客列表响应
type BlogListResp struct {
	Total    int64             `json:"total"`
	Page     int               `json:"page"`
	PageSize int               `json:"page_size"`
	BlogList []*BlogDetailResp `json:"blog_list"`
}

func (req *GetBlogListReq) Dto2ConditionGetBlogList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Category != "" {
		condition["category"] = req.Category
	}
	return condition
}

func (req *CreateBlogReq) Dto2EntityCreateBlogReq() (blog *entity.Blog) {
	blog = &entity.Blog{}
	blog.Title = req.Title
	blog.Description = req.Description
	blog.Content = req.Content
	blog.FeaturedImage = req.FeaturedImage
	blog.Category = req.Category
	blog.PublishedAt = req.PublishedAt
	blog.Tags = req.Tags
	blog.MerchantID = req.MerchantID
	return blog
}

func (req *UpdateBlogReq) Dto2EntityUpdateBlogReq(blog *entity.Blog) *entity.Blog {
	if strings.TrimSpace(req.Title) != "" {
		blog.Title = req.Title
	}
	if strings.TrimSpace(req.Description) != "" {
		blog.Description = req.Description
	}
	if strings.TrimSpace(req.Content) != "" {
		blog.Content = req.Content
	}
	if strings.TrimSpace(req.FeaturedImage) != "" {
		blog.FeaturedImage = req.FeaturedImage
	}
	if strings.TrimSpace(req.Category) != "" {
		blog.Category = req.Category
	}
	if len(req.Tags) > 0 {
		blog.Tags = req.Tags
	}
	if req.MerchantID > 0 {
		blog.MerchantID = req.MerchantID
	}
	blog.UpdatedAt = time.Now()
	return blog
}

func Entity2DtoBlogDetailResp(blog *entity.Blog, merchant *merchantentity.Merchant) (resp *BlogDetailResp) {
	resp = &BlogDetailResp{}
	resp.ID = blog.ID
	resp.Title = blog.Title
	resp.Description = blog.Description
	resp.Content = blog.Content
	resp.FeaturedImage = blog.FeaturedImage
	resp.PublishedAt = blog.PublishedAt
	resp.Category = blog.Category
	resp.Tags = blog.Tags
	if merchant != nil {
		resp.MerchantInfo = dto.Entity2DtoMerchantDetailResp(merchant, nil)
	} else {
		resp.MerchantInfo = &dto.MerchantDetailResp{}
	}
	return resp
}

func Entity2DtoBlogListResp(pageSize int, page int, total int64, blogList []*entity.Blog, merchantMap map[uint64]*merchantentity.Merchant) (resp *BlogListResp) {
	// 获取列表时可以接受没有商家信息
	resp = &BlogListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range blogList {
		if merchant, ok := merchantMap[blogList[i].MerchantID]; ok {
			resp.BlogList = append(resp.BlogList, Entity2DtoBlogDetailResp(blogList[i], merchant))
		} else {
			resp.BlogList = append(resp.BlogList, Entity2DtoBlogDetailResp(blogList[i], nil))
		}
	}
	return resp
}
