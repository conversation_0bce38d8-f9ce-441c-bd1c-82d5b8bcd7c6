package appservice

import (
	"bonusearned/application/blog/dto"
	"bonusearned/domain/blog/service"
	merchantentity "bonusearned/domain/merchant/entity"
	merchantservice "bonusearned/domain/merchant/service"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

type BlogAppService interface {
	// CreateBlog 创建博客
	CreateBlog(ctx *gin.Context, req *dto.CreateBlogReq) *ecode.Error
	// UpdateBlog 更新博客
	UpdateBlog(ctx *gin.Context, req *dto.UpdateBlogReq) *ecode.Error
	// DeleteBlog 删除博客
	DeleteBlog(ctx *gin.Context, id uint64) *ecode.Error
	// GetBlogDetailById 根据ID查找博客
	GetBlogDetailById(ctx *gin.Context, id uint64, userId uint64, userCode string) (*dto.BlogDetailResp, *ecode.Error)
	// GetBlogListByCondition 搜索博客
	GetBlogListByCondition(ctx *gin.Context, req *dto.GetBlogListReq, userId uint64, userCode string) (*dto.BlogListResp, *ecode.Error)
	GetBlogCount(ctx *gin.Context) (int64, *ecode.Error)
}

type BlogAppServiceImpl struct {
	blogService     service.BlogService
	merchantService merchantservice.MerchantService
}

func NewBlogAppService(
	blogService service.BlogService,
	merchantService merchantservice.MerchantService,
) BlogAppService {
	return &BlogAppServiceImpl{
		blogService:     blogService,
		merchantService: merchantService,
	}
}

func (app *BlogAppServiceImpl) CreateBlog(ctx *gin.Context, req *dto.CreateBlogReq) *ecode.Error {
	blog := req.Dto2EntityCreateBlogReq()
	if err := app.blogService.CreateBlog(ctx, blog); err != nil {
		return err
	}
	return nil
}

func (app *BlogAppServiceImpl) UpdateBlog(ctx *gin.Context, req *dto.UpdateBlogReq) *ecode.Error {
	blog, err := app.blogService.GetBlogDetailById(ctx, req.ID)
	if err != nil {
		return err
	}
	newBlog := req.Dto2EntityUpdateBlogReq(blog)
	err = app.blogService.UpdateBlog(ctx, newBlog)
	if err != nil {
		return err
	}
	return nil
}

func (app *BlogAppServiceImpl) GetBlogCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.blogService.GetBlogCount(ctx)
}

func (app *BlogAppServiceImpl) DeleteBlog(ctx *gin.Context, id uint64) *ecode.Error {
	return app.blogService.DeleteBlog(ctx, id)
}

func (app *BlogAppServiceImpl) GetBlogDetailById(ctx *gin.Context, id uint64, userId uint64, userCode string) (*dto.BlogDetailResp, *ecode.Error) {
	blog, err := app.blogService.GetBlogDetailById(ctx, id)
	if err != nil {
		return nil, err
	}
	merchant, _ := app.merchantService.GetMerchantDetailById(ctx, blog.MerchantID, userId, userCode)

	return dto.Entity2DtoBlogDetailResp(blog, merchant), nil
}

func (app *BlogAppServiceImpl) GetBlogListByCondition(ctx *gin.Context, req *dto.GetBlogListReq, userId uint64, userCode string) (*dto.BlogListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetBlogList()
	blogList, total, err := app.blogService.GetBlogListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	merchantIds := make([]uint64, 0)
	for i := range blogList {
		merchantIds = append(merchantIds, blogList[i].MerchantID)
	}
	merchantList, err := app.merchantService.GetMerchantListByIDs(ctx, merchantIds, userId, userCode)
	if err != nil {
		return nil, err
	}
	merchantMap := make(map[uint64]*merchantentity.Merchant, 0)
	for i := range merchantList {
		merchantMap[merchantList[i].ID] = merchantList[i]
	}

	return dto.Entity2DtoBlogListResp(req.PageSize, req.Page, total, blogList, merchantMap), nil
}
