package dto

import (
	"bonusearned/domain/merchant/entity"
	"strings"
	"time"
)

type GetCategoryListReq struct {
	Page     int `json:"page" form:"page"`
	PageSize int `json:"page_size" form:"page_size"`
	Offset   int `json:"offset"`
}

// CreateCategoryReq 创建商家分类请求
type CreateCategoryReq struct {
	Name string `json:"name" binding:"required,min=2,max=50"`
	Icon string `json:"icon"` // 分类图标
}

// UpdateCategoryReq 更新商家分类请求
type UpdateCategoryReq struct {
	ID     uint64 `json:"id"`
	Name   string `json:"name" binding:"omitempty,min=2,max=50"`
	Icon   string `json:"icon"` // 分类图标
	Status int8   `json:"status" binding:"omitempty,oneof=0 1"`
}

// CategoryDetailResp 商家分类响应
type CategoryDetailResp struct {
	ID   uint64 `json:"id"`
	Name string `json:"name"`
	Icon string `json:"icon"` // 分类图标
}

// CategoryListResp 商家分类列表响应
type CategoryListResp struct {
	Total        int64                 `json:"total"`
	CategoryList []*CategoryDetailResp `json:"category_list"`
	Page         int                   `json:"page"`
	PageSize     int                   `json:"page_size"`
}

func (req *GetCategoryListReq) Dto2ConditionGetCategoryList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	return condition
}

func Entity2DtoCategoryDetailResp(category *entity.Category) (resp *CategoryDetailResp) {
	resp = &CategoryDetailResp{}
	resp.ID = category.ID
	resp.Name = category.Name
	resp.Icon = category.Icon
	return resp
}

func (req *CreateCategoryReq) Dto2EntityCreateCategoryReq() (category *entity.Category) {
	category = &entity.Category{}
	category.Name = req.Name
	category.Icon = req.Icon
	category.CreatedAt = time.Now()
	category.UpdatedAt = time.Now()
	return category
}

func (req *UpdateCategoryReq) Dto2EntityUpdateCategoryReq(category *entity.Category) *entity.Category {
	if strings.TrimSpace(req.Name) != "" {
		category.Name = req.Name
	}
	if strings.TrimSpace(req.Icon) != "" {
		category.Icon = req.Icon
	}
	if req.Status == 0 || req.Status == 1 {
		category.Status = req.Status
	}
	category.UpdatedAt = time.Now()
	return category
}

func Entity2DtoCategoryListResp(pageSize int, page int, total int64, categoryList []*entity.Category) (resp *CategoryListResp) {
	resp = &CategoryListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range categoryList {
		resp.CategoryList = append(resp.CategoryList, Entity2DtoCategoryDetailResp(categoryList[i]))
	}
	return resp
}
