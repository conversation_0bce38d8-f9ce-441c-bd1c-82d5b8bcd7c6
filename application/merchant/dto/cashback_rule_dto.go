package dto

import (
	"bonusearned/domain/merchant/entity"
	"github.com/shopspring/decimal"
	"time"
)

type GetCashbackRuleListReq struct {
	Page       int    `json:"page" form:"page"`
	PageSize   int    `json:"page_size" form:"page_size"`
	Offset     int    `json:"offset"`
	UserID     uint64 `json:"user_id"`
	MerchantID uint64 `json:"merchant_id"`
	IsGlobal   bool   `json:"is_global"`
}

// CreateCashbackRuleReq 创建商家返现规则请求
type CreateCashbackRuleReq struct {
	UserID        uint64          `json:"user_id" binding:"required"`
	MerchantID    uint64          `json:"merchant_id"`
	CashbackRate  decimal.Decimal `json:"cashback_rate" binding:"required"`
	CashbackValue decimal.Decimal `json:"cashback_value"`
	IsGlobal      bool            `json:"is_global"`
	StartTime     time.Time       `json:"start_time"`
	EndTime       time.Time       `json:"end_time"`
}

// UpdateCashbackRuleReq 更新商家返现规则请求
type UpdateCashbackRuleReq struct {
	ID            uint64          `json:"id"`
	UserID        uint64          `json:"user_id"`
	MerchantID    uint64          `json:"merchant_id"`
	IsGlobal      bool            `json:"is_global"` // 返现比例
	Status        int8            `json:"status" binding:"omitempty,oneof=0 1"`
	CashbackValue decimal.Decimal `json:"cashback_value"` // 返现值
	CashbackRate  decimal.Decimal `json:"cashback_rate"`  // 返现比例
	StartTime     time.Time       `json:"start_time"`     // 生效开始时间
	EndTime       time.Time       `json:"end_time"`       // 生效结束时间
}

// CashbackRuleDetailResp 商家返现规则响应
type CashbackRuleDetailResp struct {
	ID            uint64          `json:"id"`
	UserID        uint64          `json:"user_id"`
	MerchantID    uint64          `json:"merchant_id"`
	CashbackValue decimal.Decimal `json:"cashback_value"` // 返现值
	CashbackRate  decimal.Decimal `json:"cashback_rate"`  // 返现比例
	IsGlobal      bool            `json:"is_global"`      // 全局返现
	StartTime     time.Time       `json:"start_time"`     // 生效开始时间
	EndTime       time.Time       `json:"end_time"`       // 生效结束时间
	CreatedAt     time.Time       `json:"created_at"`
}

// CashbackRuleListResp 商家返现规则列表响应
type CashbackRuleListResp struct {
	Total            int64                     `json:"total"`
	CashbackRuleList []*CashbackRuleDetailResp `json:"cashback_rule_list"`
	Page             int                       `json:"page"`
	PageSize         int                       `json:"page_size"`
}

// Dto2ConditionGetCashbackRuleList 获取商家列表请求转换为 condition
func (req *GetCashbackRuleListReq) Dto2ConditionGetCashbackRuleList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.UserID > 0 {
		condition["user_id"] = req.UserID
	}
	if req.MerchantID > 0 {
		condition["merchant_id"] = req.MerchantID
	}
	condition["is_global"] = req.IsGlobal
	return condition
}

// Dto2EntityCreateCashbackRuleReq 将请求转换为实体
func (req *CreateCashbackRuleReq) Dto2EntityCreateCashbackRuleReq() *entity.CashbackRule {
	cashbackRule := &entity.CashbackRule{}
	cashbackRule.UserID = req.UserID
	cashbackRule.MerchantID = req.MerchantID
	cashbackRule.CashbackRate = req.CashbackRate
	cashbackRule.IsGlobal = req.IsGlobal
	cashbackRule.StartTime = req.StartTime
	cashbackRule.EndTime = req.EndTime
	cashbackRule.CreatedAt = time.Now()
	cashbackRule.UpdatedAt = time.Now()
	return cashbackRule
}

func (req *UpdateCashbackRuleReq) Dto2EntityUpdateCashbackRuleReq(rule *entity.CashbackRule) *entity.CashbackRule {

	rule.UpdatedAt = time.Now()

	return rule
}

// Entity2DtoCashbackRuleDetailResp 将实体转换为响应
func Entity2DtoCashbackRuleDetailResp(cashbackRule *entity.CashbackRule) (resp *CashbackRuleDetailResp) {
	resp = &CashbackRuleDetailResp{}
	resp.ID = cashbackRule.ID
	resp.UserID = cashbackRule.UserID
	resp.MerchantID = cashbackRule.MerchantID
	resp.CashbackRate = cashbackRule.CashbackRate
	resp.IsGlobal = cashbackRule.IsGlobal
	resp.StartTime = cashbackRule.StartTime
	resp.EndTime = cashbackRule.EndTime
	resp.CreatedAt = cashbackRule.CreatedAt
	return resp
}

func Entity2DtoCashbackRuleListResp(pageSize int, page int, total int64, cashbackRuleList []*entity.CashbackRule) (resp *CashbackRuleListResp) {
	resp = &CashbackRuleListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range cashbackRuleList {
		resp.CashbackRuleList = append(resp.CashbackRuleList, Entity2DtoCashbackRuleDetailResp(cashbackRuleList[i]))
	}
	return resp
}
