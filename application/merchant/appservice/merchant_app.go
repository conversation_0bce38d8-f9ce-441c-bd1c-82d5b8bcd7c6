package appservice

import (
	"bonusearned/application/merchant/dto"
	"bonusearned/domain/merchant/entity"
	"bonusearned/domain/merchant/service"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

type MerchantAppService interface {
	GetMerchantDetailById(ctx *gin.Context, id uint64, userId uint64, userCode string) (*dto.MerchantDetailResp, *ecode.Error)
	GetMerchantDetailByCode(ctx *gin.Context, merchantCode string, userId uint64, userCode string) (*dto.MerchantDetailResp, *ecode.Error)
	GetMerchantDetailByUniqueName(ctx *gin.Context, uniqueName string, userId uint64, userCode string) (*dto.MerchantDetailResp, *ecode.Error)
	GetMerchantList(ctx *gin.Context, req *dto.GetMerchantListReq, userId uint64, userCode string) (*dto.MerchantListResp, *ecode.Error)
	GetMerchantCount(ctx *gin.Context) (int64, *ecode.Error)
	CreateMerchant(ctx *gin.Context, req *dto.CreateMerchantReq) *ecode.Error
	UpdateMerchant(ctx *gin.Context, req *dto.UpdateMerchantReq) *ecode.Error
	DeleteMerchant(ctx *gin.Context, id uint64) *ecode.Error
	UpdateMerchantStatus(ctx *gin.Context, id uint64, status int8) *ecode.Error
}

type MerchantAppServiceImpl struct {
	merchantService service.MerchantService
	categoryService service.CategoryService
}

func NewMerchantAppService(merchantService service.MerchantService, categoryService service.CategoryService) MerchantAppService {
	return &MerchantAppServiceImpl{
		merchantService: merchantService,
		categoryService: categoryService,
	}
}

func (app *MerchantAppServiceImpl) GetMerchantDetailById(ctx *gin.Context, id uint64, userId uint64, userCode string) (*dto.MerchantDetailResp, *ecode.Error) {
	// 1. 获取商家基础数据
	merchant, err := app.merchantService.GetMerchantDetailById(ctx, id, userId, userCode)
	if err != nil {
		return nil, err
	}
	// 2. 获取分类信息
	category, err := app.categoryService.GetCategoryDetailById(ctx, merchant.CategoryID)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoMerchantDetailResp(merchant, category), nil
}

func (app *MerchantAppServiceImpl) GetMerchantDetailByCode(ctx *gin.Context, merchantCode string, userId uint64, userCode string) (*dto.MerchantDetailResp, *ecode.Error) {
	merchant, err := app.merchantService.GetMerchantDetailByCode(ctx, merchantCode, userId, userCode)
	if err != nil {
		return nil, err
	}
	// 2. 获取分类信息
	category, err := app.categoryService.GetCategoryDetailById(ctx, merchant.CategoryID)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoMerchantDetailResp(merchant, category), nil
}

func (app *MerchantAppServiceImpl) GetMerchantDetailByUniqueName(ctx *gin.Context, uniqueName string, userId uint64, userCode string) (*dto.MerchantDetailResp, *ecode.Error) {
	merchant, err := app.merchantService.GetMerchantDetailByUniqueName(ctx, uniqueName, userId, userCode)
	if err != nil {
		return nil, err
	}
	// 2. 获取分类信息
	category, _ := app.categoryService.GetCategoryDetailById(ctx, merchant.CategoryID)
	return dto.Entity2DtoMerchantDetailResp(merchant, category), nil
}

func (app *MerchantAppServiceImpl) GetMerchantList(ctx *gin.Context, req *dto.GetMerchantListReq, userId uint64, userCode string) (*dto.MerchantListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetMerchantList()
	merchants, total, err := app.merchantService.GetMerchantListByCondition(ctx, condition, userId, userCode)
	if err != nil {
		return nil, err
	}
	// 2. 获取分类信息
	categoryList, _, err := app.categoryService.GetCategoryListByCondition(ctx, map[string]interface{}{})
	if err != nil {
		return nil, err
	}
	categoryMap := make(map[uint64]*entity.Category)
	for i := range categoryList {
		categoryMap[categoryList[i].ID] = categoryList[i]
	}
	resp := dto.Entity2DtoMerchantListResp(req.PageSize, req.Page, total, merchants, categoryMap)

	return resp, nil
}

func (app *MerchantAppServiceImpl) GetMerchantCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.merchantService.GetMerchantCount(ctx)
}

func (app *MerchantAppServiceImpl) CreateMerchant(ctx *gin.Context, req *dto.CreateMerchantReq) *ecode.Error {
	merchant := req.Dto2EntityCreateMerchantReq()
	if err := app.merchantService.CreateMerchant(ctx, merchant); err != nil {
		return err
	}
	return nil
}

func (app *MerchantAppServiceImpl) UpdateMerchant(ctx *gin.Context, req *dto.UpdateMerchantReq) *ecode.Error {
	//merchant := req.
	//if err := app.merchantService.UpdateMerchant(ctx, merchant); err != nil {
	//	return err
	//}
	return nil
}

func (app *MerchantAppServiceImpl) UpdateMerchantStatus(ctx *gin.Context, id uint64, status int8) *ecode.Error {
	return app.merchantService.UpdateMerchantStatus(ctx, id, status)
}

func (app *MerchantAppServiceImpl) DeleteMerchant(ctx *gin.Context, id uint64) *ecode.Error {
	return app.merchantService.DeleteMerchant(ctx, id)
}
