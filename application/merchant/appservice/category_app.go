package appservice

import (
	"bonusearned/application/merchant/dto"
	"bonusearned/domain/merchant/service"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// CategoryAppService 商家分类应用服务接口
type CategoryAppService interface {
	// CreateCategory 创建商家分类
	CreateCategory(ctx *gin.Context, req *dto.CreateCategoryReq) *ecode.Error
	// UpdateCategory 更新商家分类
	UpdateCategory(ctx *gin.Context, req *dto.UpdateCategoryReq) *ecode.Error
	// GetCategoryDetailById 根据ID获取商家分类
	GetCategoryDetailById(ctx *gin.Context, id uint64) (*dto.CategoryDetailResp, *ecode.Error)
	// GetCategoryDetailByName 根据名称获取商家分类
	GetCategoryDetailByName(ctx *gin.Context, name string) (*dto.CategoryDetailResp, *ecode.Error)
	// GetCategoryList 获取商家分类列表
	GetCategoryList(ctx *gin.Context, req *dto.GetCategoryListReq) (*dto.CategoryListResp, *ecode.Error)
}

type CategoryAppServiceImpl struct {
	categoryService service.CategoryService
}

// NewCategoryAppService 创建商家分类应用服务
func NewCategoryAppService(categoryService service.CategoryService) CategoryAppService {
	return &CategoryAppServiceImpl{
		categoryService: categoryService,
	}
}

func (app *CategoryAppServiceImpl) GetCategoryDetailById(ctx *gin.Context, id uint64) (*dto.CategoryDetailResp, *ecode.Error) {
	category, err := app.categoryService.GetCategoryDetailById(ctx, id)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoCategoryDetailResp(category), nil
}

func (app *CategoryAppServiceImpl) GetCategoryDetailByName(ctx *gin.Context, name string) (*dto.CategoryDetailResp, *ecode.Error) {
	category, err := app.categoryService.GetCategoryDetailByName(ctx, name)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoCategoryDetailResp(category), nil
}

func (app *CategoryAppServiceImpl) GetCategoryList(ctx *gin.Context, req *dto.GetCategoryListReq) (*dto.CategoryListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetCategoryList()
	categoryList, total, err := app.categoryService.GetCategoryListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoCategoryListResp(req.PageSize, req.Page, total, categoryList), nil
}

func (app *CategoryAppServiceImpl) CreateCategory(ctx *gin.Context, req *dto.CreateCategoryReq) *ecode.Error {
	category := req.Dto2EntityCreateCategoryReq()
	err := app.categoryService.CreateCategory(ctx, category)
	if err != nil {
		return err
	}
	return nil
}

func (app *CategoryAppServiceImpl) UpdateCategory(ctx *gin.Context, req *dto.UpdateCategoryReq) *ecode.Error {
	// 先获取，确保存在
	category, err := app.categoryService.GetCategoryDetailById(ctx, req.ID)
	if err != nil {
		return err
	}
	newCategory := req.Dto2EntityUpdateCategoryReq(category)
	err = app.categoryService.UpdateCategory(ctx, newCategory)
	if err != nil {
		return err
	}
	return nil
}
