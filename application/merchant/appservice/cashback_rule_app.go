package appservice

import (
	"bonusearned/application/merchant/dto"
	"bonusearned/domain/merchant/service"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
)

// CashbackRuleAppService 商家返现规则应用服务接口
type CashbackRuleAppService interface {
	// CreateCashbackRule 创建商家返现规则
	CreateCashbackRule(ctx *gin.Context, req *dto.CreateCashbackRuleReq) *ecode.Error
	// UpdateCashbackRule 更新商家返现规则
	UpdateCashbackRule(ctx *gin.Context, req *dto.UpdateCashbackRuleReq) *ecode.Error
	// GetCashbackRuleDetailByID 根据ID获取返现规则详情
	GetCashbackRuleDetailByID(ctx *gin.Context, id uint64) (*dto.CashbackRuleDetailResp, *ecode.Error)
	// GetCashbackRuleByUserAndMerchant 根据用户ID和商家ID获取商家返现规则
	GetCashbackRuleByUserAndMerchant(ctx *gin.Context, userID, merchantID uint64) (*dto.CashbackRuleDetailResp, *ecode.Error)
	// GetCashbackGlobalRuleByUserId 获取用户的全局返现规则
	GetCashbackGlobalRuleByUserId(ctx *gin.Context, userID uint64) (*dto.CashbackRuleDetailResp, *ecode.Error)
	// GetCashbackRuleList 获取返现规则列表
	GetCashbackRuleList(ctx *gin.Context, req *dto.GetCashbackRuleListReq) (*dto.CashbackRuleListResp, *ecode.Error)
	// DeleteCashbackRule 删除过期的返现规则
	DeleteCashbackRule(ctx *gin.Context, id uint64) *ecode.Error
}

type CashbackRuleAppServiceImpl struct {
	cashbackRuleService service.CashbackRuleService
}

// NewCashbackRuleAppService 创建商家返现规则应用服务
func NewCashbackRuleAppService(cashbackRuleService service.CashbackRuleService) CashbackRuleAppService {
	return &CashbackRuleAppServiceImpl{
		cashbackRuleService: cashbackRuleService,
	}
}

func (app *CashbackRuleAppServiceImpl) GetCashbackRuleDetailByID(ctx *gin.Context, id uint64) (*dto.CashbackRuleDetailResp, *ecode.Error) {
	cashbackRule, err := app.cashbackRuleService.GetCashbackRuleDetailByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoCashbackRuleDetailResp(cashbackRule), nil
}

func (app *CashbackRuleAppServiceImpl) GetCashbackRuleByUserAndMerchant(ctx *gin.Context, userID, merchantID uint64) (*dto.CashbackRuleDetailResp, *ecode.Error) {
	cashbackRule, err := app.cashbackRuleService.GetCashbackRuleByUserAndMerchant(ctx, userID, merchantID)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoCashbackRuleDetailResp(cashbackRule), nil
}
func (app *CashbackRuleAppServiceImpl) GetCashbackGlobalRuleByUserId(ctx *gin.Context, userID uint64) (*dto.CashbackRuleDetailResp, *ecode.Error) {
	cashbackRule, err := app.cashbackRuleService.GetCashbackGlobalRuleByUserId(ctx, userID)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoCashbackRuleDetailResp(cashbackRule), nil
}

func (app *CashbackRuleAppServiceImpl) GetCashbackRuleList(ctx *gin.Context, req *dto.GetCashbackRuleListReq) (*dto.CashbackRuleListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetCashbackRuleList()
	cashbackRuleList, total, err := app.cashbackRuleService.GetCashbackRuleListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoCashbackRuleListResp(req.PageSize, req.Page, total, cashbackRuleList), nil
}

func (app *CashbackRuleAppServiceImpl) CreateCashbackRule(ctx *gin.Context, req *dto.CreateCashbackRuleReq) *ecode.Error {
	cashbackRule := req.Dto2EntityCreateCashbackRuleReq()
	err := app.cashbackRuleService.CreateCashbackRule(ctx, cashbackRule)
	if err != nil {
		return err
	}
	return nil
}

func (app *CashbackRuleAppServiceImpl) UpdateCashbackRule(ctx *gin.Context, req *dto.UpdateCashbackRuleReq) *ecode.Error {
	// 先获取，确保存在
	cashbackRule, err := app.cashbackRuleService.GetCashbackRuleDetailByID(ctx, req.ID)
	if err != nil {
		return err
	}
	newCashbackRule := req.Dto2EntityUpdateCashbackRuleReq(cashbackRule)
	err = app.cashbackRuleService.UpdateCashbackRule(ctx, newCashbackRule)
	if err != nil {
		return err
	}
	return nil
}

func (app *CashbackRuleAppServiceImpl) DeleteCashbackRule(ctx *gin.Context, id uint64) *ecode.Error {
	err := app.cashbackRuleService.DeleteCashbackRule(ctx, id)
	if err != nil {
		return err
	}
	return nil
}
