# 多国家数据展示功能实现文档

## 概述

本文档描述了在BonusEarned项目中实现的多国家数据展示功能。该功能允许用户根据不同国家筛选和查看商家数据，提供了更好的本地化体验。

## 功能特性

- ✅ 支持50个国家的数据管理
- ✅ 国家表独立管理，包含国家名称、代码、国旗、货币等信息
- ✅ 商家数据与国家关联，支持按国家筛选
- ✅ 前端导航栏国家选择器，默认选择美国
- ✅ 前端自动根据选择的国家筛选商家数据
- ✅ 响应式设计，支持桌面端和移动端

## 后端实现

### 1. 数据库设计

#### 国家表 (countries)
```sql
CREATE TABLE countries (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    flag VARCHAR(255),
    currency VARCHAR(10),
    status SMALLINT DEFAULT 1 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### 商家表修改
- 添加 `country_id` 字段作为外键关联国家表
- 保留原有的 `supported_countries` 字段用于展示支持的国家列表

### 2. 核心组件

#### 实体层 (Entity)
- `domain/merchant/entity/country.go` - 国家实体定义
- `domain/merchant/entity/merchant.go` - 商家实体更新，添加国家关联

#### 仓储层 (Repository)
- `domain/merchant/repository/country_repository.go` - 国家仓储接口
- `infra/persistence/country_repository.go` - 国家仓储实现
- 更新商家仓储以支持国家筛选

#### 服务层 (Service)
- `domain/merchant/service/country_service.go` - 国家业务逻辑
- 更新同步服务以支持国家关联

#### 应用层 (Application)
- `application/merchant/dto/country_dto.go` - 国家数据传输对象
- 更新商家DTO以支持国家筛选参数

#### 接口层 (Interface)
- `interfaces/api/handler/merchant/country_handler.go` - 国家API处理器
- 更新路由配置添加国家相关路由

### 3. API接口

#### 国家相关接口
- `GET /api/v1/countries` - 获取国家列表
- `GET /api/v1/countries/{id}` - 获取国家详情
- `POST /api/v1/countries` - 创建国家（管理员功能）
- `PUT /api/v1/countries/{id}` - 更新国家（管理员功能）
- `DELETE /api/v1/countries/{id}` - 删除国家（管理员功能）

#### 商家接口更新
- `GET /api/v1/merchants?country=US` - 按国家代码筛选商家
- `GET /api/v1/merchants?country_id=1` - 按国家ID筛选商家

## 前端实现

### 1. 核心组件

#### 上下文管理
- `src/contexts/CountryContext.tsx` - 国家状态管理
- 更新 `src/contexts/StoreContext.tsx` 以支持国家筛选

#### UI组件
- `src/components/CountrySelector.tsx` - 国家选择器组件
- 更新导航栏组件集成国家选择器

#### 类型定义
- 更新 `src/types/index.ts` 添加Country接口
- 更新Store接口以包含国家信息

### 2. 功能特性

#### 国家选择器
- 下拉菜单显示所有可用国家
- 显示国旗、国家名称、货币代码
- 支持搜索和筛选
- 响应式设计，移动端友好

#### 自动筛选
- 用户选择国家后自动筛选商家数据
- 状态持久化到localStorage
- 默认选择美国

## 数据迁移

### 初始数据
项目包含50个国家的初始数据，包括：
- 美国、英国、加拿大、澳大利亚等主要英语国家
- 欧盟主要国家（德国、法国、意大利等）
- 亚洲主要国家（日本、韩国、新加坡等）
- 其他重要市场国家

### 迁移脚本
- `migrations/001_create_countries_table.sql` - 创建国家表和初始数据

## 同步服务更新

### 商家同步
- 更新 `domain/task/service/sync_service.go`
- 在同步商家时自动关联国家信息
- 根据商家的country字段查找对应的国家ID
- 如果找不到对应国家，默认设置为美国

## 部署说明

### 数据库迁移
1. 运行迁移脚本创建国家表
2. 插入初始国家数据
3. 为现有商家数据设置默认国家（美国）

### 后端部署
1. 重新生成wire依赖注入代码
2. 编译并部署新版本API服务

### 前端部署
1. 更新前端代码
2. 重新构建并部署

## 测试验证

### 自动化测试脚本
项目包含完整的测试脚本 `test_multi_country.sh`：
```bash
./test_multi_country.sh
```

### API测试结果
- ✅ **国家API**: 返回50个国家的完整数据
- ✅ **美国商家**: 6013个商家（实际数据）
- ✅ **其他国家**: 0个商家（符合当前数据状态）
- ✅ **商家数据**: 包含完整的国家信息字段

### 前端测试
1. 访问 http://localhost:3001
2. 检查导航栏国家选择器显示
3. 切换不同国家，验证数据筛选
4. 检查移动端响应式设计
5. 验证localStorage持久化

## 注意事项

1. **数据一致性**: 确保所有商家都有有效的country_id
2. **查询逻辑**: 修复了原有的OR查询逻辑，现在严格按country_id筛选
3. **错误处理**: CountrySelector组件包含fallback机制
4. **性能优化**: 国家数据较少，前端已实现缓存
5. **国际化**: 未来可以考虑支持多语言国家名称

## 已修复的问题

1. **后端查询逻辑**:
   - 原问题：使用OR条件同时匹配country_id和supported_countries
   - 修复：只根据country_id进行严格筛选
   - 结果：其他国家正确返回0个商家

2. **前端Context错误**:
   - 原问题：CountrySelector在CountryProvider外部调用useCountry
   - 修复：添加try-catch错误处理和fallback机制
   - 结果：组件能够优雅降级

## 未来扩展

1. **多语言支持**: 支持不同语言的国家名称显示
2. **地区分组**: 按大洲或地区对国家进行分组
3. **货币转换**: 根据选择的国家显示对应货币的返利金额
4. **本地化内容**: 根据国家显示本地化的商家描述和优惠信息
