frontend:
  frontend_path: /Users/<USER>/projects/bonusearned/frontend-react
postgres:
  host: localhost
  port: 5432
  user: postgres
  password: postgres
  dbname: bonusearned
  sslmode: disable
  timezone: Asia/Shanghai

#postgres:
#  host: **************
#  port: 14614
#  user: user_SgXD8fYdcn7mPs6kttjk
#  password: password_BkGnEmjRrBEJYcv8xHsU
#  dbname: bonusearned
#  sslmode: disable
#  timezone: Asia/Shanghai

redis:
  host: localhost
  port: 6379
  db: 0
  password: ""

logger:
  level: debug
  encoding: json
  output_paths:
    - stdout
    - logs/app.log
  error_output_paths:
    - stderr
    - logs/error.log

monitoring:
  prometheus:
    enabled: true
    port: 9090
  tracing:
    enabled: true
    endpoint: http://jaeger:14268/api/traces

cloudflare_r2:
  account_id: "5ecf52532b8dc3acbc9bed4044bd41a5"
  access_key_id: "1249d9d685c4a396d17745b2780a747b"
  secret_access_key: "86d29df638f2d8f2c6cab10a2130a190eb5c072f920bbadcf82f64e3b2ff85a3"
  bucket_name: "bonusearned"
  public_url: "https://img.bonusearned.com"

imgbox:
  upload_token: "********************************"
  public_url: "https://image.bonusearned.com"
