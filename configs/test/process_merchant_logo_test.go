package test

import (
	"bonusearned/infra/utils/imageutil"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

// TestProcessMerchantLogo tests the ProcessMerchantLogo function with real and mock data
func TestProcessMerchantLogo(t *testing.T) {
	// Create a temporary directory for test files
	tempDir, err := os.MkdirTemp("", "merchant_logos_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir) // Clean up after test

	// Test cases with real logo URLs
	testCases := []struct {
		name              string
		logoURL           string
		merchantCode      string
		merchantName      string
		expectError       bool
		expectDefaultLogo bool
	}{
		{
			name:              "Valid Amazon Logo",
			logoURL:           "https://logo.clearbit.com/amazon.com",
			merchantCode:      "M123456789012",
			merchantName:      "Amazon",
			expectError:       false,
			expectDefaultLogo: false,
		},
		{
			name:              "Valid Apple Logo",
			logoURL:           "https://logo.clearbit.com/apple.com",
			merchantCode:      "M234567890123",
			merchantName:      "Apple",
			expectError:       false,
			expectDefaultLogo: false,
		},
		{
			name:              "Valid Microsoft Logo",
			logoURL:           "https://logo.clearbit.com/microsoft.com",
			merchantCode:      "M345678901234",
			merchantName:      "Microsoft",
			expectError:       false,
			expectDefaultLogo: false,
		},
		{
			name:              "Invalid URL - Should Use Default Logo",
			logoURL:           "https://nonexistent-domain-12345.com/logo.png",
			merchantCode:      "M456789012345",
			merchantName:      "Nonexistent Store",
			expectError:       false,
			expectDefaultLogo: true,
		},
		{
			name:              "Empty URL - Should Use Default Logo",
			logoURL:           "",
			merchantCode:      "M567890123456",
			merchantName:      "Empty URL Store",
			expectError:       false,
			expectDefaultLogo: true,
		},
	}

	// Run test cases
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Process the logo
			filename, imageData, err := imageutil.ProcessMerchantLogo(tc.logoURL, tc.merchantCode, tempDir, tc.merchantName)

			// Check results
			if tc.expectError {
				if err == nil {
					t.Errorf("Expected error but got none for URL: %s", tc.logoURL)
				} else {
					t.Logf("Got expected error for URL %s: %v", tc.logoURL, err)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for URL %s: %v", tc.logoURL, err)
				} else {
					// Verify the results
					if filename == "" {
						t.Error("Expected non-empty filename but got empty string")
					}
					if len(imageData) == 0 {
						t.Error("Expected non-empty image data but got empty data")
					}

					// Check if file exists in temp directory
					filePath := filepath.Join(tempDir, filename)
					if _, err := os.Stat(filePath); os.IsNotExist(err) {
						t.Errorf("Expected file %s to exist but it doesn't", filePath)
					}

					// Check if it's a default logo
					isDefaultLogo := strings.Contains(filename, "default_logo")
					if tc.expectDefaultLogo && !isDefaultLogo {
						t.Errorf("Expected default logo but got regular logo: %s", filename)
					} else if !tc.expectDefaultLogo && isDefaultLogo {
						t.Errorf("Expected regular logo but got default logo: %s", filename)
					}

					t.Logf("Successfully processed logo for %s: filename=%s, data size=%d bytes, isDefaultLogo=%v",
						tc.merchantCode, filename, len(imageData), isDefaultLogo)
				}
			}
		})
	}
}

// TestMain is the entry point for running the tests
func TestMain(m *testing.M) {
	// Setup before tests
	fmt.Println("Starting ProcessMerchantLogo tests...")

	// Run tests
	exitCode := m.Run()

	// Cleanup after tests
	fmt.Println("Completed ProcessMerchantLogo tests.")

	// Exit with the same code as the tests
	os.Exit(exitCode)
}
