database:
  driver: postgres
  host: localhost
  port: 5432
  name: cashbackany_test
  user: postgres
  password: postgres
  max_open_conns: 20
  max_idle_conns: 5
  sslmode: disable
  timezone: Asia/Shanghai

redis:
  host: localhost
  port: 6379
  db: 1
  password: ""

logger:
  level: info
  encoding: json
  output_paths:
    - stdout
    - logs/app.log
  error_output_paths:
    - stderr
    - logs/error.log

monitoring:
  prometheus:
    enabled: true
    port: 9090
  tracing:
    enabled: true
    endpoint: http://jaeger:14268/api/traces

cloudflare_r2:
  account_id: "test-account-id"
  access_key_id: "test-access-key-id"
  secret_access_key: "test-secret-access-key"
  bucket_name: "test-bucket"
  public_url: "https://test-img.bonusearned.com"

imgbox:
  upload_token: "test-upload-token"
  public_url: "https://test-image.bonusearned.com"
