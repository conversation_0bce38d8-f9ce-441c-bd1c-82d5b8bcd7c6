package test

import (
	"fmt"
	"testing"

	orderentity "bonusearned/domain/order/entity"
	userentity "bonusearned/domain/user/entity"
	"bonusearned/infra/database/postgres"

	"github.com/shopspring/decimal"
)

// TestUserBalanceCalculation 测试用户余额计算逻辑
func TestUserBalanceCalculation(t *testing.T) {
	// 模拟用户数据
	user := &userentity.User{
		ID:       1,
		Email:    "<EMAIL>",
		UserCode: "USER001",
	}

	// 模拟订单数据
	orders := []*orderentity.Order{
		{
			ID:             1,
			UserID:         1,
			CashbackAmount: decimal.NewFromFloat(10.50),
			Status:         orderentity.OrderStatusPending,
		},
		{
			ID:             2,
			UserID:         1,
			CashbackAmount: decimal.NewFromFloat(25.00),
			Status:         orderentity.OrderStatusApproved,
		},
		{
			ID:             3,
			UserID:         1,
			CashbackAmount: decimal.NewFromFloat(15.75),
			Status:         orderentity.OrderStatusPaid,
		},
		{
			ID:             4,
			UserID:         1,
			CashbackAmount: decimal.NewFromFloat(5.00),
			Status:         orderentity.OrderStatusRejected, // 这个不应该计入总额
		},
	}

	// 计算预期的余额
	expectedBalance := &userentity.UserBalance{
		TotalAmount:    51.25, // 10.50 + 25.00 + 15.75 (不包括已取消的5.00)
		PendingAmount:  10.50,
		ApprovedAmount: 25.00,
		PaidAmount:     15.75,
	}

	// 模拟余额计算逻辑
	actualBalance := &userentity.UserBalance{
		TotalAmount:    0,
		PendingAmount:  0,
		PaidAmount:     0,
		ApprovedAmount: 0,
	}

	for _, order := range orders {
		if order.UserID == user.ID {
			cashbackFloat, _ := order.CashbackAmount.Float64()

			switch order.Status {
			case orderentity.OrderStatusPending:
				actualBalance.PendingAmount += cashbackFloat
			case orderentity.OrderStatusApproved:
				actualBalance.ApprovedAmount += cashbackFloat
			case orderentity.OrderStatusPaid:
				actualBalance.PaidAmount += cashbackFloat
			}

			// 计算总金额（不包括已取消的订单）
			if order.Status != orderentity.OrderStatusRejected {
				actualBalance.TotalAmount += cashbackFloat
			}
		}
	}

	// 验证计算结果
	if actualBalance.TotalAmount != expectedBalance.TotalAmount {
		t.Errorf("TotalAmount 计算错误: 期望 %.2f, 实际 %.2f",
			expectedBalance.TotalAmount, actualBalance.TotalAmount)
	}

	if actualBalance.PendingAmount != expectedBalance.PendingAmount {
		t.Errorf("PendingAmount 计算错误: 期望 %.2f, 实际 %.2f",
			expectedBalance.PendingAmount, actualBalance.PendingAmount)
	}

	if actualBalance.ApprovedAmount != expectedBalance.ApprovedAmount {
		t.Errorf("ApprovedAmount 计算错误: 期望 %.2f, 实际 %.2f",
			expectedBalance.ApprovedAmount, actualBalance.ApprovedAmount)
	}

	if actualBalance.PaidAmount != expectedBalance.PaidAmount {
		t.Errorf("PaidAmount 计算错误: 期望 %.2f, 实际 %.2f",
			expectedBalance.PaidAmount, actualBalance.PaidAmount)
	}

	fmt.Printf("用户余额计算测试通过:\n")
	fmt.Printf("  总金额: %.2f\n", actualBalance.TotalAmount)
	fmt.Printf("  待确认: %.2f\n", actualBalance.PendingAmount)
	fmt.Printf("  已确认: %.2f\n", actualBalance.ApprovedAmount)
	fmt.Printf("  已支付: %.2f\n", actualBalance.PaidAmount)
}

// TestUserBalanceJSONConversion 测试用户余额JSON转换
func TestUserBalanceJSONConversion(t *testing.T) {
	balance := &userentity.UserBalance{
		TotalAmount:    100.50,
		PendingAmount:  25.25,
		ApprovedAmount: 50.00,
		PaidAmount:     25.25,
	}

	// 转换为JSON格式
	balanceMap := map[string]interface{}{
		"total_amount":    balance.TotalAmount,
		"pending_amount":  balance.PendingAmount,
		"paid_amount":     balance.PaidAmount,
		"approved_amount": balance.ApprovedAmount,
	}

	balanceJSON, err := postgres.FromMap(balanceMap)
	if err != nil {
		t.Fatalf("转换用户余额为JSON失败: %v", err)
	}

	// 验证JSON不为空
	if balanceJSON.IsNull() {
		t.Error("转换后的JSON为空")
	}

	// 转换回map验证数据
	resultMap, err := balanceJSON.ToMap()
	if err != nil {
		t.Fatalf("从JSON转换回map失败: %v", err)
	}

	if resultMap["total_amount"].(float64) != balance.TotalAmount {
		t.Errorf("TotalAmount JSON转换错误: 期望 %.2f, 实际 %.2f",
			balance.TotalAmount, resultMap["total_amount"].(float64))
	}

	fmt.Printf("用户余额JSON转换测试通过: %s\n", balanceJSON.String())
}

// TestEmptyUserBalance 测试空用户余额处理
func TestEmptyUserBalance(t *testing.T) {
	// 测试空余额的JSON转换
	emptyBalance := &userentity.UserBalance{
		TotalAmount:    0,
		PendingAmount:  0,
		ApprovedAmount: 0,
		PaidAmount:     0,
	}

	balanceMap := map[string]interface{}{
		"total_amount":    emptyBalance.TotalAmount,
		"pending_amount":  emptyBalance.PendingAmount,
		"paid_amount":     emptyBalance.PaidAmount,
		"approved_amount": emptyBalance.ApprovedAmount,
	}

	balanceJSON, err := postgres.FromMap(balanceMap)
	if err != nil {
		t.Fatalf("转换空用户余额为JSON失败: %v", err)
	}

	fmt.Printf("空用户余额JSON转换测试通过: %s\n", balanceJSON.String())
}
