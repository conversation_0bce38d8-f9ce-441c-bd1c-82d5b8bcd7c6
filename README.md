

    location / {
      try_files $uri $uri/ /index.html;
    }


# Cashback Any

A cashback service that allows users to earn cashback from their purchases at partner merchants.

## Features

- User registration and authentication
- Merchant management
- Order tracking and cashback calculation
- Withdrawal management
- Secure tracking links

## Prerequisites

- Go 1.21 or higher
- PostgreSQL 14 or higher
- Redis 6 or higher
- Make

## Configuration

Copy `config/config.yaml` and modify the values according to your environment:

```yaml
api:
  port: 8080

track:
  port: 8081

postgres:
  host: localhost
  port: 5432
  user: postgres
  password: postgres
  dbname: cashbackany
  sslmode: disable
  timezone: Asia/Shanghai

redis:
  host: localhost
  port: 6379
  password: ""
  db: 0

logger:
  level: debug
  encoding: json
  output_paths:
    - stdout
    - logs/app.log
  error_output_paths:
    - stderr
    - logs/error.log

security:
  jwt_secret: your-secret-key
  token_expiry: 24h
```

## Running the Services

1. First, ensure you have PostgreSQL and Redis running.

2. Create the database:
   ```bash
   createdb cashbackany
   ```

3. Run database migrations:
   ```bash
   make migrate
   ```

4. Start the API service:
   ```bash
   make run-api
   ```

5. Start the Track service:
   ```bash
   make run-track
   ```

## API Documentation

### Authentication

All authenticated endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <token>
```

### Public Endpoints

#### Register User
```
POST /api/v1/users/register
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

#### Login
```
POST /api/v1/users/login
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

#### List Merchants
```
GET /api/v1/merchants?page=1&page_size=10
```

#### Get Merchant
```
GET /api/v1/merchants/:id
```

### Authenticated Endpoints

#### Get User Profile
```
GET /api/v1/users/me
```

#### Update User Profile
```
PUT /api/v1/users/me
{
    "password": "newpassword123"
}
```

#### Get User Balance
```
GET /api/v1/users/me/balance
```

#### Create Order
```
POST /api/v1/orders
{
    "merchant_id": 1,
    "order_number": "ORDER123",
    "amount": 100.00,
    "cashback_amount": 5.00
}
```

#### List Orders
```
GET /api/v1/orders?page=1&page_size=10&status=pending
```

#### Get Order
```
GET /api/v1/orders/:id
```

#### Create Withdrawal
```
POST /api/v1/withdrawals
{
    "amount": 50.00
}
```

#### List Withdrawals
```
GET /api/v1/withdrawals?page=1&page_size=10&status=pending
```

#### Get Withdrawal
```
GET /api/v1/withdrawals/:id
```

### Track Service

#### Track Link
```
GET /t/:merchant_id?user_id=123
```

This endpoint will redirect to the merchant's website with the necessary tracking parameters.
