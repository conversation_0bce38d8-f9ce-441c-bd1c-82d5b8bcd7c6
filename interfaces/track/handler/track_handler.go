package handler

import (
	"bonusearned/application/track/appservice"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
	"net/http"
	"net/url"
	"strings"
)

var (
	// 限流器，每秒1000个请求，突发2000
	limiter = rate.NewLimiter(rate.Limit(2000), 4000)
)

// TrackHandler 跟踪处理器
type TrackHandler struct {
	trackApp appservice.TrackAppService
	logger   *zap.Logger
}

// NewTrackHandler 创建跟踪处理器
func NewTrackHandler(trackApp appservice.TrackAppService, logger *zap.Logger) *TrackHandler {
	return &TrackHandler{
		trackApp: trackApp,
		logger:   logger,
	}
}

func (h *TrackHandler) Track(c *gin.Context) {
	// 限流检查
	if !limiter.Allow() {
		h.logger.Warn("Rate limit exceeded")
		c.Status(http.StatusTooManyRequests)
		return
	}

	// 获取参数
	merchantCode := c.Param("merchant_code")
	if merchantCode == "" {
		h.redirectToDefault(c)
		return
	}
	userCode := c.Param("user_code")
	sub1 := c.Query("sub1")

	trackUrl, err := h.trackApp.GetTrackUrl(c, userCode, merchantCode, sub1)
	if err != nil {
		h.redirectToDefault(c)
		return
	}
	// 解析 URL，拆分 base URL 和参数
	parsedUrl, _ := url.Parse(trackUrl)
	baseUrl := parsedUrl.Scheme + "://" + parsedUrl.Host + parsedUrl.Path
	queryParams := parsedUrl.Query()

	// 生成隐藏的表单字段
	var hiddenInputs strings.Builder
	for key, values := range queryParams {
		for _, value := range values {
			hiddenInputs.WriteString(fmt.Sprintf(`<input type="hidden" name="%s" value="%s">`, key, value))
		}
	}

	// 生成 HTML 页面（完全空白）
	htmlContent := fmt.Sprintf(`
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Loading...</title>
            <style>
                body { margin: 0; padding: 0; background: #fff; }
                form { display: none; }
            </style>
        </head>
        <body>
            <form id="redirectForm" action="%s" method="GET">
                %s
            </form>
            <script>
                setTimeout(function() {
                    document.getElementById("redirectForm").submit();
                }, 0);
            </script>
        </body>
        </html>
        `, baseUrl, hiddenInputs.String())

	// 返回 HTML 页面
	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, htmlContent)
}

// redirectToDefault 重定向到默认页面
func (h *TrackHandler) redirectToDefault(c *gin.Context) {
	defaultUrl := "https://bonusearned.com" // 配置默认重定向URL
	c.Redirect(http.StatusFound, defaultUrl)
}
