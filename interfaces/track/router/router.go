package router

import (
	"bonusearned/application/track/appservice"
	"bonusearned/config"
	clickrecordservice "bonusearned/domain/clickrecord/service"
	"bonusearned/domain/merchant/service"
	trackService "bonusearned/domain/track/service"
	userservice "bonusearned/domain/user/service"
	"bonusearned/infra/persistence"
	"bonusearned/interfaces/track/handler"
	"context"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"net/http"
	"time"
)

// Server 跟踪服务器
type Server struct {
	trackApp appservice.TrackAppService
	router   *gin.Engine
	logger   *zap.Logger
	server   *http.Server
}

// NewServer 创建跟踪服务器
func NewServer(cfg *config.Config, db *gorm.DB, redisClient *redis.Client, logger *zap.Logger) *Server {
	gin.SetMode(gin.ReleaseMode)
	router := gin.New()

	// 使用 Recovery 中间件
	router.Use(gin.Recovery())

	// 初始化各层服务
	cashbackRuleRepo := persistence.NewCashbackRulePostgresRepository(db)
	cashbackRuleService := service.NewCashbackRuleService(
		cashbackRuleRepo,
		logger,
		redisClient,
	)
	merchantRepo := persistence.NewMerchantPostgresRepository(db)
	merchantService := service.NewMerchantService(
		merchantRepo,
		cashbackRuleService,
		redisClient,
		cfg,
		logger,
	)
	clickRecordRepo := persistence.NewClickPostgresRepository(db)
	clickRecordService := clickrecordservice.NewClickRecordService(clickRecordRepo, logger)
	userRepo := persistence.NewUserPostgresRepository(db)
	userService := userservice.NewUserService(userRepo, redisClient, cfg, logger)

	trackDomainService := trackService.NewTrackService(merchantService, clickRecordService, userService)
	trackAppService := appservice.NewTrackAppService(trackDomainService)

	s := &Server{
		trackApp: trackAppService,
		router:   router,
		logger:   logger,
	}

	s.setupRoutes()
	return s
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 创建处理器
	trackHandler := handler.NewTrackHandler(s.trackApp, s.logger)

	// 设置路由
	s.router.GET("/t/:merchant_code/:user_code", trackHandler.Track)
	s.router.GET("/t/:merchant_code", trackHandler.Track)

	// 健康检查
	s.router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})
	// 其余的全部返回到主页
	s.router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "https://bonusearned.com/")
	})
}

// Run 运行服务器
func (s *Server) Run(addr string) error {
	s.server = &http.Server{
		Addr:         addr,
		Handler:      s.router,
		ReadTimeout:  5 * time.Second,
		WriteTimeout: 10 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	return s.server.ListenAndServe()
}

// Shutdown 优雅关闭服务器
func (s *Server) Shutdown(ctx context.Context) error {
	if s.server != nil {
		return s.server.Shutdown(ctx)
	}
	return nil
}
