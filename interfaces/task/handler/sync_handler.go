package handler

import (
	"bonusearned/application/task/appservice"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// SyncHandler 同步处理器
type SyncHandler struct {
	syncApp appservice.SyncApp
	redis   *redis.Client
	logger  *zap.Logger
}

// NewSyncHandler 创建同步处理器
func NewSyncHandler(syncApp appservice.SyncApp, redis *redis.Client, logger *zap.Logger) *SyncHandler {
	return &SyncHandler{
		syncApp: syncApp,
		redis:   redis,
		logger:  logger,
	}
}

// SyncMerchant godoc
// @Summary 同步商家信息
// @Description 从上级联盟同步商家信息
// @Tags task
// @Accept json
// @Produce json
// @Success 200 {object} appservice.SyncResult
// @Router /task/merchant [post]
func (h *SyncHandler) SyncMerchant(c *gin.Context) {
	return
}

// SyncOrder godoc
// @Summary 同步订单信息
// @Description 从上级联盟同步订单信息
// @Tags task
// @Accept json
// @Produce json
// @Success 200 {object} appservice.SyncResult
// @Router /task/order [post]
func (h *SyncHandler) SyncOrder(c *gin.Context) {
	return
}
