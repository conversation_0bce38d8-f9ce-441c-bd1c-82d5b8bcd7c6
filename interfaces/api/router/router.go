package router

import (
	"bonusearned/config"
	"bonusearned/interfaces/api/handler/blog"
	"bonusearned/interfaces/api/handler/clickrecord"
	"bonusearned/interfaces/api/handler/coupon"
	"bonusearned/interfaces/api/handler/merchant"
	"bonusearned/interfaces/api/handler/order"
	"bonusearned/interfaces/api/handler/seo"
	"bonusearned/interfaces/api/handler/user"
	"bonusearned/interfaces/api/handler/withdrawal"
	"bonusearned/interfaces/api/middleware"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// ApiServer HTTP服务器
type ApiServer struct {
	blogHandler        *blog.BlogHandler
	clickRecordHandler *clickrecord.ClickRecordHandler
	categoryHandler    *merchant.CategoryHandler
	countryHandler     *merchant.CountryHandler
	couponHandler      *coupon.CouponHandler
	merchantHandler    *merchant.Handler
	orderHandler       *order.OrderHandler
	cfg                *config.Config
	redis              *redis.Client
	withdrawalHandler  *withdrawal.WithdrawalHandler
	userHandler        *user.UserHandler
	seoHandler         *seo.SEOHandler
}

// NewApiServer 创建API服务器
func NewApiServer(
	blogHandler *blog.BlogHandler,
	clickRecordHandler *clickrecord.ClickRecordHandler,
	categoryHandler *merchant.CategoryHandler,
	countryHandler *merchant.CountryHandler,
	couponHandler *coupon.CouponHandler,
	merchantHandler *merchant.Handler,
	orderHandler *order.OrderHandler,
	cfg *config.Config,
	redis *redis.Client,
	withdrawalHandler *withdrawal.WithdrawalHandler,
	userHandler *user.UserHandler,
	seoHandler *seo.SEOHandler,
) *ApiServer {
	return &ApiServer{
		blogHandler:        blogHandler,
		clickRecordHandler: clickRecordHandler,
		categoryHandler:    categoryHandler,
		countryHandler:     countryHandler,
		merchantHandler:    merchantHandler,
		orderHandler:       orderHandler,
		cfg:                cfg,
		redis:              redis,
		couponHandler:      couponHandler,
		withdrawalHandler:  withdrawalHandler,
		userHandler:        userHandler,
		seoHandler:         seoHandler,
	}
}

// setupPublicRoutes 设置公开路由
func (s *ApiServer) setupPublicRoutes(router *gin.Engine) {
	// 公共中间件
	router.Use(middleware.CORS())

	// SEO相关路由
	router.GET("/sitemap.xml", s.seoHandler.GenerateSitemap)                      // 站点地图索引
	router.GET("/sitemap_stores_:page.xml", s.seoHandler.GenerateMerchantSitemap) // 商家站点地图
	router.GET("/sitemap_blogs_:page.xml", s.seoHandler.GenerateBlogSitemap)      // 博客站点地图
	router.GET("/robots.txt", s.seoHandler.GenerateRobots)                        // 爬虫规则

	// api v1 路径
	v1 := router.Group("/api/v1")

	// 分类相关（公开访问）
	v1.GET("/categories", s.categoryHandler.GetCategoryList)

	// 国家相关（公开访问）
	v1.GET("/countries", s.countryHandler.GetCountryList)

	authRoute := v1.Group("/auth")
	{
		authRoute.POST("/register", s.userHandler.Register)
		authRoute.POST("/login", s.userHandler.Login)
	}

}

// setupOptionalAuthRoutes 设置可选认证路由
func (s *ApiServer) setupOptionalAuthRoutes(router *gin.Engine) {
	router.Use(middleware.CORS())
	v1 := router.Group("/api/v1")

	// 博客相关（可选认证）
	blogRoute := v1.Group("/blogs")
	blogRoute.Use(middleware.OptionalAuth())
	{
		blogRoute.GET("", s.blogHandler.GettBlogList)
		blogRoute.GET("/:id", s.blogHandler.GetBlogById)
	}

	// 优惠券相关（可选认证）
	couponRoute := v1.Group("/coupons")
	couponRoute.Use(middleware.OptionalAuth())
	{
		couponRoute.GET("", s.couponHandler.GetCouponList)
	}

	// 商家相关（可选认证）
	merchantRoute := v1.Group("/merchants")
	merchantRoute.Use(middleware.OptionalAuth())
	{
		merchantRoute.GET("", s.merchantHandler.GetMerchantList)
		merchantRoute.GET("/:unique_name", s.merchantHandler.GetMerchantDetailByUniqueName)
	}
}

// setupAuthRoutes 设置需要认证的路由
func (s *ApiServer) setupAuthRoutes(router *gin.Engine) {
	router.Use(middleware.CORS())
	v1 := router.Group("/api/v1")
	v1.Use(middleware.Auth())

	// 订单相关（需要认证）
	orderRoute := v1.Group("/orders")
	{
		orderRoute.GET("", s.orderHandler.GetOrderList)
	}

	withdrawalRoute := v1.Group("/withdrawals")
	{
		withdrawalRoute.GET("", s.withdrawalHandler.GetWithdrawalList)
	}

	userRoute := v1.Group("/users")
	{
		userRoute.GET("/me/profile", s.userHandler.GetProfile)
		userRoute.POST("/me/update-profile", s.userHandler.UpdateProfile)
		userRoute.POST("/me/update-payment-info", s.userHandler.UpdatePaymentInfo)
		userRoute.POST("/me/update-password", s.userHandler.UpdatePassword)
	}

	authRoute := v1.Group("/auth")
	{
		authRoute.POST("/logout", s.userHandler.Logout)
	}

	clickRecordRoute := v1.Group("/click-record")
	{
		clickRecordRoute.GET("", s.clickRecordHandler.GetClickRecordListByUser)
	}
}

// setupAdminRoutes 设置管理员路由
func (s *ApiServer) setupAdminRoutes(router *gin.Engine) {
	admin := router.Group("/api/v1/admin")
	admin.Use(middleware.Auth())
	// TODO: 添加管理员路由, 后续再开发
}

// Setup 设置路由
func (s *ApiServer) Setup(router *gin.Engine) {
	// 初始化认证
	middleware.InitAuth(s.cfg.Security.JWTSecret)

	// 设置各类路由
	s.setupPublicRoutes(router)
	s.setupOptionalAuthRoutes(router)
	s.setupAuthRoutes(router)
	s.setupAdminRoutes(router)
}
