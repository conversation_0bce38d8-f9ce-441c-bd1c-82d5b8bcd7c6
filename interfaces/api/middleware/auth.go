package middleware

import (
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"strings"
	"time"
)

// UserInfo 用户信息
type UserInfo struct {
	ID       uint64 `json:"id"`
	UserCode string `json:"user_code"`
	Email    string `json:"email"`
}

// Claims 自定义的JWT声明
type Claims struct {
	UserInfo
	jwt.RegisteredClaims
}

var secretKey []byte

// InitAuth 初始化认证中间件
func InitAuth(key string) {
	secretKey = []byte(key)
}

// generateToken 生成JWT token
func generateToken(user *UserInfo) (string, *ecode.Error) {
	claims := Claims{
		UserInfo: *user,
		RegisteredClaims: jwt.RegisteredClaims{
			IssuedAt: jwt.NewNumericDate(time.Now()),
			// 不设置过期时间，使token永久有效
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	signedString, err := token.SignedString(secretKey)
	if err != nil {
		return "", ecode.ErrInternalServer
	}

	return signedString, nil
}

// parseToken 解析JWT token
func parseToken(tokenStr string) (*UserInfo, *ecode.Error) {
	token, err := jwt.ParseWithClaims(tokenStr, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, ecode.ErrInvalidToken
		}
		return secretKey, nil
	})

	if err != nil {
		return nil, ecode.ErrInvalidToken
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return &claims.UserInfo, nil
	}

	return nil, ecode.ErrInvalidToken
}

// GetUser 从上下文中获取用户信息
func GetUser(c *gin.Context) (*UserInfo, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}
	userInfo, ok := user.(*UserInfo)
	return userInfo, ok
}

// GetUserID 从上下文中获取用户ID
func GetUserID(c *gin.Context) uint64 {
	userId := c.GetUint64("user_id")
	if userId <= 0 {
		return 0
	}
	return userId
}

// Auth 认证中间件
func Auth() gin.HandlerFunc {
	return func(c *gin.Context) {
		header := c.GetHeader("Authorization")
		if header == "" || !strings.HasPrefix(header, "Bearer ") {
			c.AbortWithStatusJSON(401, ecode.ErrUnauthorized)
			return
		}

		tokenStr := header[7:]
		user, err := parseToken(tokenStr)
		if err != nil {
			c.AbortWithStatusJSON(401, ecode.ErrUnauthorized)
			return
		}

		c.Set("user", user)
		c.Set("user_id", user.ID)
		c.Next()
	}
}

// OptionalAuth 可选的认证中间件
func OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		header := c.GetHeader("Authorization")
		if header == "" || !strings.HasPrefix(header, "Bearer ") {
			c.Next()
			return
		}

		tokenStr := header[7:]
		user, err := parseToken(tokenStr)
		if err != nil {
			c.Next()
			return
		}

		c.Set("user", user)
		c.Set("user_id", user.ID)
		c.Next()
	}
}

// LoginResponse 登录响应
type LoginResponse struct {
	User  *UserInfo `json:"user"`
	Token string    `json:"token"`
}

// GenerateLoginResponse 生成登录响应
func GenerateLoginResponse(id uint64, userCode, email string) (*LoginResponse, *ecode.Error) {
	user := &UserInfo{
		ID:       id,
		UserCode: userCode,
		Email:    email,
	}

	token, err := generateToken(user)
	if err != nil {
		return nil, err
	}

	return &LoginResponse{
		User:  user,
		Token: token,
	}, nil
}
