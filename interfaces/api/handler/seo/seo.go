package seo

import (
	blogappservice "bonusearned/application/blog/appservice"
	blogdto "bonusearned/application/blog/dto"
	merchantappservice "bonusearned/application/merchant/appservice"
	"bonusearned/application/merchant/dto"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// SEOHandler SEO处理器
type SEOHandler struct {
	merchantApp merchantappservice.MerchantAppService
	blogApp     blogappservice.BlogAppService
	logger      *zap.Logger
}

// NewSEOHandler 创建SEO处理器
func NewSEOHandler(
	merchantApp merchantappservice.MerchantAppService,
	blogApp blogappservice.BlogAppService,
	logger *zap.Logger,
) *SEOHandler {
	return &SEOHandler{
		merchantApp: merchantApp,
		blogApp:     blogApp,
		logger:      logger,
	}
}

// GenerateSitemap 生成站点地图
func (h *SEOHandler) GenerateSitemap(c *gin.Context) {
	// 重定向到sitemap索引
	h.GenerateSitemapIndex(c)
}

// GenerateSitemapIndex 生成站点地图索引
func (h *SEOHandler) GenerateSitemapIndex(c *gin.Context) {
	// 获取商家总数
	merchantCount, err := h.merchantApp.GetMerchantCount(c)
	if err != nil {
		h.logger.Error("failed to get merchant count", zap.Error(err))
		c.String(http.StatusInternalServerError, "Internal Server Error")
		return
	}

	// 获取博客总数
	blogCount, err := h.blogApp.GetBlogCount(c)
	if err != nil {
		h.logger.Error("failed to get blog count", zap.Error(err))
		c.String(http.StatusInternalServerError, "Internal Server Error")
		return
	}

	// 计算需要的sitemap文件数量
	merchantPages := (merchantCount + 9999) / 10000
	blogPages := (blogCount + 9999) / 10000

	// 设置HTTP头为XML
	c.Header("Content-Type", "application/xml; charset=UTF-8")

	// 生成sitemap索引XML
	// 使用strings.Builder来构建XML，以避免多次调用Write
	var builder strings.Builder
	builder.WriteString(`<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
`)

	// 添加商家sitemap
	for i := 0; i < int(merchantPages); i++ {
		builder.WriteString(`<sitemap>
<loc>https://bonusearned.com/sitemap_stores_` + strconv.Itoa(i+1) + `.xml</loc>
<lastmod>` + time.Now().Format("2006-01-02") + `</lastmod>
</sitemap>
`)
	}

	// 添加博客sitemap
	for i := 0; i < int(blogPages); i++ {
		builder.WriteString(`<sitemap>
<loc>https://bonusearned.com/sitemap_blogs_` + strconv.Itoa(i+1) + `.xml</loc>
<lastmod>` + time.Now().Format("2006-01-02") + `</lastmod>
</sitemap>
`)
	}

	builder.WriteString(`</sitemapindex>`)
	c.String(http.StatusOK, builder.String())
}

// GenerateMerchantSitemap 生成商家站点地图
func (h *SEOHandler) GenerateMerchantSitemap(c *gin.Context) {
	page, _ := strconv.Atoi(c.Param("page"))
	if page < 1 {
		page = 1
	}

	// 获取指定页的商家
	merchantReq := dto.GetMerchantListReq{}
	merchantReq.Page = page
	merchantReq.PageSize = 10000
	merchants, err := h.merchantApp.GetMerchantList(c, &merchantReq, 0, "")
	if err != nil {
		h.logger.Error("failed to get merchant list", zap.Error(err))
		c.String(http.StatusInternalServerError, "Internal Server Error")
		return
	}

	// 生成sitemap XML
	c.Header("Content-Type", "application/xml; charset=UTF-8")

	// 使用strings.Builder来构建XML
	var builder strings.Builder
	builder.WriteString(`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
`)

	// 添加商家URL
	for _, merchant := range merchants.MerchantList {
		// 使用辅助函数确保UniqueName不包含特殊字符
		sanitizedUniqueName := sanitizeXMLText(merchant.UniqueName)

		builder.WriteString(`<url>
<loc>https://bonusearned.com/stores/` + sanitizedUniqueName + `</loc>
<lastmod>` + time.Now().Format("2006-01-02") + `</lastmod>
<changefreq>daily</changefreq>
<priority>0.8</priority>
</url>
`)
	}

	builder.WriteString(`</urlset>`)
	c.String(http.StatusOK, builder.String())
}

// GenerateBlogSitemap 生成博客站点地图
func (h *SEOHandler) GenerateBlogSitemap(c *gin.Context) {
	page, _ := strconv.Atoi(c.Param("page"))
	if page < 1 {
		page = 1
	}

	// 获取指定页的博客
	blogsReq := blogdto.GetBlogListReq{}
	blogsReq.Page = page
	blogsReq.PageSize = 10000
	blogs, err := h.blogApp.GetBlogListByCondition(c, &blogsReq, 0, "")
	if err != nil {
		h.logger.Error("failed to get blog list", zap.Error(err))
		c.String(http.StatusInternalServerError, "Internal Server Error")
		return
	}

	// 生成sitemap XML
	c.Header("Content-Type", "application/xml; charset=UTF-8")

	// 使用strings.Builder来构建XML
	var builder strings.Builder
	builder.WriteString(`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
`)

	// 添加博客URL
	for _, blog := range blogs.BlogList {
		blogID := strconv.FormatUint(blog.ID, 10)
		lastmod := blog.PublishedAt.Format("2006-01-02")

		// 确保博客ID不包含特殊字符
		sanitizedBlogID := sanitizeXMLText(blogID)

		builder.WriteString(`<url>
<loc>https://bonusearned.com/blog/` + sanitizedBlogID + `</loc>
<lastmod>` + lastmod + `</lastmod>
<changefreq>daily</changefreq>
<priority>0.9</priority>
</url>
`)
	}

	builder.WriteString(`</urlset>`)
	c.String(http.StatusOK, builder.String())
}

// GenerateRobots 生成爬虫规则
func (h *SEOHandler) GenerateRobots(c *gin.Context) {
	c.Header("Content-Type", "text/plain")
	c.String(http.StatusOK, `User-agent: *
Allow: /
Sitemap: https://bonusearned.com/sitemap.xml`)
}

// 安全处理XML文本的辅助函数
func sanitizeXMLText(text string) string {
	// 按照XML规范转义所有特殊字符
	text = strings.Replace(text, "&", "&amp;", -1)
	text = strings.Replace(text, "<", "&lt;", -1)
	text = strings.Replace(text, ">", "&gt;", -1)
	text = strings.Replace(text, "\"", "&quot;", -1)
	text = strings.Replace(text, "'", "&apos;", -1)

	// 移除任何其他可能导致XML解析错误的字符
	return strings.Map(func(r rune) rune {
		if r >= 32 && r != 127 || r == '\n' || r == '\r' || r == '\t' {
			return r
		}
		return -1
	}, text)
}
