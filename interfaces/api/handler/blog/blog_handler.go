package blog

import (
	"bonusearned/application/blog/appservice"
	"bonusearned/application/blog/dto"
	"bonusearned/infra/constant"
	"bonusearned/infra/ecode"
	"bonusearned/infra/response"
	"bonusearned/interfaces/api/middleware"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)

type BlogHandler struct {
	blogApp appservice.BlogAppService
	logger  *zap.Logger
}

func NewBlogHandler(blogApp appservice.BlogAppService, logger *zap.Logger) *BlogHandler {
	return &BlogHandler{
		blogApp: blogApp,
		logger:  logger,
	}
}

// GetBlogById 获取博客详情
// @Summary 获取博客详情
// @Description 获取指定博客的详细信息
// @Tags 博客管理
// @Accept json
// @Produce json
// @Param id path int true "博客ID"
// @Success 200 {object} dto.BlogDetailResp
// @Failure 400 {object} ecode.Error
// @Router /api/v1/blogs/{id} [get]
func (h *<PERSON><PERSON><PERSON>and<PERSON>) GetBlogById(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	userCode := c.GetHeader("X-User-Code")
	userID := middleware.GetUserID(c)

	blog, errc := h.blogApp.GetBlogDetailById(c, id, userID, userCode)
	if errc != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, blog)
	return
}

// GettBlogList 获取博客列表
// @Summary 获取博客列表
// @Description 获取博客文章列表，支持分页和搜索
// @Tags 博客管理
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认30"
// @Param category query string false "分类"
// @Param search query string false "搜索关键词"
// @Success 200 {object} dto.BlogListResp
// @Failure 400 {object} ecode.Error
// @Router /api/v1/blogs [get]
func (h *BlogHandler) GettBlogList(c *gin.Context) {
	var req dto.GetBlogListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	userCode := c.GetHeader("X-User-Code")
	userID := middleware.GetUserID(c)
	// 设置默认值和参数验证
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}
	resp, err := h.blogApp.GetBlogListByCondition(c, &req, userID, userCode)
	if err != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, resp)
	return
}
