package coupon

import (
	"bonusearned/application/coupon/appservice"
	"bonusearned/application/coupon/dto"
	"bonusearned/infra/constant"
	"bonusearned/infra/ecode"
	"bonusearned/infra/response"
	"bonusearned/interfaces/api/middleware"
	"github.com/gin-gonic/gin"
)

// CouponHandler 优惠券处理器
type CouponHandler struct {
	couponApp appservice.CouponAppService
}

// NewCouponHandler 创建优惠券处理器
func NewCouponHandler(couponApp appservice.CouponAppService) *CouponHandler {
	return &CouponHandler{
		couponApp: couponApp,
	}
}

func (h *CouponHandler) GetCouponList(c *gin.Context) {
	var req dto.GetCouponListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	userCode := c.GetHeader("X-User-Code")
	userID := middleware.GetUserID(c)
	// 设置默认值和参数验证
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}

	resp, err := h.couponApp.GetCouponList(c, &req, userID, userCode)
	if err != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, resp)
	return
}
