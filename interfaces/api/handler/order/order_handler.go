package order

import (
	"bonusearned/application/order/appservice"
	"bonusearned/application/order/dto"
	"bonusearned/infra/constant"
	"bonusearned/infra/ecode"
	"bonusearned/infra/response"
	"bonusearned/interfaces/api/middleware"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// OrderHandler 订单处理器
type OrderHandler struct {
	orderApp appservice.OrderAppService
	logger   *zap.Logger
}

// NewOrderHandler 创建订单处理器
func NewOrderHandler(orderApp appservice.OrderAppService, logger *zap.Logger) *OrderHandler {
	return &OrderHandler{
		orderApp: orderApp,
		logger:   logger,
	}
}

func (h *OrderHandler) GetOrderList(c *gin.Context) {
	// 获取用户ID
	userID := middleware.GetUserID(c)
	if userID <= 0 {
		response.Error(c, ecode.ErrUnauthorized.Code, ecode.ErrUnauthorized.Message)
		return
	}
	userCode := c.GetHeader("X-User-Code")
	var req dto.GetOrderListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}

	// 设置默认值和参数验证
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}

	resp, err := h.orderApp.GetOrderList(c, userID, &req, userCode)
	if err != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, resp)
	return
}
