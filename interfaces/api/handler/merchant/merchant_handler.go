package merchant

import (
	"bonusearned/application/merchant/appservice"
	"bonusearned/application/merchant/dto"
	"bonusearned/infra/constant"
	"bonusearned/infra/ecode"
	"bonusearned/infra/response"
	"bonusearned/interfaces/api/middleware"
	"github.com/gin-gonic/gin"
)

// Handler 商家处理器
type Handler struct {
	merchantAppService appservice.MerchantAppService
}

// NewHandler 创建商家处理器
func NewHandler(merchantAppService appservice.MerchantAppService) *Handler {
	return &Handler{
		merchantAppService: merchantAppService,
	}
}

// GetMerchantList 获取商家列表
// @Summary 获取商家列表
// @Description 获取所有商家的列表，支持分页和搜索
// @Tags 商家查询
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Param keyword query string false "搜索关键词"
// @Param category query string false "商家分类"
// @Success 200 {object} dto.GetMerchantListResp
// @Failure 400 {object} ecode.Error
// @Router /api/v1/merchants [get]
func (h *Handler) GetMerchantList(c *gin.Context) {
	req := new(dto.GetMerchantListReq)
	if err := c.ShouldBindQuery(req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	userCode := c.GetHeader("X-User-Code")
	userID := middleware.GetUserID(c)

	// 设置默认值和参数验证
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}

	result, err := h.merchantAppService.GetMerchantList(c, req, userID, userCode)
	if err != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, result)
	return
}

// GetMerchantDetailByUniqueName 获取商家详情
// @Summary 获取商家详情
// @Description 根据商家唯一名称获取商家详细信息
// @Tags 商家查询
// @Accept json
// @Produce json
// @Param unique_name path string true "商家唯一名称"
// @Success 200 {object} dto.MerchantDetailResp
// @Failure 400 {object} ecode.Error
// @Failure 404 {object} ecode.Error
// @Router /api/v1/merchants/{unique_name} [get]
func (h *Handler) GetMerchantDetailByUniqueName(c *gin.Context) {
	uniqueName := c.Param("unique_name")
	if uniqueName == "" {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}

	userCode := c.GetHeader("X-User-Code")
	userID := middleware.GetUserID(c)

	merchant, err := h.merchantAppService.GetMerchantDetailByUniqueName(c, uniqueName, userID, userCode)
	if err != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, merchant)
	return
}
