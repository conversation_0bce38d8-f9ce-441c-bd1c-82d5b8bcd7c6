package merchant

import (
	"bonusearned/application/merchant/appservice"
	"bonusearned/application/merchant/dto"
	"bonusearned/infra/constant"
	"bonusearned/infra/ecode"
	"bonusearned/infra/response"
	"github.com/gin-gonic/gin"
)

// CategoryHandler 商家分类处理器
type CategoryHandler struct {
	categoryApp appservice.CategoryAppService
}

// NewCategoryHandler 创建商家分类处理器
func NewCategoryHandler(categoryApp appservice.CategoryAppService) *CategoryHandler {
	return &CategoryHandler{
		categoryApp: categoryApp,
	}
}

// GetCategoryList godoc
// @Summary 获取商家分类列表
// @Description 获取商家分类列表
// @Tags categories
// @Accept json
// @Produce json
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Success 200 {object} dto.CategoryListResponse
// @Router /api/v1/categories [get]
func (h *CategoryHandler) GetCategoryList(c *gin.Context) {
	var req dto.GetCategoryListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	// 设置默认值和参数验证
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}

	categories, err := h.categoryApp.GetCategoryList(c, &req)
	if err != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, categories)
	return
}
