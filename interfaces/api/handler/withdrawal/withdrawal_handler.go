package withdrawal

import (
	"bonusearned/application/withdrawal/appservice"
	"bonusearned/application/withdrawal/dto"
	"bonusearned/infra/constant"
	"bonusearned/infra/ecode"
	"bonusearned/infra/response"
	"bonusearned/interfaces/api/middleware"
	"github.com/gin-gonic/gin"
)

type WithdrawalHandler struct {
	withdrawalApp appservice.WithdrawalAppService
}

func NewWithdrawalHandler(withdrawalApp appservice.WithdrawalAppService) *WithdrawalHandler {
	return &WithdrawalHandler{
		withdrawalApp: withdrawalApp,
	}
}

func (h *WithdrawalHandler) GetWithdrawalList(c *gin.Context) {
	// 获取用户ID
	userID := middleware.GetUserID(c)
	if userID <= 0 {
		response.Error(c, ecode.ErrUnauthorized.Code, ecode.ErrUnauthorized.Message)
		return
	}

	var req dto.GetWithdrawalListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	// 设置默认值和参数验证
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}
	// 获取提现列表
	resp, err := h.withdrawalApp.GetWithdrawalList(c, userID, &req)
	if err != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}
	response.Success(c, resp)
	return
}
