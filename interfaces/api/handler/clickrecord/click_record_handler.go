package clickrecord

import (
	"bonusearned/application/clickrecord/appservice"
	"bonusearned/application/clickrecord/dto"
	"bonusearned/infra/constant"
	"bonusearned/infra/ecode"
	"bonusearned/infra/response"
	"bonusearned/interfaces/api/middleware"
	"github.com/gin-gonic/gin"
)

// ClickRecordHandler  点击记录处理器
type ClickRecordHandler struct {
	clickRecordApp appservice.ClickRecordAppService
}

// NewClickHandler 创建点击记录处理器
func NewClickHandler(clickRecordApp appservice.ClickRecordAppService) *ClickRecordHandler {
	return &ClickRecordHandler{
		clickRecordApp: clickRecordApp,
	}
}

func (h *ClickRecordHandler) GetClickRecordListByUser(c *gin.Context) {
	// 获取用户ID
	userId := middleware.GetUserID(c)
	if userId <= 0 {
		response.Error(c, ecode.ErrUnauthorized.Code, ecode.ErrUnauthorized.Message)
	}
	userCode := c.GetHeader("X-User-Code")
	var req dto.GetClickRecordListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	// 设置默认值和参数验证
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}

	records, err := h.clickRecordApp.GetClickRecordList(c, userId, &req, userCode)
	if err != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, records)
	return
}
