package user

import (
	"bonusearned/application/user/appservice"
	"bonusearned/application/user/dto"
	"bonusearned/infra/ecode"
	"bonusearned/infra/response"
	"bonusearned/interfaces/api/middleware"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// UserHandler 用户处理器
type UserHandler struct {
	userApp appservice.UserApp
	logger  *zap.Logger
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userApp appservice.UserApp, logger *zap.Logger) *UserHandler {
	return &UserHandler{
		userApp: userApp,
		logger:  logger,
	}
}

func (h *UserHandler) Register(c *gin.Context) {
	var req dto.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	userInfo, err := h.userApp.Register(c, &req)
	if err != nil {
		response.Error(c, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
		return
	}

	response.Success(c, userInfo)
	return
}

func (h *UserHandler) Login(c *gin.Context) {
	var req dto.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}

	resp, err := h.userApp.Login(c, &req)
	if err != nil {
		response.Error(c, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
		return
	}

	response.Success(c, resp)
	return
}

func (h *UserHandler) GetProfile(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID <= 0 {
		response.Error(c, ecode.ErrUnauthorized.Code, ecode.ErrUnauthorized.Message)
		return
	}

	resp, err := h.userApp.GetProfile(c, userID)
	if err != nil {
		response.Error(c, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
		return
	}
	response.Success(c, resp)
	return
}

func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID <= 0 {
		response.Error(c, ecode.ErrUnauthorized.Code, ecode.ErrUnauthorized.Message)
		return
	}

	var req dto.UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}

	if err := h.userApp.UpdateProfile(c, userID, &req); err != nil {
		response.Error(c, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
		return
	}

	response.Success(c, nil)
	return
}

func (h *UserHandler) UpdatePaymentInfo(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID <= 0 {
		response.Error(c, ecode.ErrUnauthorized.Code, ecode.ErrUnauthorized.Message)
		return
	}

	var req dto.UpdatePaymentInfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}

	if err := h.userApp.UpdatePaymentInfo(c, userID, &req); err != nil {
		response.Error(c, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
		return
	}

	response.Success(c, nil)
	return
}

func (h *UserHandler) UpdatePassword(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID <= 0 {
		response.Error(c, ecode.ErrUnauthorized.Code, ecode.ErrUnauthorized.Message)
		return
	}

	var req dto.UpdatePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}

	if err := h.userApp.UpdatePassword(c, userID, &req); err != nil {
		response.Error(c, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
		return
	}

	response.Success(c, nil)
	return
}

func (h *UserHandler) Logout(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID <= 0 {
		response.Error(c, ecode.ErrUnauthorized.Code, ecode.ErrUnauthorized.Message)
		return
	}

	if err := h.userApp.Logout(c, userID); err != nil {
		response.Error(c, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
		return
	}
	response.Success(c, nil)
	return
}
